<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Interactive BOM for KiCAD</title>
  <style type="text/css">
:root {
  --pcb-edge-color: black;
  --pad-color: #878787;
  --pad-hole-color: #CCCCCC;
  --pad-color-highlight: #D04040;
  --pad-color-highlight-both: #D0D040;
  --pad-color-highlight-marked: #44a344;
  --pin1-outline-color: #ffb629;
  --pin1-outline-color-highlight: #ffb629;
  --pin1-outline-color-highlight-both: #fcbb39;
  --pin1-outline-color-highlight-marked: #fdbe41;
  --silkscreen-edge-color: #aa4;
  --silkscreen-polygon-color: #4aa;
  --silkscreen-text-color: #4aa;
  --fabrication-edge-color: #907651;
  --fabrication-polygon-color: #907651;
  --fabrication-text-color: #a27c24;
  --track-color: #def5f1;
  --track-color-highlight: #D04040;
  --zone-color: #def5f1;
  --zone-color-highlight: #d0404080;
}

html,
body {
  margin: 0px;
  height: 100%;
  font-family: Verdana, sans-serif;
}

.dark.topmostdiv {
  --pcb-edge-color: #eee;
  --pad-color: #808080;
  --pin1-outline-color: #ffa800;
  --pin1-outline-color-highlight: #ccff00;
  --track-color: #42524f;
  --zone-color: #42524f;
  background-color: #252c30;
  color: #eee;
}

button {
  background-color: #eee;
  border: 1px solid #888;
  color: black;
  height: 44px;
  width: 44px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 14px;
  font-weight: bolder;
}

.dark button {
  /* This will be inverted */
  background-color: #c3b7b5;
}

button.depressed {
  background-color: #0a0;
  color: white;
}

.dark button.depressed {
  /* This will be inverted */
  background-color: #b3b;
}

button:focus {
  outline: 0;
}

button#tb-btn {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8.47 8.47'%3E%3Crect transform='translate(0 -288.53)' ry='1.17' y='288.8' x='.27' height='7.94' width='7.94' fill='%23f9f9f9'/%3E%3Cg transform='translate(0 -288.53)'%3E%3Crect width='7.94' height='7.94' x='.27' y='288.8' ry='1.17' fill='none' stroke='%23000' stroke-width='.4' stroke-linejoin='round'/%3E%3Cpath d='M1.32 290.12h5.82M1.32 291.45h5.82' fill='none' stroke='%23000' stroke-width='.4'/%3E%3Cpath d='M4.37 292.5v4.23M.26 292.63H8.2' fill='none' stroke='%23000' stroke-width='.3'/%3E%3Ctext font-weight='700' font-size='3.17' font-family='sans-serif'%3E%3Ctspan x='1.35' y='295.73'%3EF%3C/tspan%3E%3Ctspan x='5.03' y='295.68'%3EB%3C/tspan%3E%3C/text%3E%3C/g%3E%3C/svg%3E%0A");
}

button#lr-btn {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8.47 8.47'%3E%3Crect transform='translate(0 -288.53)' ry='1.17' y='288.8' x='.27' height='7.94' width='7.94' fill='%23f9f9f9'/%3E%3Cg transform='translate(0 -288.53)'%3E%3Crect width='7.94' height='7.94' x='.27' y='288.8' ry='1.17' fill='none' stroke='%23000' stroke-width='.4' stroke-linejoin='round'/%3E%3Cpath d='M1.06 290.12H3.7m-2.64 1.33H3.7m-2.64 1.32H3.7m-2.64 1.3H3.7m-2.64 1.33H3.7' fill='none' stroke='%23000' stroke-width='.4'/%3E%3Cpath d='M4.37 288.8v7.94m0-4.11h3.96' fill='none' stroke='%23000' stroke-width='.3'/%3E%3Ctext font-weight='700' font-size='3.17' font-family='sans-serif'%3E%3Ctspan x='5.11' y='291.96'%3EF%3C/tspan%3E%3Ctspan x='5.03' y='295.68'%3EB%3C/tspan%3E%3C/text%3E%3C/g%3E%3C/svg%3E%0A");
}

button#bom-btn {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8.47 8.47'%3E%3Crect transform='translate(0 -288.53)' ry='1.17' y='288.8' x='.27' height='7.94' width='7.94' fill='%23f9f9f9'/%3E%3Cg transform='translate(0 -288.53)' fill='none' stroke='%23000' stroke-width='.4'%3E%3Crect width='7.94' height='7.94' x='.27' y='288.8' ry='1.17' stroke-linejoin='round'/%3E%3Cpath d='M1.59 290.12h5.29M1.59 291.45h5.33M1.59 292.75h5.33M1.59 294.09h5.33M1.59 295.41h5.33'/%3E%3C/g%3E%3C/svg%3E");
}

button#bom-grouped-btn {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32'%3E%3Cg stroke='%23000' stroke-linejoin='round' class='layer'%3E%3Crect width='29' height='29' x='1.5' y='1.5' stroke-width='2' fill='%23fff' rx='5' ry='5'/%3E%3Cpath stroke-linecap='square' stroke-width='2' d='M6 10h4m4 0h5m4 0h3M6.1 22h3m3.9 0h5m4 0h4m-16-8h4m4 0h4'/%3E%3Cpath stroke-linecap='null' d='M5 17.5h22M5 26.6h22M5 5.5h22'/%3E%3C/g%3E%3C/svg%3E");
}

button#bom-ungrouped-btn {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32'%3E%3Cg stroke='%23000' stroke-linejoin='round' class='layer'%3E%3Crect width='29' height='29' x='1.5' y='1.5' stroke-width='2' fill='%23fff' rx='5' ry='5'/%3E%3Cpath stroke-linecap='square' stroke-width='2' d='M6 10h4m-4 8h3m-3 8h4'/%3E%3Cpath stroke-linecap='null' d='M5 13.5h22m-22 8h22M5 5.5h22'/%3E%3C/g%3E%3C/svg%3E");
}

button#bom-netlist-btn {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32'%3E%3Cg fill='none' stroke='%23000' class='layer'%3E%3Crect width='29' height='29' x='1.5' y='1.5' stroke-width='2' fill='%23fff' rx='5' ry='5'/%3E%3Cpath stroke-width='2' d='M6 26l6-6v-8m13.8-6.3l-6 6v8'/%3E%3Ccircle cx='11.8' cy='9.5' r='2.8' stroke-width='2'/%3E%3Ccircle cx='19.8' cy='22.8' r='2.8' stroke-width='2'/%3E%3C/g%3E%3C/svg%3E");
}

button#copy {
  background-image: url("data:image/svg+xml,%3Csvg height='48' viewBox='0 0 48 48' width='48' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 0h48v48h-48z' fill='none'/%3E%3Cpath d='M32 2h-24c-2.21 0-4 1.79-4 4v28h4v-28h24v-4zm6 8h-22c-2.21 0-4 1.79-4 4v28c0 2.21 1.79 4 4 4h22c2.21 0 4-1.79 4-4v-28c0-2.21-1.79-4-4-4zm0 32h-22v-28h22v28z'/%3E%3C/svg%3E");
  background-position: 6px 6px;
  background-repeat: no-repeat;
  background-size: 26px 26px;
  border-radius: 6px;
  height: 40px;
  width: 40px;
  margin: 10px 5px;
}

button#copy:active {
  box-shadow: inset 0px 0px 5px #6c6c6c;
}

textarea.clipboard-temp {
  position: fixed;
  top: 0;
  left: 0;
  width: 2em;
  height: 2em;
  padding: 0;
  border: None;
  outline: None;
  box-shadow: None;
  background: transparent;
}

.left-most-button {
  border-right: 0;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.middle-button {
  border-right: 0;
}

.right-most-button {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.button-container {
  font-size: 0;
  margin: 10px 10px 10px 0px;
}

.dark .button-container {
  filter: invert(1);
}

.button-container button {
  background-size: 32px 32px;
  background-position: 5px 5px;
  background-repeat: no-repeat;
}

@media print {
  .hideonprint {
    display: none;
  }
}

canvas {
  cursor: crosshair;
}

canvas:active {
  cursor: grabbing;
}

.fileinfo {
  width: 100%;
  max-width: 1000px;
  border: none;
  padding: 5px;
}

.fileinfo .title {
  font-size: 20pt;
  font-weight: bold;
}

.fileinfo td {
  overflow: hidden;
  white-space: nowrap;
  max-width: 1px;
  width: 50%;
  text-overflow: ellipsis;
}

.bom {
  border-collapse: collapse;
  font-family: Consolas, "DejaVu Sans Mono", Monaco, monospace;
  font-size: 10pt;
  table-layout: fixed;
  width: 100%;
  margin-top: 1px;
  position: relative;
}

.bom th,
.bom td {
  border: 1px solid black;
  padding: 5px;
  word-wrap: break-word;
  text-align: center;
  position: relative;
}

.dark .bom th,
.dark .bom td {
  border: 1px solid #777;
}

.bom th {
  background-color: #CCCCCC;
  background-clip: padding-box;
}

.dark .bom th {
  background-color: #3b4749;
}

.bom tr.highlighted:nth-child(n) {
  background-color: #cfc;
}

.dark .bom tr.highlighted:nth-child(n) {
  background-color: #226022;
}

.bom tr:nth-child(even) {
  background-color: #f2f2f2;
}

.dark .bom tr:nth-child(even) {
  background-color: #313b40;
}

.bom tr.checked {
  color: #1cb53d;
}

.dark .bom tr.checked {
  color: #2cce54;
}

.bom tr {
  transition: background-color 0.2s;
}

.bom .numCol {
  width: 30px;
}

.bom .value {
  width: 15%;
}

.bom .quantity {
  width: 65px;
}

.bom th .sortmark {
  position: absolute;
  right: 1px;
  top: 1px;
  margin-top: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent #221 transparent;
  transform-origin: 50% 85%;
  transition: opacity 0.2s, transform 0.4s;
}

.dark .bom th .sortmark {
  filter: invert(1);
}

.bom th .sortmark.none {
  opacity: 0;
}

.bom th .sortmark.desc {
  transform: rotate(180deg);
}

.bom th:hover .sortmark.none {
  opacity: 0.5;
}

.bom .bom-checkbox {
  width: 30px;
  position: relative;
  user-select: none;
  -moz-user-select: none;
}

.bom .bom-checkbox:before {
  content: "";
  position: absolute;
  border-width: 15px;
  border-style: solid;
  border-color: #51829f transparent transparent transparent;
  visibility: hidden;
  top: -15px;
}

.bom .bom-checkbox:after {
  content: "Double click to set/unset all";
  position: absolute;
  color: white;
  top: -35px;
  left: -26px;
  background: #51829f;
  padding: 5px 15px;
  border-radius: 8px;
  white-space: nowrap;
  visibility: hidden;
}

.bom .bom-checkbox:hover:before,
.bom .bom-checkbox:hover:after {
  visibility: visible;
  transition: visibility 0.2s linear 1s;
}

.split {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  background-color: inherit;
}

.split.split-horizontal,
.gutter.gutter-horizontal {
  height: 100%;
  float: left;
}

.gutter {
  background-color: #ddd;
  background-repeat: no-repeat;
  background-position: 50%;
  transition: background-color 0.3s;
}

.dark .gutter {
  background-color: #777;
}

.gutter.gutter-horizontal {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==');
  cursor: ew-resize;
  width: 5px;
}

.gutter.gutter-vertical {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAFAQMAAABo7865AAAABlBMVEVHcEzMzMzyAv2sAAAAAXRSTlMAQObYZgAAABBJREFUeF5jOAMEEAIEEFwAn3kMwcB6I2AAAAAASUVORK5CYII=');
  cursor: ns-resize;
  height: 5px;
}

.searchbox {
  float: left;
  height: 40px;
  margin: 10px 5px;
  padding: 12px 32px;
  font-family: Consolas, "DejaVu Sans Mono", Monaco, monospace;
  font-size: 18px;
  box-sizing: border-box;
  border: 1px solid #888;
  border-radius: 6px;
  outline: none;
  background-color: #eee;
  transition: background-color 0.2s, border 0.2s;
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABNklEQVQ4T8XSMUvDQBQH8P/LElFa/AIZHcTBQSz0I/gFstTBRR2KUC4ldDxw7h0Bl3RRUATxi4iiODgoiLNrbQYp5J6cpJJqomkX33Z37/14d/dIa33MzDuYI4johOI4XhyNRteO46zNYjDzAxE1yBZprVeZ+QbAUhXEGJMA2Ox2u4+fQIa0mPmsCgCgJYQ4t7lfgF0opQYAdv9ABkKI/UnOFCClXKjX61cA1osQY8x9kiRNKeV7IWA3oyhaSdP0FkAtjxhj3hzH2RBCPOf3pzqYHCilfAAX+URm9oMguPzeWSGQvUcMYC8rOBJCHBRdqxTo9/vbRHRqi8bj8XKv1xvODbiuW2u32/bvf0SlDv4XYOY7z/Mavu+nM1+BmQ+NMc0wDF/LprP0DbTWW0T00ul0nn4b7Q87+X4Qmfiq2wAAAABJRU5ErkJggg==');
  background-position: 10px 10px;
  background-repeat: no-repeat;
}

.dark .searchbox {
  background-color: #111;
  color: #eee;
}

.searchbox::placeholder {
  color: #ccc;
}

.dark .searchbox::placeholder {
  color: #666;
}

.filter {
  width: calc(60% - 64px);
}

.reflookup {
  width: calc(40% - 10px);
}

input[type=text]:focus {
  background-color: white;
  border: 1px solid #333;
}

.dark input[type=text]:focus {
  background-color: #333;
  border: 1px solid #ccc;
}

mark.highlight {
  background-color: #5050ff;
  color: #fff;
  padding: 2px;
  border-radius: 6px;
}

.dark mark.highlight {
  background-color: #76a6da;
  color: #111;
}

.menubtn {
  background-color: white;
  border: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='36' height='36' viewBox='0 0 20 20'%3E%3Cpath fill='none' d='M0 0h20v20H0V0z'/%3E%3Cpath d='M15.95 10.78c.03-.25.05-.51.05-.78s-.02-.53-.06-.78l1.69-1.32c.15-.12.19-.34.1-.51l-1.6-2.77c-.1-.18-.31-.24-.49-.18l-1.99.8c-.42-.32-.86-.58-1.35-.78L12 2.34c-.03-.2-.2-.34-.4-.34H8.4c-.2 0-.36.14-.39.34l-.3 2.12c-.49.2-.94.47-1.35.78l-1.99-.8c-.18-.07-.39 0-.49.18l-1.6 2.77c-.1.18-.06.39.1.51l1.69 1.32c-.04.25-.07.52-.07.78s.02.53.06.78L2.37 12.1c-.15.12-.19.34-.1.51l1.6 2.77c.1.18.31.24.49.18l1.99-.8c.42.32.86.58 1.35.78l.3 2.12c.04.2.2.34.4.34h3.2c.2 0 .37-.14.39-.34l.3-2.12c.49-.2.94-.47 1.35-.78l1.99.8c.18.07.39 0 .49-.18l1.6-2.77c.1-.18.06-.39-.1-.51l-1.67-1.32zM10 13c-1.65 0-3-1.35-3-3s1.35-3 3-3 3 1.35 3 3-1.35 3-3 3z'/%3E%3C/svg%3E%0A");
  background-position: center;
  background-repeat: no-repeat;
}

.statsbtn {
  background-color: white;
  border: none;
  background-image: url("data:image/svg+xml,%3Csvg width='36' height='36' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4 6h28v24H4V6zm0 8h28v8H4m9-16v24h10V5.8' fill='none' stroke='%23000' stroke-width='2'/%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
}

.iobtn {
  background-color: white;
  border: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='36' height='36'%3E%3Cpath fill='none' stroke='%23000' stroke-width='2' d='M3 33v-7l6.8-7h16.5l6.7 7v7H3zM3.2 26H33M21 9l5-5.9 5 6h-2.5V15h-5V9H21zm-4.9 0l-5 6-5-6h2.5V3h5v6h2.5z'/%3E%3Cpath fill='none' stroke='%23000' d='M6.1 29.5H10'/%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
}

.visbtn {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24'%3E%3Cpath fill='none' stroke='%23333' d='M2.5 4.5h5v15h-5zM9.5 4.5h5v15h-5zM16.5 4.5h5v15h-5z'/%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  padding: 15px;
}

#vismenu-content {
  left: 0px;
  font-family: Verdana, sans-serif;
}

.dark .statsbtn,
.dark .savebtn,
.dark .menubtn,
.dark .iobtn,
.dark .visbtn {
  filter: invert(1);
}

.flexbox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.savebtn {
  background-color: #d6d6d6;
  width: auto;
  height: 30px;
  flex-grow: 1;
  margin: 5px;
  border-radius: 4px;
}

.savebtn:active {
  background-color: #0a0;
  color: white;
}

.dark .savebtn:active {
  /* This will be inverted */
  background-color: #b3b;
}

.stats {
  border-collapse: collapse;
  font-size: 12pt;
  table-layout: fixed;
  width: 100%;
  min-width: 450px;
}

.dark .stats td {
  border: 1px solid #bbb;
}

.stats td {
  border: 1px solid black;
  padding: 5px;
  word-wrap: break-word;
  text-align: center;
  position: relative;
}

#checkbox-stats div {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

#checkbox-stats .bar {
  background-color: rgba(28, 251, 0, 0.6);
}

.menu {
  position: relative;
  display: inline-block;
  margin: 10px 10px 10px 0px;
}

.menu-content {
  font-size: 12pt !important;
  text-align: left !important;
  font-weight: normal !important;
  display: none;
  position: absolute;
  background-color: white;
  right: 0;
  min-width: 300px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 100;
  padding: 8px;
}

.dark .menu-content {
  background-color: #111;
}

.menu:hover .menu-content {
  display: block;
}

.menu:hover .menubtn,
.menu:hover .iobtn,
.menu:hover .statsbtn {
  background-color: #eee;
}

.menu-label {
  display: inline-block;
  padding: 8px;
  border: 1px solid #ccc;
  border-top: 0;
  width: calc(100% - 18px);
}

.menu-label-top {
  border-top: 1px solid #ccc;
}

.menu-textbox {
  float: left;
  height: 24px;
  margin: 10px 5px;
  padding: 5px 5px;
  font-family: Consolas, "DejaVu Sans Mono", Monaco, monospace;
  font-size: 14px;
  box-sizing: border-box;
  border: 1px solid #888;
  border-radius: 4px;
  outline: none;
  background-color: #eee;
  transition: background-color 0.2s, border 0.2s;
  width: calc(100% - 10px);
}

.menu-textbox.invalid,
.dark .menu-textbox.invalid {
  color: red;
}

.dark .menu-textbox {
  background-color: #222;
  color: #eee;
}

.radio-container {
  margin: 4px;
}

.topmostdiv {
  width: 100%;
  height: 100%;
  background-color: white;
  transition: background-color 0.3s;
}

#top {
  height: 78px;
  border-bottom: 2px solid black;
}

.dark #top {
  border-bottom: 2px solid #ccc;
}

#dbg {
  display: block;
}

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #aaa;
}

::-webkit-scrollbar-thumb {
  background: #666;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.slider {
  -webkit-appearance: none;
  width: 100%;
  margin: 3px 0;
  padding: 0;
  outline: none;
  opacity: 0.7;
  -webkit-transition: .2s;
  transition: opacity .2s;
  border-radius: 3px;
}

.slider:hover {
  opacity: 1;
}

.slider:focus {
  outline: none;
}

.slider::-webkit-slider-runnable-track {
  -webkit-appearance: none;
  width: 100%;
  height: 8px;
  background: #d3d3d3;
  border-radius: 3px;
  border: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #0a0;
  cursor: pointer;
  margin-top: -4px;
}

.dark .slider::-webkit-slider-thumb {
  background: #3d3;
}

.slider::-moz-range-thumb {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #0a0;
  cursor: pointer;
}

.slider::-moz-range-track {
  height: 8px;
  background: #d3d3d3;
  border-radius: 3px;
}

.dark .slider::-moz-range-thumb {
  background: #3d3;
}

.slider::-ms-track {
  width: 100%;
  height: 8px;
  border-width: 3px 0;
  background: transparent;
  border-color: transparent;
  color: transparent;
  transition: opacity .2s;
}

.slider::-ms-fill-lower {
  background: #d3d3d3;
  border: none;
  border-radius: 3px;
}

.slider::-ms-fill-upper {
  background: #d3d3d3;
  border: none;
  border-radius: 3px;
}

.slider::-ms-thumb {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #0a0;
  cursor: pointer;
  margin: 0;
}

.shameless-plug {
  font-size: 0.8em;
  text-align: center;
  display: block;
}

a {
  color: #0278a4;
}

.dark a {
  color: #00b9fd;
}

#frontcanvas,
#backcanvas {
  touch-action: none;
}

.placeholder {
  border: 1px dashed #9f9fda !important;
  background-color: #edf2f7 !important;
}

.dragging {
  z-index: 999;
}

.dark .dragging>table>tbody>tr {
  background-color: #252c30;
}

.dark .placeholder {
  filter: invert(1);
}

.column-spacer {
  top: 0;
  left: 0;
  width: calc(100% - 4px);
  position: absolute;
  cursor: pointer;
  user-select: none;
  height: 100%;
}

.column-width-handle {
  top: 0;
  right: 0;
  width: 4px;
  position: absolute;
  cursor: col-resize;
  user-select: none;
  height: 100%;
}

.column-width-handle:hover {
  background-color: #4f99bd;
}

.help-link {
  border: 1px solid #0278a4;
  padding-inline: 0.3rem;
  border-radius: 3px;
  cursor: pointer;
}

.dark .help-link {
  border: 1px solid #00b9fd;
}


  </style>
  <script type="text/javascript" >
///////////////////////////////////////////////
/*
  Split.js - v1.3.5
  MIT License
  https://github.com/nathancahill/Split.js
*/
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.Split=t()}(this,function(){"use strict";var e=window,t=e.document,n="addEventListener",i="removeEventListener",r="getBoundingClientRect",s=function(){return!1},o=e.attachEvent&&!e[n],a=["","-webkit-","-moz-","-o-"].filter(function(e){var n=t.createElement("div");return n.style.cssText="width:"+e+"calc(9px)",!!n.style.length}).shift()+"calc",l=function(e){return"string"==typeof e||e instanceof String?t.querySelector(e):e};return function(u,c){function z(e,t,n){var i=A(y,t,n);Object.keys(i).forEach(function(t){return e.style[t]=i[t]})}function h(e,t){var n=B(y,t);Object.keys(n).forEach(function(t){return e.style[t]=n[t]})}function f(e){var t=E[this.a],n=E[this.b],i=t.size+n.size;t.size=e/this.size*i,n.size=i-e/this.size*i,z(t.element,t.size,this.aGutterSize),z(n.element,n.size,this.bGutterSize)}function m(e){var t;this.dragging&&((t="touches"in e?e.touches[0][b]-this.start:e[b]-this.start)<=E[this.a].minSize+M+this.aGutterSize?t=E[this.a].minSize+this.aGutterSize:t>=this.size-(E[this.b].minSize+M+this.bGutterSize)&&(t=this.size-(E[this.b].minSize+this.bGutterSize)),f.call(this,t),c.onDrag&&c.onDrag())}function g(){var e=E[this.a].element,t=E[this.b].element;this.size=e[r]()[y]+t[r]()[y]+this.aGutterSize+this.bGutterSize,this.start=e[r]()[G]}function d(){var t=this,n=E[t.a].element,r=E[t.b].element;t.dragging&&c.onDragEnd&&c.onDragEnd(),t.dragging=!1,e[i]("mouseup",t.stop),e[i]("touchend",t.stop),e[i]("touchcancel",t.stop),t.parent[i]("mousemove",t.move),t.parent[i]("touchmove",t.move),delete t.stop,delete t.move,n[i]("selectstart",s),n[i]("dragstart",s),r[i]("selectstart",s),r[i]("dragstart",s),n.style.userSelect="",n.style.webkitUserSelect="",n.style.MozUserSelect="",n.style.pointerEvents="",r.style.userSelect="",r.style.webkitUserSelect="",r.style.MozUserSelect="",r.style.pointerEvents="",t.gutter.style.cursor="",t.parent.style.cursor=""}function S(t){var i=this,r=E[i.a].element,o=E[i.b].element;!i.dragging&&c.onDragStart&&c.onDragStart(),t.preventDefault(),i.dragging=!0,i.move=m.bind(i),i.stop=d.bind(i),e[n]("mouseup",i.stop),e[n]("touchend",i.stop),e[n]("touchcancel",i.stop),i.parent[n]("mousemove",i.move),i.parent[n]("touchmove",i.move),r[n]("selectstart",s),r[n]("dragstart",s),o[n]("selectstart",s),o[n]("dragstart",s),r.style.userSelect="none",r.style.webkitUserSelect="none",r.style.MozUserSelect="none",r.style.pointerEvents="none",o.style.userSelect="none",o.style.webkitUserSelect="none",o.style.MozUserSelect="none",o.style.pointerEvents="none",i.gutter.style.cursor=j,i.parent.style.cursor=j,g.call(i)}function v(e){e.forEach(function(t,n){if(n>0){var i=F[n-1],r=E[i.a],s=E[i.b];r.size=e[n-1],s.size=t,z(r.element,r.size,i.aGutterSize),z(s.element,s.size,i.bGutterSize)}})}function p(){F.forEach(function(e){e.parent.removeChild(e.gutter),E[e.a].element.style[y]="",E[e.b].element.style[y]=""})}void 0===c&&(c={});var y,b,G,E,w=l(u[0]).parentNode,D=e.getComputedStyle(w).flexDirection,U=c.sizes||u.map(function(){return 100/u.length}),k=void 0!==c.minSize?c.minSize:100,x=Array.isArray(k)?k:u.map(function(){return k}),L=void 0!==c.gutterSize?c.gutterSize:10,M=void 0!==c.snapOffset?c.snapOffset:30,O=c.direction||"horizontal",j=c.cursor||("horizontal"===O?"ew-resize":"ns-resize"),C=c.gutter||function(e,n){var i=t.createElement("div");return i.className="gutter gutter-"+n,i},A=c.elementStyle||function(e,t,n){var i={};return"string"==typeof t||t instanceof String?i[e]=t:i[e]=o?t+"%":a+"("+t+"% - "+n+"px)",i},B=c.gutterStyle||function(e,t){return n={},n[e]=t+"px",n;var n};"horizontal"===O?(y="width","clientWidth",b="clientX",G="left","paddingLeft"):"vertical"===O&&(y="height","clientHeight",b="clientY",G="top","paddingTop");var F=[];return E=u.map(function(e,t){var i,s={element:l(e),size:U[t],minSize:x[t]};if(t>0&&(i={a:t-1,b:t,dragging:!1,isFirst:1===t,isLast:t===u.length-1,direction:O,parent:w},i.aGutterSize=L,i.bGutterSize=L,i.isFirst&&(i.aGutterSize=L/2),i.isLast&&(i.bGutterSize=L/2),"row-reverse"===D||"column-reverse"===D)){var a=i.a;i.a=i.b,i.b=a}if(!o&&t>0){var c=C(t,O);h(c,L),c[n]("mousedown",S.bind(i)),c[n]("touchstart",S.bind(i)),w.insertBefore(c,s.element),i.gutter=c}0===t||t===u.length-1?z(s.element,s.size,L/2):z(s.element,s.size,L);var f=s.element[r]()[y];return f<s.minSize&&(s.minSize=f),t>0&&F.push(i),s}),o?{setSizes:v,destroy:p}:{setSizes:v,getSizes:function(){return E.map(function(e){return e.size})},collapse:function(e){if(e===F.length){var t=F[e-1];g.call(t),o||f.call(t,t.size-t.bGutterSize)}else{var n=F[e];g.call(n),o||f.call(n,n.aGutterSize)}},destroy:p}}});

///////////////////////////////////////////////

///////////////////////////////////////////////
// Copyright (c) 2013 Pieroxy <<EMAIL>>
// This work is free. You can redistribute it and/or modify it
// under the terms of the WTFPL, Version 2
// For more information see LICENSE.txt or http://www.wtfpl.net/
//
// For more information, the home page:
// http://pieroxy.net/blog/pages/lz-string/testing.html
//
// LZ-based compression algorithm, version 1.4.4
var LZString=function(){var o=String.fromCharCode,i={};var n={decompressFromBase64:function(o){return null==o?"":""==o?null:n._decompress(o.length,32,function(n){return function(o,n){if(!i[o]){i[o]={};for(var t=0;t<o.length;t++)i[o][o.charAt(t)]=t}return i[o][n]}("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",o.charAt(n))})},_decompress:function(i,n,t){var r,e,a,s,p,u,l,f=[],c=4,d=4,h=3,v="",g=[],m={val:t(0),position:n,index:1};for(r=0;r<3;r+=1)f[r]=r;for(a=0,p=Math.pow(2,2),u=1;u!=p;)s=m.val&m.position,m.position>>=1,0==m.position&&(m.position=n,m.val=t(m.index++)),a|=(s>0?1:0)*u,u<<=1;switch(a){case 0:for(a=0,p=Math.pow(2,8),u=1;u!=p;)s=m.val&m.position,m.position>>=1,0==m.position&&(m.position=n,m.val=t(m.index++)),a|=(s>0?1:0)*u,u<<=1;l=o(a);break;case 1:for(a=0,p=Math.pow(2,16),u=1;u!=p;)s=m.val&m.position,m.position>>=1,0==m.position&&(m.position=n,m.val=t(m.index++)),a|=(s>0?1:0)*u,u<<=1;l=o(a);break;case 2:return""}for(f[3]=l,e=l,g.push(l);;){if(m.index>i)return"";for(a=0,p=Math.pow(2,h),u=1;u!=p;)s=m.val&m.position,m.position>>=1,0==m.position&&(m.position=n,m.val=t(m.index++)),a|=(s>0?1:0)*u,u<<=1;switch(l=a){case 0:for(a=0,p=Math.pow(2,8),u=1;u!=p;)s=m.val&m.position,m.position>>=1,0==m.position&&(m.position=n,m.val=t(m.index++)),a|=(s>0?1:0)*u,u<<=1;f[d++]=o(a),l=d-1,c--;break;case 1:for(a=0,p=Math.pow(2,16),u=1;u!=p;)s=m.val&m.position,m.position>>=1,0==m.position&&(m.position=n,m.val=t(m.index++)),a|=(s>0?1:0)*u,u<<=1;f[d++]=o(a),l=d-1,c--;break;case 2:return g.join("")}if(0==c&&(c=Math.pow(2,h),h++),f[l])v=f[l];else{if(l!==d)return null;v=e+e.charAt(0)}g.push(v),f[d++]=e+v.charAt(0),e=v,0==--c&&(c=Math.pow(2,h),h++)}}};return n}();"function"==typeof define&&define.amd?define(function(){return LZString}):"undefined"!=typeof module&&null!=module?module.exports=LZString:"undefined"!=typeof angular&&null!=angular&&angular.module("LZString",[]).factory("LZString",function(){return LZString});
///////////////////////////////////////////////

///////////////////////////////////////////////
/*!
 * PEP v0.4.3 | https://github.com/jquery/PEP
 * Copyright jQuery Foundation and other contributors | http://jquery.org/license
 */
!function(a,b){"object"==typeof exports&&"undefined"!=typeof module?module.exports=b():"function"==typeof define&&define.amd?define(b):a.PointerEventsPolyfill=b()}(this,function(){"use strict";function a(a,b){b=b||Object.create(null);var c=document.createEvent("Event");c.initEvent(a,b.bubbles||!1,b.cancelable||!1);
for(var d,e=2;e<m.length;e++)d=m[e],c[d]=b[d]||n[e];c.buttons=b.buttons||0;
var f=0;return f=b.pressure&&c.buttons?b.pressure:c.buttons?.5:0,c.x=c.clientX,c.y=c.clientY,c.pointerId=b.pointerId||0,c.width=b.width||0,c.height=b.height||0,c.pressure=f,c.tiltX=b.tiltX||0,c.tiltY=b.tiltY||0,c.twist=b.twist||0,c.tangentialPressure=b.tangentialPressure||0,c.pointerType=b.pointerType||"",c.hwTimestamp=b.hwTimestamp||0,c.isPrimary=b.isPrimary||!1,c}function b(){this.array=[],this.size=0}function c(a,b,c,d){this.addCallback=a.bind(d),this.removeCallback=b.bind(d),this.changedCallback=c.bind(d),A&&(this.observer=new A(this.mutationWatcher.bind(this)))}function d(a){return"body /shadow-deep/ "+e(a)}function e(a){return'[touch-action="'+a+'"]'}function f(a){return"{ -ms-touch-action: "+a+"; touch-action: "+a+"; }"}function g(){if(F){D.forEach(function(a){String(a)===a?(E+=e(a)+f(a)+"\n",G&&(E+=d(a)+f(a)+"\n")):(E+=a.selectors.map(e)+f(a.rule)+"\n",G&&(E+=a.selectors.map(d)+f(a.rule)+"\n"))});var a=document.createElement("style");a.textContent=E,document.head.appendChild(a)}}function h(){if(!window.PointerEvent){if(window.PointerEvent=a,window.navigator.msPointerEnabled){var b=window.navigator.msMaxTouchPoints;Object.defineProperty(window.navigator,"maxTouchPoints",{value:b,enumerable:!0}),u.registerSource("ms",_)}else Object.defineProperty(window.navigator,"maxTouchPoints",{value:0,enumerable:!0}),u.registerSource("mouse",N),void 0!==window.ontouchstart&&u.registerSource("touch",V);u.register(document)}}function i(a){if(!u.pointermap.has(a)){var b=new Error("InvalidPointerId");throw b.name="InvalidPointerId",b}}function j(a){for(var b=a.parentNode;b&&b!==a.ownerDocument;)b=b.parentNode;if(!b){var c=new Error("InvalidStateError");throw c.name="InvalidStateError",c}}function k(a){var b=u.pointermap.get(a);return 0!==b.buttons}function l(){window.Element&&!Element.prototype.setPointerCapture&&Object.defineProperties(Element.prototype,{setPointerCapture:{value:W},releasePointerCapture:{value:X},hasPointerCapture:{value:Y}})}
var m=["bubbles","cancelable","view","detail","screenX","screenY","clientX","clientY","ctrlKey","altKey","shiftKey","metaKey","button","relatedTarget","pageX","pageY"],n=[!1,!1,null,null,0,0,0,0,!1,!1,!1,!1,0,null,0,0],o=window.Map&&window.Map.prototype.forEach,p=o?Map:b;b.prototype={set:function(a,b){return void 0===b?this["delete"](a):(this.has(a)||this.size++,void(this.array[a]=b))},has:function(a){return void 0!==this.array[a]},"delete":function(a){this.has(a)&&(delete this.array[a],this.size--)},get:function(a){return this.array[a]},clear:function(){this.array.length=0,this.size=0},forEach:function(a,b){return this.array.forEach(function(c,d){a.call(b,c,d,this)},this)}};var q=["bubbles","cancelable","view","detail","screenX","screenY","clientX","clientY","ctrlKey","altKey","shiftKey","metaKey","button","relatedTarget","buttons","pointerId","width","height","pressure","tiltX","tiltY","pointerType","hwTimestamp","isPrimary","type","target","currentTarget","which","pageX","pageY","timeStamp"],r=[!1,!1,null,null,0,0,0,0,!1,!1,!1,!1,0,null,0,0,0,0,0,0,0,"",0,!1,"",null,null,0,0,0,0],s={pointerover:1,pointerout:1,pointerenter:1,pointerleave:1},t="undefined"!=typeof SVGElementInstance,u={pointermap:new p,eventMap:Object.create(null),captureInfo:Object.create(null),eventSources:Object.create(null),eventSourceList:[],registerSource:function(a,b){var c=b,d=c.events;d&&(d.forEach(function(a){c[a]&&(this.eventMap[a]=c[a].bind(c))},this),this.eventSources[a]=c,this.eventSourceList.push(c))},register:function(a){for(var b,c=this.eventSourceList.length,d=0;d<c&&(b=this.eventSourceList[d]);d++)
b.register.call(b,a)},unregister:function(a){for(var b,c=this.eventSourceList.length,d=0;d<c&&(b=this.eventSourceList[d]);d++)
b.unregister.call(b,a)},contains:function(a,b){try{return a.contains(b)}catch(c){return!1}},down:function(a){a.bubbles=!0,this.fireEvent("pointerdown",a)},move:function(a){a.bubbles=!0,this.fireEvent("pointermove",a)},up:function(a){a.bubbles=!0,this.fireEvent("pointerup",a)},enter:function(a){a.bubbles=!1,this.fireEvent("pointerenter",a)},leave:function(a){a.bubbles=!1,this.fireEvent("pointerleave",a)},over:function(a){a.bubbles=!0,this.fireEvent("pointerover",a)},out:function(a){a.bubbles=!0,this.fireEvent("pointerout",a)},cancel:function(a){a.bubbles=!0,this.fireEvent("pointercancel",a)},leaveOut:function(a){this.out(a),this.propagate(a,this.leave,!1)},enterOver:function(a){this.over(a),this.propagate(a,this.enter,!0)},eventHandler:function(a){if(!a._handledByPE){var b=a.type,c=this.eventMap&&this.eventMap[b];c&&c(a),a._handledByPE=!0}},listen:function(a,b){b.forEach(function(b){this.addEvent(a,b)},this)},unlisten:function(a,b){b.forEach(function(b){this.removeEvent(a,b)},this)},addEvent:function(a,b){a.addEventListener(b,this.boundHandler)},removeEvent:function(a,b){a.removeEventListener(b,this.boundHandler)},makeEvent:function(b,c){this.captureInfo[c.pointerId]&&(c.relatedTarget=null);var d=new a(b,c);return c.preventDefault&&(d.preventDefault=c.preventDefault),d._target=d._target||c.target,d},fireEvent:function(a,b){var c=this.makeEvent(a,b);return this.dispatchEvent(c)},cloneEvent:function(a){for(var b,c=Object.create(null),d=0;d<q.length;d++)b=q[d],c[b]=a[b]||r[d],!t||"target"!==b&&"relatedTarget"!==b||c[b]instanceof SVGElementInstance&&(c[b]=c[b].correspondingUseElement);return a.preventDefault&&(c.preventDefault=function(){a.preventDefault()}),c},getTarget:function(a){var b=this.captureInfo[a.pointerId];return b?a._target!==b&&a.type in s?void 0:b:a._target},propagate:function(a,b,c){for(var d=a.target,e=[];d!==document&&!d.contains(a.relatedTarget);) if(e.push(d),d=d.parentNode,!d)return;c&&e.reverse(),e.forEach(function(c){a.target=c,b.call(this,a)},this)},setCapture:function(b,c,d){this.captureInfo[b]&&this.releaseCapture(b,d),this.captureInfo[b]=c,this.implicitRelease=this.releaseCapture.bind(this,b,d),document.addEventListener("pointerup",this.implicitRelease),document.addEventListener("pointercancel",this.implicitRelease);var e=new a("gotpointercapture");e.pointerId=b,e._target=c,d||this.asyncDispatchEvent(e)},releaseCapture:function(b,c){var d=this.captureInfo[b];if(d){this.captureInfo[b]=void 0,document.removeEventListener("pointerup",this.implicitRelease),document.removeEventListener("pointercancel",this.implicitRelease);var e=new a("lostpointercapture");e.pointerId=b,e._target=d,c||this.asyncDispatchEvent(e)}},dispatchEvent:/*scope.external.dispatchEvent || */function(a){var b=this.getTarget(a);if(b)return b.dispatchEvent(a)},asyncDispatchEvent:function(a){requestAnimationFrame(this.dispatchEvent.bind(this,a))}};u.boundHandler=u.eventHandler.bind(u);var v={shadow:function(a){if(a)return a.shadowRoot||a.webkitShadowRoot},canTarget:function(a){return a&&Boolean(a.elementFromPoint)},targetingShadow:function(a){var b=this.shadow(a);if(this.canTarget(b))return b},olderShadow:function(a){var b=a.olderShadowRoot;if(!b){var c=a.querySelector("shadow");c&&(b=c.olderShadowRoot)}return b},allShadows:function(a){for(var b=[],c=this.shadow(a);c;)b.push(c),c=this.olderShadow(c);return b},searchRoot:function(a,b,c){if(a){var d,e,f=a.elementFromPoint(b,c);for(e=this.targetingShadow(f);e;){if(d=e.elementFromPoint(b,c)){var g=this.targetingShadow(d);return this.searchRoot(g,b,c)||d} e=this.olderShadow(e)} return f}},owner:function(a){
for(var b=a;b.parentNode;)b=b.parentNode;
return b.nodeType!==Node.DOCUMENT_NODE&&b.nodeType!==Node.DOCUMENT_FRAGMENT_NODE&&(b=document),b},findTarget:function(a){var b=a.clientX,c=a.clientY,d=this.owner(a.target);
return d.elementFromPoint(b,c)||(d=document),this.searchRoot(d,b,c)}},w=Array.prototype.forEach.call.bind(Array.prototype.forEach),x=Array.prototype.map.call.bind(Array.prototype.map),y=Array.prototype.slice.call.bind(Array.prototype.slice),z=Array.prototype.filter.call.bind(Array.prototype.filter),A=window.MutationObserver||window.WebKitMutationObserver,B="[touch-action]",C={subtree:!0,childList:!0,attributes:!0,attributeOldValue:!0,attributeFilter:["touch-action"]};c.prototype={watchSubtree:function(a){
//
this.observer&&v.canTarget(a)&&this.observer.observe(a,C)},enableOnSubtree:function(a){this.watchSubtree(a),a===document&&"complete"!==document.readyState?this.installOnLoad():this.installNewSubtree(a)},installNewSubtree:function(a){w(this.findElements(a),this.addElement,this)},findElements:function(a){return a.querySelectorAll?a.querySelectorAll(B):[]},removeElement:function(a){this.removeCallback(a)},addElement:function(a){this.addCallback(a)},elementChanged:function(a,b){this.changedCallback(a,b)},concatLists:function(a,b){return a.concat(y(b))},
installOnLoad:function(){document.addEventListener("readystatechange",function(){"complete"===document.readyState&&this.installNewSubtree(document)}.bind(this))},isElement:function(a){return a.nodeType===Node.ELEMENT_NODE},flattenMutationTree:function(a){
var b=x(a,this.findElements,this);
return b.push(z(a,this.isElement)),b.reduce(this.concatLists,[])},mutationWatcher:function(a){a.forEach(this.mutationHandler,this)},mutationHandler:function(a){if("childList"===a.type){var b=this.flattenMutationTree(a.addedNodes);b.forEach(this.addElement,this);var c=this.flattenMutationTree(a.removedNodes);c.forEach(this.removeElement,this)}else"attributes"===a.type&&this.elementChanged(a.target,a.oldValue)}};var D=["none","auto","pan-x","pan-y",{rule:"pan-x pan-y",selectors:["pan-x pan-y","pan-y pan-x"]}],E="",F=window.PointerEvent||window.MSPointerEvent,G=!window.ShadowDOMPolyfill&&document.head.createShadowRoot,H=u.pointermap,I=25,J=[1,4,2,8,16],K=!1;try{K=1===new MouseEvent("test",{buttons:1}).buttons}catch(L){}
var M,N={POINTER_ID:1,POINTER_TYPE:"mouse",events:["mousedown","mousemove","mouseup","mouseover","mouseout"],register:function(a){u.listen(a,this.events)},unregister:function(a){u.unlisten(a,this.events)},lastTouches:[],
isEventSimulatedFromTouch:function(a){for(var b,c=this.lastTouches,d=a.clientX,e=a.clientY,f=0,g=c.length;f<g&&(b=c[f]);f++){
var h=Math.abs(d-b.x),i=Math.abs(e-b.y);if(h<=I&&i<=I)return!0}},prepareEvent:function(a){var b=u.cloneEvent(a),c=b.preventDefault;return b.preventDefault=function(){a.preventDefault(),c()},b.pointerId=this.POINTER_ID,b.isPrimary=!0,b.pointerType=this.POINTER_TYPE,b},prepareButtonsForMove:function(a,b){var c=H.get(this.POINTER_ID);
0!==b.which&&c?a.buttons=c.buttons:a.buttons=0,b.buttons=a.buttons},mousedown:function(a){if(!this.isEventSimulatedFromTouch(a)){var b=H.get(this.POINTER_ID),c=this.prepareEvent(a);K||(c.buttons=J[c.button],b&&(c.buttons|=b.buttons),a.buttons=c.buttons),H.set(this.POINTER_ID,a),b&&0!==b.buttons?u.move(c):u.down(c)}},mousemove:function(a){if(!this.isEventSimulatedFromTouch(a)){var b=this.prepareEvent(a);K||this.prepareButtonsForMove(b,a),b.button=-1,H.set(this.POINTER_ID,a),u.move(b)}},mouseup:function(a){if(!this.isEventSimulatedFromTouch(a)){var b=H.get(this.POINTER_ID),c=this.prepareEvent(a);if(!K){var d=J[c.button];
c.buttons=b?b.buttons&~d:0,a.buttons=c.buttons}H.set(this.POINTER_ID,a),
c.buttons&=~J[c.button],0===c.buttons?u.up(c):u.move(c)}},mouseover:function(a){if(!this.isEventSimulatedFromTouch(a)){var b=this.prepareEvent(a);K||this.prepareButtonsForMove(b,a),b.button=-1,H.set(this.POINTER_ID,a),u.enterOver(b)}},mouseout:function(a){if(!this.isEventSimulatedFromTouch(a)){var b=this.prepareEvent(a);K||this.prepareButtonsForMove(b,a),b.button=-1,u.leaveOut(b)}},cancel:function(a){var b=this.prepareEvent(a);u.cancel(b),this.deactivateMouse()},deactivateMouse:function(){H["delete"](this.POINTER_ID)}},O=u.captureInfo,P=v.findTarget.bind(v),Q=v.allShadows.bind(v),R=u.pointermap,S=2500,T=200,U="touch-action",V={events:["touchstart","touchmove","touchend","touchcancel"],register:function(a){M.enableOnSubtree(a)},unregister:function(){},elementAdded:function(a){var b=a.getAttribute(U),c=this.touchActionToScrollType(b);c&&(a._scrollType=c,u.listen(a,this.events),
Q(a).forEach(function(a){a._scrollType=c,u.listen(a,this.events)},this))},elementRemoved:function(a){a._scrollType=void 0,u.unlisten(a,this.events),
Q(a).forEach(function(a){a._scrollType=void 0,u.unlisten(a,this.events)},this)},elementChanged:function(a,b){var c=a.getAttribute(U),d=this.touchActionToScrollType(c),e=this.touchActionToScrollType(b);
d&&e?(a._scrollType=d,Q(a).forEach(function(a){a._scrollType=d},this)):e?this.elementRemoved(a):d&&this.elementAdded(a)},scrollTypes:{EMITTER:"none",XSCROLLER:"pan-x",YSCROLLER:"pan-y",SCROLLER:/^(?:pan-x pan-y)|(?:pan-y pan-x)|auto$/},touchActionToScrollType:function(a){var b=a,c=this.scrollTypes;return"none"===b?"none":b===c.XSCROLLER?"X":b===c.YSCROLLER?"Y":c.SCROLLER.exec(b)?"XY":void 0},POINTER_TYPE:"touch",firstTouch:null,isPrimaryTouch:function(a){return this.firstTouch===a.identifier},setPrimaryTouch:function(a){
(0===R.size||1===R.size&&R.has(1))&&(this.firstTouch=a.identifier,this.firstXY={X:a.clientX,Y:a.clientY},this.scrolling=!1,this.cancelResetClickCount())},removePrimaryPointer:function(a){a.isPrimary&&(this.firstTouch=null,this.firstXY=null,this.resetClickCount())},clickCount:0,resetId:null,resetClickCount:function(){var a=function(){this.clickCount=0,this.resetId=null}.bind(this);this.resetId=setTimeout(a,T)},cancelResetClickCount:function(){this.resetId&&clearTimeout(this.resetId)},typeToButtons:function(a){var b=0;return"touchstart"!==a&&"touchmove"!==a||(b=1),b},touchToPointer:function(a){var b=this.currentTouchEvent,c=u.cloneEvent(a),d=c.pointerId=a.identifier+2;c.target=O[d]||P(c),c.bubbles=!0,c.cancelable=!0,c.detail=this.clickCount,c.button=0,c.buttons=this.typeToButtons(b.type),c.width=2*(a.radiusX||a.webkitRadiusX||0),c.height=2*(a.radiusY||a.webkitRadiusY||0),c.pressure=a.force||a.webkitForce||.5,c.isPrimary=this.isPrimaryTouch(a),c.pointerType=this.POINTER_TYPE,
c.altKey=b.altKey,c.ctrlKey=b.ctrlKey,c.metaKey=b.metaKey,c.shiftKey=b.shiftKey;
var e=this;return c.preventDefault=function(){e.scrolling=!1,e.firstXY=null,b.preventDefault()},c},processTouches:function(a,b){var c=a.changedTouches;this.currentTouchEvent=a;for(var d,e=0;e<c.length;e++)d=c[e],b.call(this,this.touchToPointer(d))},
shouldScroll:function(a){if(this.firstXY){var b,c=a.currentTarget._scrollType;if("none"===c)
b=!1;else if("XY"===c)
b=!0;else{var d=a.changedTouches[0],e=c,f="Y"===c?"X":"Y",g=Math.abs(d["client"+e]-this.firstXY[e]),h=Math.abs(d["client"+f]-this.firstXY[f]);
b=g>=h}return this.firstXY=null,b}},findTouch:function(a,b){for(var c,d=0,e=a.length;d<e&&(c=a[d]);d++)if(c.identifier===b)return!0},
vacuumTouches:function(a){var b=a.touches;
if(R.size>=b.length){var c=[];R.forEach(function(a,d){
if(1!==d&&!this.findTouch(b,d-2)){var e=a.out;c.push(e)}},this),c.forEach(this.cancelOut,this)}},touchstart:function(a){this.vacuumTouches(a),this.setPrimaryTouch(a.changedTouches[0]),this.dedupSynthMouse(a),this.scrolling||(this.clickCount++,this.processTouches(a,this.overDown))},overDown:function(a){R.set(a.pointerId,{target:a.target,out:a,outTarget:a.target}),u.enterOver(a),u.down(a)},touchmove:function(a){this.scrolling||(this.shouldScroll(a)?(this.scrolling=!0,this.touchcancel(a)):(a.preventDefault(),this.processTouches(a,this.moveOverOut)))},moveOverOut:function(a){var b=a,c=R.get(b.pointerId);
if(c){var d=c.out,e=c.outTarget;u.move(b),d&&e!==b.target&&(d.relatedTarget=b.target,b.relatedTarget=e,
d.target=e,b.target?(u.leaveOut(d),u.enterOver(b)):(
b.target=e,b.relatedTarget=null,this.cancelOut(b))),c.out=b,c.outTarget=b.target}},touchend:function(a){this.dedupSynthMouse(a),this.processTouches(a,this.upOut)},upOut:function(a){this.scrolling||(u.up(a),u.leaveOut(a)),this.cleanUpPointer(a)},touchcancel:function(a){this.processTouches(a,this.cancelOut)},cancelOut:function(a){u.cancel(a),u.leaveOut(a),this.cleanUpPointer(a)},cleanUpPointer:function(a){R["delete"](a.pointerId),this.removePrimaryPointer(a)},
dedupSynthMouse:function(a){var b=N.lastTouches,c=a.changedTouches[0];
if(this.isPrimaryTouch(c)){
var d={x:c.clientX,y:c.clientY};b.push(d);var e=function(a,b){var c=a.indexOf(b);c>-1&&a.splice(c,1)}.bind(null,b,d);setTimeout(e,S)}}};M=new c(V.elementAdded,V.elementRemoved,V.elementChanged,V);var W,X,Y,Z=u.pointermap,$=window.MSPointerEvent&&"number"==typeof window.MSPointerEvent.MSPOINTER_TYPE_MOUSE,_={events:["MSPointerDown","MSPointerMove","MSPointerUp","MSPointerOut","MSPointerOver","MSPointerCancel","MSGotPointerCapture","MSLostPointerCapture"],register:function(a){u.listen(a,this.events)},unregister:function(a){u.unlisten(a,this.events)},POINTER_TYPES:["","unavailable","touch","pen","mouse"],prepareEvent:function(a){var b=a;return $&&(b=u.cloneEvent(a),b.pointerType=this.POINTER_TYPES[a.pointerType]),b},cleanup:function(a){Z["delete"](a)},MSPointerDown:function(a){Z.set(a.pointerId,a);var b=this.prepareEvent(a);u.down(b)},MSPointerMove:function(a){var b=this.prepareEvent(a);u.move(b)},MSPointerUp:function(a){var b=this.prepareEvent(a);u.up(b),this.cleanup(a.pointerId)},MSPointerOut:function(a){var b=this.prepareEvent(a);u.leaveOut(b)},MSPointerOver:function(a){var b=this.prepareEvent(a);u.enterOver(b)},MSPointerCancel:function(a){var b=this.prepareEvent(a);u.cancel(b),this.cleanup(a.pointerId)},MSLostPointerCapture:function(a){var b=u.makeEvent("lostpointercapture",a);u.dispatchEvent(b)},MSGotPointerCapture:function(a){var b=u.makeEvent("gotpointercapture",a);u.dispatchEvent(b)}},aa=window.navigator;aa.msPointerEnabled?(W=function(a){i(a),j(this),k(a)&&(u.setCapture(a,this,!0),this.msSetPointerCapture(a))},X=function(a){i(a),u.releaseCapture(a,!0),this.msReleasePointerCapture(a)}):(W=function(a){i(a),j(this),k(a)&&u.setCapture(a,this)},X=function(a){i(a),u.releaseCapture(a)}),Y=function(a){return!!u.captureInfo[a]},g(),h(),l();var ba={dispatcher:u,Installer:c,PointerEvent:a,PointerMap:p,targetFinding:v};return ba});

///////////////////////////////////////////////

///////////////////////////////////////////////
var config = {"dark_mode": false, "show_pads": true, "show_fabrication": false, "show_silkscreen": true, "highlight_pin1": false, "redraw_on_drag": true, "board_rotation": 0, "checkboxes": "Sourced,Placed", "bom_view": "left-right", "layer_view": "FB", "fields": ["Value", "Footprint"]}
///////////////////////////////////////////////

///////////////////////////////////////////////
var pcbdata = JSON.parse(LZString.decompressFromBase64("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"))
///////////////////////////////////////////////

///////////////////////////////////////////////
/* Utility functions */

var storagePrefix = 'KiCad_HTML_BOM__' + pcbdata.metadata.title + '__' +
  pcbdata.metadata.revision + '__#';
var storage;

function initStorage(key) {
  try {
    window.localStorage.getItem("blank");
    storage = window.localStorage;
  } catch (e) {
    // localStorage not available
  }
  if (!storage) {
    try {
      window.sessionStorage.getItem("blank");
      storage = window.sessionStorage;
    } catch (e) {
      // sessionStorage also not available
    }
  }
}

function readStorage(key) {
  if (storage) {
    return storage.getItem(storagePrefix + key);
  } else {
    return null;
  }
}

function writeStorage(key, value) {
  if (storage) {
    storage.setItem(storagePrefix + key, value);
  }
}

function fancyDblClickHandler(el, onsingle, ondouble) {
  return function() {
    if (el.getAttribute("data-dblclick") == null) {
      el.setAttribute("data-dblclick", 1);
      setTimeout(function() {
        if (el.getAttribute("data-dblclick") == 1) {
          onsingle();
        }
        el.removeAttribute("data-dblclick");
      }, 200);
    } else {
      el.removeAttribute("data-dblclick");
      ondouble();
    }
  }
}

function smoothScrollToRow(rowid) {
  document.getElementById(rowid).scrollIntoView({
    behavior: "smooth",
    block: "center",
    inline: "nearest"
  });
}

function focusInputField(input) {
  input.scrollIntoView(false);
  input.focus();
  input.select();
}

function saveBomTable(output) {
  var text = '';
  for (var node of bomhead.childNodes[0].childNodes) {
    if (node.firstChild) {
      text += (output == 'csv' ? `"${node.firstChild.nodeValue}"` : node.firstChild.nodeValue);
    }
    if (node != bomhead.childNodes[0].lastChild) {
      text += (output == 'csv' ? ',' : '\t');
    }
  }
  text += '\n';
  for (var row of bombody.childNodes) {
    for (var cell of row.childNodes) {
      let val = '';
      for (var node of cell.childNodes) {
        if (node.nodeName == "INPUT") {
          if (node.checked) {
            val += '✓';
          }
        } else if (node.nodeName == "MARK") {
          val += node.firstChild.nodeValue;
        } else {
          val += node.nodeValue;
        }
      }
      if (output == 'csv') {
        val = val.replace(/\"/g, '\"\"'); // pair of double-quote characters
        if (isNumeric(val)) {
          val = +val;                     // use number
        } else {
          val = `"${val}"`;               // enclosed within double-quote
        }
      }
      text += val;
      if (cell != row.lastChild) {
        text += (output == 'csv' ? ',' : '\t');
      }
    }
    text += '\n';
  }

  if (output != 'clipboard') {
    // To file: csv or txt
    var blob = new Blob([text], {
      type: `text/${output}`
    });
    saveFile(`${pcbdata.metadata.title}.${output}`, blob);
  } else {
    // To clipboard
    var textArea = document.createElement("textarea");
    textArea.classList.add('clipboard-temp');
    textArea.value = text;

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      if (document.execCommand('copy')) {
        console.log('Bom copied to clipboard.');
      }
    } catch (err) {
      console.log('Can not copy to clipboard.');
    }

    document.body.removeChild(textArea);
  }
}

function isNumeric(str) {
  /* https://stackoverflow.com/a/175787 */
  return (typeof str != "string" ? false : !isNaN(str) && !isNaN(parseFloat(str)));
}

function removeGutterNode(node) {
  for (var i = 0; i < node.childNodes.length; i++) {
    if (node.childNodes[i].classList &&
      node.childNodes[i].classList.contains("gutter")) {
      node.removeChild(node.childNodes[i]);
      break;
    }
  }
}

function cleanGutters() {
  removeGutterNode(document.getElementById("bot"));
  removeGutterNode(document.getElementById("canvasdiv"));
}

var units = {
  prefixes: {
    giga: ["G", "g", "giga", "Giga", "GIGA"],
    mega: ["M", "mega", "Mega", "MEGA"],
    kilo: ["K", "k", "kilo", "Kilo", "KILO"],
    milli: ["m", "milli", "Milli", "MILLI"],
    micro: ["U", "u", "micro", "Micro", "MICRO", "μ", "µ"], // different utf8 μ
    nano: ["N", "n", "nano", "Nano", "NANO"],
    pico: ["P", "p", "pico", "Pico", "PICO"],
  },
  unitsShort: ["R", "r", "Ω", "F", "f", "H", "h"],
  unitsLong: [
    "OHM", "Ohm", "ohm", "ohms",
    "FARAD", "Farad", "farad",
    "HENRY", "Henry", "henry"
  ],
  getMultiplier: function(s) {
    if (this.prefixes.giga.includes(s)) return 1e9;
    if (this.prefixes.mega.includes(s)) return 1e6;
    if (this.prefixes.kilo.includes(s)) return 1e3;
    if (this.prefixes.milli.includes(s)) return 1e-3;
    if (this.prefixes.micro.includes(s)) return 1e-6;
    if (this.prefixes.nano.includes(s)) return 1e-9;
    if (this.prefixes.pico.includes(s)) return 1e-12;
    return 1;
  },
  valueRegex: null,
}

function initUtils() {
  var allPrefixes = units.prefixes.giga
    .concat(units.prefixes.mega)
    .concat(units.prefixes.kilo)
    .concat(units.prefixes.milli)
    .concat(units.prefixes.micro)
    .concat(units.prefixes.nano)
    .concat(units.prefixes.pico);
  var allUnits = units.unitsShort.concat(units.unitsLong);
  units.valueRegex = new RegExp("^([0-9\.]+)" +
    "\\s*(" + allPrefixes.join("|") + ")?" +
    "(" + allUnits.join("|") + ")?" +
    "(\\b.*)?$", "");
  units.valueAltRegex = new RegExp("^([0-9]*)" +
    "(" + units.unitsShort.join("|") + ")?" +
    "([GgMmKkUuNnPp])?" +
    "([0-9]*)" +
    "(\\b.*)?$", "");
  if (config.fields.includes("Value")) {
    var index = config.fields.indexOf("Value");
    pcbdata.bom["parsedValues"] = {};
    for (var id in pcbdata.bom.fields) {
      pcbdata.bom.parsedValues[id] = parseValue(pcbdata.bom.fields[id][index])
    }
  }
}

function parseValue(val, ref) {
  var inferUnit = (unit, ref) => {
    if (unit) {
      unit = unit.toLowerCase();
      if (unit == 'Ω' || unit == "ohm" || unit == "ohms") {
        unit = 'r';
      }
      unit = unit[0];
    } else {
      ref = /^([a-z]+)\d+$/i.exec(ref);
      if (ref) {
        ref = ref[1].toLowerCase();
        if (ref == "c") unit = 'f';
        else if (ref == "l") unit = 'h';
        else if (ref == "r" || ref == "rv") unit = 'r';
        else unit = null;
      }
    }
    return unit;
  };
  val = val.replace(/,/g, "");
  var match = units.valueRegex.exec(val);
  var unit;
  if (match) {
    val = parseFloat(match[1]);
    if (match[2]) {
      val = val * units.getMultiplier(match[2]);
    }
    unit = inferUnit(match[3], ref);
    if (!unit) return null;
    else return {
      val: val,
      unit: unit,
      extra: match[4],
    }
  }
  match = units.valueAltRegex.exec(val);
  if (match && (match[1] || match[4])) {
    val = parseFloat(match[1] + "." + match[4]);
    if (match[3]) {
      val = val * units.getMultiplier(match[3]);
    }
    unit = inferUnit(match[2], ref);
    if (!unit) return null;
    else return {
      val: val,
      unit: unit,
      extra: match[5],
    }
  }
  return null;
}

function valueCompare(a, b, stra, strb) {
  if (a === null && b === null) {
    // Failed to parse both values, compare them as strings.
    if (stra != strb) return stra > strb ? 1 : -1;
    else return 0;
  } else if (a === null) {
    return 1;
  } else if (b === null) {
    return -1;
  } else {
    if (a.unit != b.unit) return a.unit > b.unit ? 1 : -1;
    else if (a.val != b.val) return a.val > b.val ? 1 : -1;
    else if (a.extra != b.extra) return a.extra > b.extra ? 1 : -1;
    else return 0;
  }
}

function validateSaveImgDimension(element) {
  var valid = false;
  var intValue = 0;
  if (/^[1-9]\d*$/.test(element.value)) {
    intValue = parseInt(element.value);
    if (intValue <= 16000) {
      valid = true;
    }
  }
  if (valid) {
    element.classList.remove("invalid");
  } else {
    element.classList.add("invalid");
  }
  return intValue;
}

function saveImage(layer) {
  var width = validateSaveImgDimension(document.getElementById("render-save-width"));
  var height = validateSaveImgDimension(document.getElementById("render-save-height"));
  var bgcolor = null;
  if (!document.getElementById("render-save-transparent").checked) {
    var style = getComputedStyle(topmostdiv);
    bgcolor = style.getPropertyValue("background-color");
  }
  if (!width || !height) return;

  // Prepare image
  var canvas = document.createElement("canvas");
  var layerdict = {
    transform: {
      x: 0,
      y: 0,
      s: 1,
      panx: 0,
      pany: 0,
      zoom: 1,
    },
    bg: canvas,
    fab: canvas,
    silk: canvas,
    highlight: canvas,
    layer: layer,
  }
  // Do the rendering
  recalcLayerScale(layerdict, width, height);
  prepareLayer(layerdict);
  clearCanvas(canvas, bgcolor);
  drawBackground(layerdict, false);
  drawHighlightsOnLayer(layerdict, false);

  // Save image
  var imgdata = canvas.toDataURL("image/png");

  var filename = pcbdata.metadata.title;
  if (pcbdata.metadata.revision) {
    filename += `.${pcbdata.metadata.revision}`;
  }
  filename += `.${layer}.png`;
  saveFile(filename, dataURLtoBlob(imgdata));
}

function saveSettings() {
  var data = {
    type: "InteractiveHtmlBom settings",
    version: 1,
    pcbmetadata: pcbdata.metadata,
    settings: settings,
  }
  var blob = new Blob([JSON.stringify(data, null, 4)], {
    type: "application/json"
  });
  saveFile(`${pcbdata.metadata.title}.settings.json`, blob);
}

function loadSettings() {
  var input = document.createElement("input");
  input.type = "file";
  input.accept = ".settings.json";
  input.onchange = function(e) {
    var file = e.target.files[0];
    var reader = new FileReader();
    reader.onload = readerEvent => {
      var content = readerEvent.target.result;
      var newSettings;
      try {
        newSettings = JSON.parse(content);
      } catch (e) {
        alert("Selected file is not InteractiveHtmlBom settings file.");
        return;
      }
      if (newSettings.type != "InteractiveHtmlBom settings") {
        alert("Selected file is not InteractiveHtmlBom settings file.");
        return;
      }
      var metadataMatches = newSettings.hasOwnProperty("pcbmetadata");
      if (metadataMatches) {
        for (var k in pcbdata.metadata) {
          if (!newSettings.pcbmetadata.hasOwnProperty(k) || newSettings.pcbmetadata[k] != pcbdata.metadata[k]) {
            metadataMatches = false;
          }
        }
      }
      if (!metadataMatches) {
        var currentMetadata = JSON.stringify(pcbdata.metadata, null, 4);
        var fileMetadata = JSON.stringify(newSettings.pcbmetadata, null, 4);
        if (!confirm(
            `Settins file metadata does not match current metadata.\n\n` +
            `Page metadata:\n${currentMetadata}\n\n` +
            `Settings file metadata:\n${fileMetadata}\n\n` +
            `Press OK if you would like to import settings anyway.`)) {
          return;
        }
      }
      overwriteSettings(newSettings.settings);
    }
    reader.readAsText(file, 'UTF-8');
  }
  input.click();
}

function overwriteSettings(newSettings) {
  initDone = false;
  Object.assign(settings, newSettings);
  writeStorage("bomlayout", settings.bomlayout);
  writeStorage("bommode", settings.bommode);
  writeStorage("canvaslayout", settings.canvaslayout);
  writeStorage("bomCheckboxes", settings.checkboxes.join(","));
  document.getElementById("bomCheckboxes").value = settings.checkboxes.join(",");
  for (var checkbox of settings.checkboxes) {
    writeStorage("checkbox_" + checkbox, settings.checkboxStoredRefs[checkbox]);
  }
  writeStorage("markWhenChecked", settings.markWhenChecked);
  padsVisible(settings.renderPads);
  document.getElementById("padsCheckbox").checked = settings.renderPads;
  fabricationVisible(settings.renderFabrication);
  document.getElementById("fabricationCheckbox").checked = settings.renderFabrication;
  silkscreenVisible(settings.renderSilkscreen);
  document.getElementById("silkscreenCheckbox").checked = settings.renderSilkscreen;
  referencesVisible(settings.renderReferences);
  document.getElementById("referencesCheckbox").checked = settings.renderReferences;
  valuesVisible(settings.renderValues);
  document.getElementById("valuesCheckbox").checked = settings.renderValues;
  tracksVisible(settings.renderTracks);
  document.getElementById("tracksCheckbox").checked = settings.renderTracks;
  zonesVisible(settings.renderZones);
  document.getElementById("zonesCheckbox").checked = settings.renderZones;
  dnpOutline(settings.renderDnpOutline);
  document.getElementById("dnpOutlineCheckbox").checked = settings.renderDnpOutline;
  setRedrawOnDrag(settings.redrawOnDrag);
  document.getElementById("dragCheckbox").checked = settings.redrawOnDrag;
  setDarkMode(settings.darkMode);
  document.getElementById("darkmodeCheckbox").checked = settings.darkMode;
  setHighlightPin1(settings.highlightpin1);
  document.getElementById("highlightpin1Checkbox").checked = settings.highlightpin1;
  showFootprints(settings.show_footprints);
  writeStorage("boardRotation", settings.boardRotation);
  document.getElementById("boardRotation").value = settings.boardRotation / 5;
  document.getElementById("rotationDegree").textContent = settings.boardRotation;
  initDone = true;
  prepCheckboxes();
  changeBomLayout(settings.bomlayout);
}

function saveFile(filename, blob) {
  var link = document.createElement("a");
  var objurl = URL.createObjectURL(blob);
  link.download = filename;
  link.href = objurl;
  link.click();
}

function dataURLtoBlob(dataurl) {
  var arr = dataurl.split(','),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], {
    type: mime
  });
}

var settings = {
  canvaslayout: "default",
  bomlayout: "default",
  bommode: "grouped",
  checkboxes: [],
  checkboxStoredRefs: {},
  darkMode: false,
  highlightpin1: false,
  redrawOnDrag: true,
  boardRotation: 0,
  renderPads: true,
  renderReferences: true,
  renderValues: true,
  renderSilkscreen: true,
  renderFabrication: true,
  renderDnpOutline: false,
  renderTracks: true,
  renderZones: true,
  columnOrder: [],
  hiddenColumns: [],
}

function initDefaults() {
  settings.bomlayout = readStorage("bomlayout");
  if (settings.bomlayout === null) {
    settings.bomlayout = config.bom_view;
  }
  if (!['bom-only', 'left-right', 'top-bottom'].includes(settings.bomlayout)) {
    settings.bomlayout = config.bom_view;
  }
  settings.bommode = readStorage("bommode");
  if (settings.bommode === null) {
    settings.bommode = "grouped";
  }
  if (!["grouped", "ungrouped", "netlist"].includes(settings.bommode)) {
    settings.bommode = "grouped";
  }
  settings.canvaslayout = readStorage("canvaslayout");
  if (settings.canvaslayout === null) {
    settings.canvaslayout = config.layer_view;
  }
  var bomCheckboxes = readStorage("bomCheckboxes");
  if (bomCheckboxes === null) {
    bomCheckboxes = config.checkboxes;
  }
  settings.checkboxes = bomCheckboxes.split(",").filter((e) => e);
  document.getElementById("bomCheckboxes").value = bomCheckboxes;

  settings.markWhenChecked = readStorage("markWhenChecked") || "";
  populateMarkWhenCheckedOptions();

  function initBooleanSetting(storageString, def, elementId, func) {
    var b = readStorage(storageString);
    if (b === null) {
      b = def;
    } else {
      b = (b == "true");
    }
    document.getElementById(elementId).checked = b;
    func(b);
  }

  initBooleanSetting("padsVisible", config.show_pads, "padsCheckbox", padsVisible);
  initBooleanSetting("fabricationVisible", config.show_fabrication, "fabricationCheckbox", fabricationVisible);
  initBooleanSetting("silkscreenVisible", config.show_silkscreen, "silkscreenCheckbox", silkscreenVisible);
  initBooleanSetting("referencesVisible", true, "referencesCheckbox", referencesVisible);
  initBooleanSetting("valuesVisible", true, "valuesCheckbox", valuesVisible);
  if ("tracks" in pcbdata) {
    initBooleanSetting("tracksVisible", true, "tracksCheckbox", tracksVisible);
    initBooleanSetting("zonesVisible", true, "zonesCheckbox", zonesVisible);
  } else {
    document.getElementById("tracksAndZonesCheckboxes").style.display = "none";
    tracksVisible(false);
    zonesVisible(false);
  }
  initBooleanSetting("dnpOutline", false, "dnpOutlineCheckbox", dnpOutline);
  initBooleanSetting("redrawOnDrag", config.redraw_on_drag, "dragCheckbox", setRedrawOnDrag);
  initBooleanSetting("darkmode", config.dark_mode, "darkmodeCheckbox", setDarkMode);
  initBooleanSetting("highlightpin1", config.highlight_pin1, "highlightpin1Checkbox", setHighlightPin1);

  var fields = ["checkboxes", "References"].concat(config.fields).concat(["Quantity"]);
  var hcols = JSON.parse(readStorage("hiddenColumns"));
  if (hcols === null) {
    hcols = [];
  }
  settings.hiddenColumns = hcols.filter(e => fields.includes(e));

  var cord = JSON.parse(readStorage("columnOrder"));
  if (cord === null) {
    cord = fields;
  } else {
    cord = cord.filter(e => fields.includes(e));
    if (cord.length != fields.length)
      cord = fields;
  }
  settings.columnOrder = cord;

  settings.boardRotation = readStorage("boardRotation");
  if (settings.boardRotation === null) {
    settings.boardRotation = config.board_rotation * 5;
  } else {
    settings.boardRotation = parseInt(settings.boardRotation);
  }
  document.getElementById("boardRotation").value = settings.boardRotation / 5;
  document.getElementById("rotationDegree").textContent = settings.boardRotation;
}

// Helper classes for user js callbacks.

const IBOM_EVENT_TYPES = {
  ALL: "all",
  HIGHLIGHT_EVENT: "highlightEvent",
  CHECKBOX_CHANGE_EVENT: "checkboxChangeEvent",
  BOM_BODY_CHANGE_EVENT: "bomBodyChangeEvent",
}

const EventHandler = {
  callbacks: {},
  init: function() {
    for (eventType of Object.values(IBOM_EVENT_TYPES))
      this.callbacks[eventType] = [];
  },
  registerCallback: function(eventType, callback) {
    this.callbacks[eventType].push(callback);
  },
  emitEvent: function(eventType, eventArgs) {
    event = {
      eventType: eventType,
      args: eventArgs,
    }
    var callback;
    for (callback of this.callbacks[eventType])
      callback(event);
    for (callback of this.callbacks[IBOM_EVENT_TYPES.ALL])
      callback(event);
  }
}
EventHandler.init();

///////////////////////////////////////////////

///////////////////////////////////////////////
/* PCB rendering code */

var emptyContext2d = document.createElement("canvas").getContext("2d");

function deg2rad(deg) {
  return deg * Math.PI / 180;
}

function calcFontPoint(linepoint, text, offsetx, offsety, tilt) {
  var point = [
    linepoint[0] * text.width + offsetx,
    linepoint[1] * text.height + offsety
  ];
  // This approximates pcbnew behavior with how text tilts depending on horizontal justification
  point[0] -= (linepoint[1] + 0.5 * (1 + text.justify[0])) * text.height * tilt;
  return point;
}

function drawText(ctx, text, color) {
  if ("ref" in text && !settings.renderReferences) return;
  if ("val" in text && !settings.renderValues) return;
  ctx.save();
  ctx.fillStyle = color;
  ctx.strokeStyle = color;
  ctx.lineCap = "round";
  ctx.lineJoin = "round";
  ctx.lineWidth = text.thickness;
  if ("svgpath" in text) {
    ctx.stroke(new Path2D(text.svgpath));
    ctx.restore();
    return;
  }
  if ("polygons" in text) {
    ctx.fill(getPolygonsPath(text));
    ctx.restore();
    return;
  }
  ctx.translate(...text.pos);
  ctx.translate(text.thickness * 0.5, 0);
  var angle = -text.angle;
  if (text.attr.includes("mirrored")) {
    ctx.scale(-1, 1);
    angle = -angle;
  }
  var tilt = 0;
  if (text.attr.includes("italic")) {
    tilt = 0.125;
  }
  var interline = text.height * 1.5 + text.thickness;
  var txt = text.text.split("\n");
  // KiCad ignores last empty line.
  if (txt[txt.length - 1] == '') txt.pop();
  ctx.rotate(deg2rad(angle));
  var offsety = (1 - text.justify[1]) / 2 * text.height; // One line offset
  offsety -= (txt.length - 1) * (text.justify[1] + 1) / 2 * interline; // Multiline offset
  for (var i in txt) {
    var lineWidth = text.thickness + interline / 2 * tilt;
    for (var j = 0; j < txt[i].length; j++) {
      if (txt[i][j] == '\t') {
        var fourSpaces = 4 * pcbdata.font_data[' '].w * text.width;
        lineWidth += fourSpaces - lineWidth % fourSpaces;
      } else {
        if (txt[i][j] == '~') {
          j++;
          if (j == txt[i].length)
            break;
        }
        lineWidth += pcbdata.font_data[txt[i][j]].w * text.width;
      }
    }
    var offsetx = -lineWidth * (text.justify[0] + 1) / 2;
    var inOverbar = false;
    for (var j = 0; j < txt[i].length; j++) {
      if (txt[i][j] == '\t') {
        var fourSpaces = 4 * pcbdata.font_data[' '].w * text.width;
        offsetx += fourSpaces - offsetx % fourSpaces;
        continue;
      } else if (txt[i][j] == '~') {
        j++;
        if (j == txt[i].length)
          break;
        if (txt[i][j] != '~') {
          inOverbar = !inOverbar;
        }
      }
      var glyph = pcbdata.font_data[txt[i][j]];
      if (inOverbar) {
        var overbarStart = [offsetx, -text.height * 1.4 + offsety];
        var overbarEnd = [offsetx + text.width * glyph.w, overbarStart[1]];

        if (!lastHadOverbar) {
          overbarStart[0] += text.height * 1.4 * tilt;
          lastHadOverbar = true;
        }
        ctx.beginPath();
        ctx.moveTo(...overbarStart);
        ctx.lineTo(...overbarEnd);
        ctx.stroke();
      } else {
        lastHadOverbar = false;
      }
      for (var line of glyph.l) {
        ctx.beginPath();
        ctx.moveTo(...calcFontPoint(line[0], text, offsetx, offsety, tilt));
        for (var k = 1; k < line.length; k++) {
          ctx.lineTo(...calcFontPoint(line[k], text, offsetx, offsety, tilt));
        }
        ctx.stroke();
      }
      offsetx += glyph.w * text.width;
    }
    offsety += interline;
  }
  ctx.restore();
}

function drawedge(ctx, scalefactor, edge, color) {
  ctx.strokeStyle = color;
  ctx.fillStyle = color;
  ctx.lineWidth = Math.max(1 / scalefactor, edge.width);
  ctx.lineCap = "round";
  ctx.lineJoin = "round";
  if ("svgpath" in edge) {
    ctx.stroke(new Path2D(edge.svgpath));
  } else {
    ctx.beginPath();
    if (edge.type == "segment") {
      ctx.moveTo(...edge.start);
      ctx.lineTo(...edge.end);
    }
    if (edge.type == "rect") {
      ctx.moveTo(...edge.start);
      ctx.lineTo(edge.start[0], edge.end[1]);
      ctx.lineTo(...edge.end);
      ctx.lineTo(edge.end[0], edge.start[1]);
      ctx.lineTo(...edge.start);
    }
    if (edge.type == "arc") {
      ctx.arc(
        ...edge.start,
        edge.radius,
        deg2rad(edge.startangle),
        deg2rad(edge.endangle));
    }
    if (edge.type == "circle") {
      ctx.arc(
        ...edge.start,
        edge.radius,
        0, 2 * Math.PI);
      ctx.closePath();
    }
    if (edge.type == "curve") {
      ctx.moveTo(...edge.start);
      ctx.bezierCurveTo(...edge.cpa, ...edge.cpb, ...edge.end);
    }
    if("filled" in edge && edge.filled)
      ctx.fill();
    else
      ctx.stroke();
  }
}

function getChamferedRectPath(size, radius, chamfpos, chamfratio) {
  // chamfpos is a bitmask, left = 1, right = 2, bottom left = 4, bottom right = 8
  var path = new Path2D();
  var width = size[0];
  var height = size[1];
  var x = width * -0.5;
  var y = height * -0.5;
  var chamfOffset = Math.min(width, height) * chamfratio;
  path.moveTo(x, 0);
  if (chamfpos & 4) {
    path.lineTo(x, y + height - chamfOffset);
    path.lineTo(x + chamfOffset, y + height);
    path.lineTo(0, y + height);
  } else {
    path.arcTo(x, y + height, x + width, y + height, radius);
  }
  if (chamfpos & 8) {
    path.lineTo(x + width - chamfOffset, y + height);
    path.lineTo(x + width, y + height - chamfOffset);
    path.lineTo(x + width, 0);
  } else {
    path.arcTo(x + width, y + height, x + width, y, radius);
  }
  if (chamfpos & 2) {
    path.lineTo(x + width, y + chamfOffset);
    path.lineTo(x + width - chamfOffset, y);
    path.lineTo(0, y);
  } else {
    path.arcTo(x + width, y, x, y, radius);
  }
  if (chamfpos & 1) {
    path.lineTo(x + chamfOffset, y);
    path.lineTo(x, y + chamfOffset);
    path.lineTo(x, 0);
  } else {
    path.arcTo(x, y, x, y + height, radius);
  }
  path.closePath();
  return path;
}

function getOblongPath(size) {
  return getChamferedRectPath(size, Math.min(size[0], size[1]) / 2, 0, 0);
}

function getPolygonsPath(shape) {
  if (shape.path2d) {
    return shape.path2d;
  }
  if ("svgpath" in shape) {
    shape.path2d = new Path2D(shape.svgpath);
  } else {
    var path = new Path2D();
    for (var polygon of shape.polygons) {
      path.moveTo(...polygon[0]);
      for (var i = 1; i < polygon.length; i++) {
        path.lineTo(...polygon[i]);
      }
      path.closePath();
    }
    shape.path2d = path;
  }
  return shape.path2d;
}

function drawPolygonShape(ctx, scalefactor, shape, color) {
  ctx.save();
  if (!("svgpath" in shape)) {
    ctx.translate(...shape.pos);
    ctx.rotate(deg2rad(-shape.angle));
  }
  if("filled" in shape && !shape.filled) {
    ctx.strokeStyle = color;
    ctx.lineWidth = Math.max(1 / scalefactor, shape.width);
    ctx.lineCap = "round";
    ctx.lineJoin = "round";
    ctx.stroke(getPolygonsPath(shape));
  } else {
    ctx.fillStyle = color;
    ctx.fill(getPolygonsPath(shape));
  }
  ctx.restore();
}

function drawDrawing(ctx, scalefactor, drawing, color) {
  if (["segment", "arc", "circle", "curve", "rect"].includes(drawing.type)) {
    drawedge(ctx, scalefactor, drawing, color);
  } else if (drawing.type == "polygon") {
    drawPolygonShape(ctx, scalefactor, drawing, color);
  } else {
    drawText(ctx, drawing, color);
  }
}

function getCirclePath(radius) {
  var path = new Path2D();
  path.arc(0, 0, radius, 0, 2 * Math.PI);
  path.closePath();
  return path;
}

function getCachedPadPath(pad) {
  if (!pad.path2d) {
    // if path2d is not set, build one and cache it on pad object
    if (pad.shape == "rect") {
      pad.path2d = new Path2D();
      pad.path2d.rect(...pad.size.map(c => -c * 0.5), ...pad.size);
    } else if (pad.shape == "oval") {
      pad.path2d = getOblongPath(pad.size);
    } else if (pad.shape == "circle") {
      pad.path2d = getCirclePath(pad.size[0] / 2);
    } else if (pad.shape == "roundrect") {
      pad.path2d = getChamferedRectPath(pad.size, pad.radius, 0, 0);
    } else if (pad.shape == "chamfrect") {
      pad.path2d = getChamferedRectPath(pad.size, pad.radius, pad.chamfpos, pad.chamfratio)
    } else if (pad.shape == "custom") {
      pad.path2d = getPolygonsPath(pad);
    }
  }
  return pad.path2d;
}

function drawPad(ctx, pad, color, outline) {
  ctx.save();
  ctx.translate(...pad.pos);
  ctx.rotate(-deg2rad(pad.angle));
  if (pad.offset) {
    ctx.translate(...pad.offset);
  }
  ctx.fillStyle = color;
  ctx.strokeStyle = color;
  var path = getCachedPadPath(pad);
  if (outline) {
    ctx.stroke(path);
  } else {
    ctx.fill(path);
  }
  ctx.restore();
}

function drawPadHole(ctx, pad, padHoleColor) {
  if (pad.type != "th") return;
  ctx.save();
  ctx.translate(...pad.pos);
  ctx.rotate(-deg2rad(pad.angle));
  ctx.fillStyle = padHoleColor;
  if (pad.drillshape == "oblong") {
    ctx.fill(getOblongPath(pad.drillsize));
  } else {
    ctx.fill(getCirclePath(pad.drillsize[0] / 2));
  }
  ctx.restore();
}

function drawFootprint(ctx, layer, scalefactor, footprint, colors, highlight, outline) {
  if (highlight) {
    // draw bounding box
    if (footprint.layer == layer) {
      ctx.save();
      ctx.globalAlpha = 0.2;
      ctx.translate(...footprint.bbox.pos);
      ctx.rotate(deg2rad(-footprint.bbox.angle));
      ctx.translate(...footprint.bbox.relpos);
      ctx.fillStyle = colors.pad;
      ctx.fillRect(0, 0, ...footprint.bbox.size);
      ctx.globalAlpha = 1;
      ctx.strokeStyle = colors.pad;
      ctx.strokeRect(0, 0, ...footprint.bbox.size);
      ctx.restore();
    }
  }
  // draw drawings
  for (var drawing of footprint.drawings) {
    if (drawing.layer == layer) {
      drawDrawing(ctx, scalefactor, drawing.drawing, colors.pad);
    }
  }
  // draw pads
  if (settings.renderPads) {
    for (var pad of footprint.pads) {
      if (pad.layers.includes(layer)) {
        drawPad(ctx, pad, colors.pad, outline);
        if (pad.pin1 && settings.highlightpin1) {
          drawPad(ctx, pad, colors.outline, true);
        }
      }
    }
    for (var pad of footprint.pads) {
      drawPadHole(ctx, pad, colors.padHole);
    }
  }
}

function drawEdgeCuts(canvas, scalefactor) {
  var ctx = canvas.getContext("2d");
  var edgecolor = getComputedStyle(topmostdiv).getPropertyValue('--pcb-edge-color');
  for (var edge of pcbdata.edges) {
    drawDrawing(ctx, scalefactor, edge, edgecolor);
  }
}

function drawFootprints(canvas, layer, scalefactor, highlight) {
  var ctx = canvas.getContext("2d");
  ctx.lineWidth = 3 / scalefactor;
  var style = getComputedStyle(topmostdiv);

  var colors = {
    pad: style.getPropertyValue('--pad-color'),
    padHole: style.getPropertyValue('--pad-hole-color'),
    outline: style.getPropertyValue('--pin1-outline-color'),
  }

  for (var i = 0; i < pcbdata.footprints.length; i++) {
    var mod = pcbdata.footprints[i];
    var outline = settings.renderDnpOutline && pcbdata.bom.skipped.includes(i);
    var h = highlightedFootprints.includes(i);
    var d = markedFootprints.has(i);
    if (highlight) {
      if(h && d) {
        colors.pad = style.getPropertyValue('--pad-color-highlight-both');
        colors.outline = style.getPropertyValue('--pin1-outline-color-highlight-both');
      } else if (h) {
        colors.pad = style.getPropertyValue('--pad-color-highlight');
        colors.outline = style.getPropertyValue('--pin1-outline-color-highlight');
      } else if (d) {
        colors.pad = style.getPropertyValue('--pad-color-highlight-marked');
        colors.outline = style.getPropertyValue('--pin1-outline-color-highlight-marked');
      }
    }
    if( h || d || !highlight) {
      drawFootprint(ctx, layer, scalefactor, mod, colors, highlight, outline);
    }
  }
}

function drawBgLayer(layername, canvas, layer, scalefactor, edgeColor, polygonColor, textColor) {
  var ctx = canvas.getContext("2d");
  for (var d of pcbdata.drawings[layername][layer]) {
    if (["segment", "arc", "circle", "curve", "rect"].includes(d.type)) {
      drawedge(ctx, scalefactor, d, edgeColor);
    } else if (d.type == "polygon") {
      drawPolygonShape(ctx, scalefactor, d, polygonColor);
    } else {
      drawText(ctx, d, textColor);
    }
  }
}

function drawTracks(canvas, layer, color, highlight) {
  ctx = canvas.getContext("2d");
  ctx.strokeStyle = color;
  ctx.lineCap = "round";
  for (var track of pcbdata.tracks[layer]) {
    if (highlight && highlightedNet != track.net) continue;
    ctx.lineWidth = track.width;
    ctx.beginPath();
    if ('radius' in track) {
      ctx.arc(
        ...track.center,
        track.radius,
        deg2rad(track.startangle),
        deg2rad(track.endangle));
    } else {
      ctx.moveTo(...track.start);
      ctx.lineTo(...track.end);
    }
    ctx.stroke();
  }
}

function drawZones(canvas, layer, color, highlight) {
  ctx = canvas.getContext("2d");
  ctx.strokeStyle = color;
  ctx.fillStyle = color;
  ctx.lineJoin = "round";
  for (var zone of pcbdata.zones[layer]) {
    if (!zone.path2d) {
      zone.path2d = getPolygonsPath(zone);
    }
    if (highlight && highlightedNet != zone.net) continue;
    ctx.fill(zone.path2d);
    if (zone.width > 0) {
      ctx.lineWidth = zone.width;
      ctx.stroke(zone.path2d);
    }
  }
}

function clearCanvas(canvas, color = null) {
  var ctx = canvas.getContext("2d");
  ctx.save();
  ctx.setTransform(1, 0, 0, 1, 0, 0);
  if (color) {
    ctx.fillStyle = color;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  } else {
    if (!window.matchMedia("print").matches)
      ctx.clearRect(0, 0, canvas.width, canvas.height);
  }
  ctx.restore();
}

function drawNets(canvas, layer, highlight) {
  var style = getComputedStyle(topmostdiv);
  if (settings.renderTracks) {
    var trackColor = style.getPropertyValue(highlight ? '--track-color-highlight' : '--track-color');
    drawTracks(canvas, layer, trackColor, highlight);
  }
  if (settings.renderZones) {
    var zoneColor = style.getPropertyValue(highlight ? '--zone-color-highlight' : '--zone-color');
    drawZones(canvas, layer, zoneColor, highlight);
  }
  if (highlight && settings.renderPads) {
    var padColor = style.getPropertyValue('--pad-color-highlight');
    var padHoleColor = style.getPropertyValue('--pad-hole-color');
    var ctx = canvas.getContext("2d");
    for (var footprint of pcbdata.footprints) {
      // draw pads
      var padDrawn = false;
      for (var pad of footprint.pads) {
        if (highlightedNet != pad.net) continue;
        if (pad.layers.includes(layer)) {
          drawPad(ctx, pad, padColor, false);
          padDrawn = true;
        }
      }
      if (padDrawn) {
        // redraw all pad holes because some pads may overlap
        for (var pad of footprint.pads) {
          drawPadHole(ctx, pad, padHoleColor);
        }
      }
    }
  }
}

function drawHighlightsOnLayer(canvasdict, clear = true) {
  if (clear) {
    clearCanvas(canvasdict.highlight);
  }
  if (markedFootprints.size > 0 || highlightedFootprints.length > 0) {
    drawFootprints(canvasdict.highlight, canvasdict.layer,
      canvasdict.transform.s * canvasdict.transform.zoom, true);
  }
  if (highlightedNet !== null) {
    drawNets(canvasdict.highlight, canvasdict.layer, true);
  }
}

function drawHighlights() {
  drawHighlightsOnLayer(allcanvas.front);
  drawHighlightsOnLayer(allcanvas.back);
}

function drawBackground(canvasdict, clear = true) {
  if (clear) {
    clearCanvas(canvasdict.bg);
    clearCanvas(canvasdict.fab);
    clearCanvas(canvasdict.silk);
  }

  drawNets(canvasdict.bg, canvasdict.layer, false);
  drawFootprints(canvasdict.bg, canvasdict.layer,
    canvasdict.transform.s * canvasdict.transform.zoom, false);

  drawEdgeCuts(canvasdict.bg, canvasdict.transform.s * canvasdict.transform.zoom);

  var style = getComputedStyle(topmostdiv);
  var edgeColor = style.getPropertyValue('--silkscreen-edge-color');
  var polygonColor = style.getPropertyValue('--silkscreen-polygon-color');
  var textColor = style.getPropertyValue('--silkscreen-text-color');
  if (settings.renderSilkscreen) {
    drawBgLayer(
      "silkscreen", canvasdict.silk, canvasdict.layer,
      canvasdict.transform.s * canvasdict.transform.zoom,
      edgeColor, polygonColor, textColor);
  }
  edgeColor = style.getPropertyValue('--fabrication-edge-color');
  polygonColor = style.getPropertyValue('--fabrication-polygon-color');
  textColor = style.getPropertyValue('--fabrication-text-color');
  if (settings.renderFabrication) {
    drawBgLayer(
      "fabrication", canvasdict.fab, canvasdict.layer,
      canvasdict.transform.s * canvasdict.transform.zoom,
      edgeColor, polygonColor, textColor);
  }
}

function prepareCanvas(canvas, flip, transform) {
  var ctx = canvas.getContext("2d");
  ctx.setTransform(1, 0, 0, 1, 0, 0);
  ctx.scale(transform.zoom, transform.zoom);
  ctx.translate(transform.panx, transform.pany);
  if (flip) {
    ctx.scale(-1, 1);
  }
  ctx.translate(transform.x, transform.y);
  ctx.rotate(deg2rad(settings.boardRotation));
  ctx.scale(transform.s, transform.s);
}

function prepareLayer(canvasdict) {
  var flip = (canvasdict.layer == "B");
  for (var c of ["bg", "fab", "silk", "highlight"]) {
    prepareCanvas(canvasdict[c], flip, canvasdict.transform);
  }
}

function rotateVector(v, angle) {
  angle = deg2rad(angle);
  return [
    v[0] * Math.cos(angle) - v[1] * Math.sin(angle),
    v[0] * Math.sin(angle) + v[1] * Math.cos(angle)
  ];
}

function applyRotation(bbox) {
  var corners = [
    [bbox.minx, bbox.miny],
    [bbox.minx, bbox.maxy],
    [bbox.maxx, bbox.miny],
    [bbox.maxx, bbox.maxy],
  ];
  corners = corners.map((v) => rotateVector(v, settings.boardRotation));
  return {
    minx: corners.reduce((a, v) => Math.min(a, v[0]), Infinity),
    miny: corners.reduce((a, v) => Math.min(a, v[1]), Infinity),
    maxx: corners.reduce((a, v) => Math.max(a, v[0]), -Infinity),
    maxy: corners.reduce((a, v) => Math.max(a, v[1]), -Infinity),
  }
}

function recalcLayerScale(layerdict, width, height) {
  var bbox = applyRotation(pcbdata.edges_bbox);
  var scalefactor = 0.98 * Math.min(
    width / (bbox.maxx - bbox.minx),
    height / (bbox.maxy - bbox.miny)
  );
  if (scalefactor < 0.1) {
    scalefactor = 1;
  }
  layerdict.transform.s = scalefactor;
  var flip = (layerdict.layer == "B");
  if (flip) {
    layerdict.transform.x = -((bbox.maxx + bbox.minx) * scalefactor + width) * 0.5;
  } else {
    layerdict.transform.x = -((bbox.maxx + bbox.minx) * scalefactor - width) * 0.5;
  }
  layerdict.transform.y = -((bbox.maxy + bbox.miny) * scalefactor - height) * 0.5;
  for (var c of ["bg", "fab", "silk", "highlight"]) {
    canvas = layerdict[c];
    canvas.width = width;
    canvas.height = height;
    canvas.style.width = (width / devicePixelRatio) + "px";
    canvas.style.height = (height / devicePixelRatio) + "px";
  }
}

function redrawCanvas(layerdict) {
  prepareLayer(layerdict);
  drawBackground(layerdict);
  drawHighlightsOnLayer(layerdict);
}

function resizeCanvas(layerdict) {
  var canvasdivid = {
    "F": "frontcanvas",
    "B": "backcanvas"
  } [layerdict.layer];
  var width = document.getElementById(canvasdivid).clientWidth * devicePixelRatio;
  var height = document.getElementById(canvasdivid).clientHeight * devicePixelRatio;
  recalcLayerScale(layerdict, width, height);
  redrawCanvas(layerdict);
}

function resizeAll() {
  resizeCanvas(allcanvas.front);
  resizeCanvas(allcanvas.back);
}

function pointWithinDistanceToSegment(x, y, x1, y1, x2, y2, d) {
  var A = x - x1;
  var B = y - y1;
  var C = x2 - x1;
  var D = y2 - y1;

  var dot = A * C + B * D;
  var len_sq = C * C + D * D;
  var dx, dy;
  if (len_sq == 0) {
    // start and end of the segment coincide
    dx = x - x1;
    dy = y - y1;
  } else {
    var param = dot / len_sq;
    var xx, yy;
    if (param < 0) {
      xx = x1;
      yy = y1;
    } else if (param > 1) {
      xx = x2;
      yy = y2;
    } else {
      xx = x1 + param * C;
      yy = y1 + param * D;
    }
    dx = x - xx;
    dy = y - yy;
  }
  return dx * dx + dy * dy <= d * d;
}

function modulo(n, mod) {
  return ((n % mod) + mod) % mod;
}

function pointWithinDistanceToArc(x, y, xc, yc, radius, startangle, endangle, d) {
  var dx = x - xc;
  var dy = y - yc;
  var r_sq = dx * dx + dy * dy;
  var rmin = Math.max(0, radius - d);
  var rmax = radius + d;

  if (r_sq < rmin * rmin || r_sq > rmax * rmax)
    return false;

  var angle1 = modulo(deg2rad(startangle), 2 * Math.PI);
  var dx1 = xc + radius * Math.cos(angle1) - x;
  var dy1 = yc + radius * Math.sin(angle1) - y;
  if (dx1 * dx1 + dy1 * dy1 <= d * d)
    return true;

  var angle2 = modulo(deg2rad(endangle), 2 * Math.PI);
  var dx2 = xc + radius * Math.cos(angle2) - x;
  var dy2 = yc + radius * Math.sin(angle2) - y;
  if (dx2 * dx2 + dy2 * dy2 <= d * d)
    return true;

  var angle = modulo(Math.atan2(dy, dx), 2 * Math.PI);
  if (angle1 > angle2)
    return (angle >= angle2 || angle <= angle1);
  else
    return (angle >= angle1 && angle <= angle2);
}

function pointWithinPad(x, y, pad) {
  var v = [x - pad.pos[0], y - pad.pos[1]];
  v = rotateVector(v, pad.angle);
  if (pad.offset) {
    v[0] -= pad.offset[0];
    v[1] -= pad.offset[1];
  }
  return emptyContext2d.isPointInPath(getCachedPadPath(pad), ...v);
}

function netHitScan(layer, x, y) {
  // Check track segments
  if (settings.renderTracks && pcbdata.tracks) {
    for (var track of pcbdata.tracks[layer]) {
      if ('radius' in track) {
        if (pointWithinDistanceToArc(x, y, ...track.center, track.radius, track.startangle, track.endangle, track.width / 2)) {
          return track.net;
        }
      } else {
        if (pointWithinDistanceToSegment(x, y, ...track.start, ...track.end, track.width / 2)) {
          return track.net;
        }
      }
    }
  }
  // Check pads
  if (settings.renderPads) {
    for (var footprint of pcbdata.footprints) {
      for (var pad of footprint.pads) {
        if (pad.layers.includes(layer) && pointWithinPad(x, y, pad)) {
          return pad.net;
        }
      }
    }
  }
  return null;
}

function pointWithinFootprintBbox(x, y, bbox) {
  var v = [x - bbox.pos[0], y - bbox.pos[1]];
  v = rotateVector(v, bbox.angle);
  return bbox.relpos[0] <= v[0] && v[0] <= bbox.relpos[0] + bbox.size[0] &&
    bbox.relpos[1] <= v[1] && v[1] <= bbox.relpos[1] + bbox.size[1];
}

function bboxHitScan(layer, x, y) {
  var result = [];
  for (var i = 0; i < pcbdata.footprints.length; i++) {
    var footprint = pcbdata.footprints[i];
    if (footprint.layer == layer) {
      if (pointWithinFootprintBbox(x, y, footprint.bbox)) {
        result.push(i);
      }
    }
  }
  return result;
}

function handlePointerDown(e, layerdict) {
  if (e.button != 0 && e.button != 1) {
    return;
  }
  e.preventDefault();
  e.stopPropagation();

  if (!e.hasOwnProperty("offsetX")) {
    // The polyfill doesn't set this properly
    e.offsetX = e.pageX - e.currentTarget.offsetLeft;
    e.offsetY = e.pageY - e.currentTarget.offsetTop;
  }

  layerdict.pointerStates[e.pointerId] = {
    distanceTravelled: 0,
    lastX: e.offsetX,
    lastY: e.offsetY,
    downTime: Date.now(),
  };
}

function handleMouseClick(e, layerdict) {
  if (!e.hasOwnProperty("offsetX")) {
    // The polyfill doesn't set this properly
    e.offsetX = e.pageX - e.currentTarget.offsetLeft;
    e.offsetY = e.pageY - e.currentTarget.offsetTop;
  }

  var x = e.offsetX;
  var y = e.offsetY;
  var t = layerdict.transform;
  if (layerdict.layer == "B") {
    x = (devicePixelRatio * x / t.zoom - t.panx + t.x) / -t.s;
  } else {
    x = (devicePixelRatio * x / t.zoom - t.panx - t.x) / t.s;
  }
  y = (devicePixelRatio * y / t.zoom - t.y - t.pany) / t.s;
  var v = rotateVector([x, y], -settings.boardRotation);
  if ("nets" in pcbdata) {
    var net = netHitScan(layerdict.layer, ...v);
    if (net !== highlightedNet) {
      netClicked(net);
    }
  }
  if (highlightedNet === null) {
    var footprints = bboxHitScan(layerdict.layer, ...v);
    if (footprints.length > 0) {
      footprintsClicked(footprints);
    }
  }
}

function handlePointerLeave(e, layerdict) {
  e.preventDefault();
  e.stopPropagation();

  if (!settings.redrawOnDrag) {
    redrawCanvas(layerdict);
  }

  delete layerdict.pointerStates[e.pointerId];
}

function resetTransform(layerdict) {
  layerdict.transform.panx = 0;
  layerdict.transform.pany = 0;
  layerdict.transform.zoom = 1;
  redrawCanvas(layerdict);
}

function handlePointerUp(e, layerdict) {
  if (!e.hasOwnProperty("offsetX")) {
    // The polyfill doesn't set this properly
    e.offsetX = e.pageX - e.currentTarget.offsetLeft;
    e.offsetY = e.pageY - e.currentTarget.offsetTop;
  }

  e.preventDefault();
  e.stopPropagation();

  if (e.button == 2) {
    // Reset pan and zoom on right click.
    resetTransform(layerdict);
    layerdict.anotherPointerTapped = false;
    return;
  }

  // We haven't necessarily had a pointermove event since the interaction started, so make sure we update this now
  var ptr = layerdict.pointerStates[e.pointerId];
  ptr.distanceTravelled += Math.abs(e.offsetX - ptr.lastX) + Math.abs(e.offsetY - ptr.lastY);

  if (e.button == 0 && ptr.distanceTravelled < 10 && Date.now() - ptr.downTime <= 500) {
    if (Object.keys(layerdict.pointerStates).length == 1) {
      if (layerdict.anotherPointerTapped) {
        // This is the second pointer coming off of a two-finger tap
        resetTransform(layerdict);
      } else {
        // This is just a regular tap
        handleMouseClick(e, layerdict);
      }
      layerdict.anotherPointerTapped = false;
    } else {
      // This is the first finger coming off of what could become a two-finger tap
      layerdict.anotherPointerTapped = true;
    }
  } else {
    if (!settings.redrawOnDrag) {
      redrawCanvas(layerdict);
    }
    layerdict.anotherPointerTapped = false;
  }

  delete layerdict.pointerStates[e.pointerId];
}

function handlePointerMove(e, layerdict) {
  if (!layerdict.pointerStates.hasOwnProperty(e.pointerId)) {
    return;
  }
  e.preventDefault();
  e.stopPropagation();

  if (!e.hasOwnProperty("offsetX")) {
    // The polyfill doesn't set this properly
    e.offsetX = e.pageX - e.currentTarget.offsetLeft;
    e.offsetY = e.pageY - e.currentTarget.offsetTop;
  }

  var thisPtr = layerdict.pointerStates[e.pointerId];

  var dx = e.offsetX - thisPtr.lastX;
  var dy = e.offsetY - thisPtr.lastY;

  // If this number is low on pointer up, we count the action as a click
  thisPtr.distanceTravelled += Math.abs(dx) + Math.abs(dy);

  if (Object.keys(layerdict.pointerStates).length == 1) {
    // This is a simple drag
    layerdict.transform.panx += devicePixelRatio * dx / layerdict.transform.zoom;
    layerdict.transform.pany += devicePixelRatio * dy / layerdict.transform.zoom;
  } else if (Object.keys(layerdict.pointerStates).length == 2) {
    var otherPtr = Object.values(layerdict.pointerStates).filter((ptr) => ptr != thisPtr)[0];

    var oldDist = Math.sqrt(Math.pow(thisPtr.lastX - otherPtr.lastX, 2) + Math.pow(thisPtr.lastY - otherPtr.lastY, 2));
    var newDist = Math.sqrt(Math.pow(e.offsetX - otherPtr.lastX, 2) + Math.pow(e.offsetY - otherPtr.lastY, 2));

    var scaleFactor = newDist / oldDist;

    if (scaleFactor != NaN) {
      layerdict.transform.zoom *= scaleFactor;

      var zoomd = (1 - scaleFactor) / layerdict.transform.zoom;
      layerdict.transform.panx += devicePixelRatio * otherPtr.lastX * zoomd;
      layerdict.transform.pany += devicePixelRatio * otherPtr.lastY * zoomd;
    }
  }

  thisPtr.lastX = e.offsetX;
  thisPtr.lastY = e.offsetY;

  if (settings.redrawOnDrag) {
    redrawCanvas(layerdict);
  }
}

function handleMouseWheel(e, layerdict) {
  e.preventDefault();
  e.stopPropagation();
  var t = layerdict.transform;
  var wheeldelta = e.deltaY;
  if (e.deltaMode == 1) {
    // FF only, scroll by lines
    wheeldelta *= 30;
  } else if (e.deltaMode == 2) {
    wheeldelta *= 300;
  }
  var m = Math.pow(1.1, -wheeldelta / 40);
  // Limit amount of zoom per tick.
  if (m > 2) {
    m = 2;
  } else if (m < 0.5) {
    m = 0.5;
  }
  t.zoom *= m;
  var zoomd = (1 - m) / t.zoom;
  t.panx += devicePixelRatio * e.offsetX * zoomd;
  t.pany += devicePixelRatio * e.offsetY * zoomd;
  redrawCanvas(layerdict);
}

function addMouseHandlers(div, layerdict) {
  div.addEventListener("pointerdown", function(e) {
    handlePointerDown(e, layerdict);
  });
  div.addEventListener("pointermove", function(e) {
    handlePointerMove(e, layerdict);
  });
  div.addEventListener("pointerup", function(e) {
    handlePointerUp(e, layerdict);
  });
  var pointerleave = function(e) {
    handlePointerLeave(e, layerdict);
  }
  div.addEventListener("pointercancel", pointerleave);
  div.addEventListener("pointerleave", pointerleave);
  div.addEventListener("pointerout", pointerleave);

  div.onwheel = function(e) {
    handleMouseWheel(e, layerdict);
  }
  for (var element of [div, layerdict.bg, layerdict.fab, layerdict.silk, layerdict.highlight]) {
    element.addEventListener("contextmenu", function(e) {
      e.preventDefault();
    }, false);
  }
}

function setRedrawOnDrag(value) {
  settings.redrawOnDrag = value;
  writeStorage("redrawOnDrag", value);
}

function setBoardRotation(value) {
  settings.boardRotation = value * 5;
  writeStorage("boardRotation", settings.boardRotation);
  document.getElementById("rotationDegree").textContent = settings.boardRotation;
  resizeAll();
}

function initRender() {
  allcanvas = {
    front: {
      transform: {
        x: 0,
        y: 0,
        s: 1,
        panx: 0,
        pany: 0,
        zoom: 1,
      },
      pointerStates: {},
      anotherPointerTapped: false,
      bg: document.getElementById("F_bg"),
      fab: document.getElementById("F_fab"),
      silk: document.getElementById("F_slk"),
      highlight: document.getElementById("F_hl"),
      layer: "F",
    },
    back: {
      transform: {
        x: 0,
        y: 0,
        s: 1,
        panx: 0,
        pany: 0,
        zoom: 1,
      },
      pointerStates: {},
      anotherPointerTapped: false,
      bg: document.getElementById("B_bg"),
      fab: document.getElementById("B_fab"),
      silk: document.getElementById("B_slk"),
      highlight: document.getElementById("B_hl"),
      layer: "B",
    }
  };
  addMouseHandlers(document.getElementById("frontcanvas"), allcanvas.front);
  addMouseHandlers(document.getElementById("backcanvas"), allcanvas.back);
}

///////////////////////////////////////////////

///////////////////////////////////////////////
/*
 * Table reordering via Drag'n'Drop
 * Inspired by: https://htmldom.dev/drag-and-drop-table-column
 */

function setBomHandlers() {

  const bom = document.getElementById('bomtable');

  let dragName;
  let placeHolderElements;
  let draggingElement;
  let forcePopulation;
  let xOffset;
  let yOffset;
  let wasDragged;

  const mouseUpHandler = function(e) {
    // Delete dragging element
    draggingElement.remove();

    // Make BOM selectable again
    bom.style.removeProperty("userSelect");

    // Remove listeners
    document.removeEventListener('mousemove', mouseMoveHandler);
    document.removeEventListener('mouseup', mouseUpHandler);

    if (wasDragged) {
      // Redraw whole BOM
      populateBomTable();
    }
  }

  const mouseMoveHandler = function(e) {
    // Notice the dragging
    wasDragged = true;

    // Make the dragged element visible
    draggingElement.style.removeProperty("display");

    // Set elements position to mouse position
    draggingElement.style.left = `${e.screenX - xOffset}px`;
    draggingElement.style.top = `${e.screenY - yOffset}px`;

    // Forced redrawing of BOM table
    if (forcePopulation) {
      forcePopulation = false;
      // Copy array
      phe = Array.from(placeHolderElements);
      // populate BOM table again
      populateBomHeader(dragName, phe);
      populateBomBody(dragName, phe);
    }

    // Set up array of hidden columns
    var hiddenColumns = Array.from(settings.hiddenColumns);
    // In the ungrouped mode, quantity don't exist
    if (settings.bommode === "ungrouped")
      hiddenColumns.push("Quantity");
    // If no checkbox fields can be found, we consider them hidden
    if (settings.checkboxes.length == 0)
      hiddenColumns.push("checkboxes");

    // Get table headers and group them into checkboxes, extrafields and normal headers
    const bh = document.getElementById("bomhead");
    headers = Array.from(bh.querySelectorAll("th"))
    headers.shift() // numCol is not part of the columnOrder
    headerGroups = []
    lastCompoundClass = null;
    for (i = 0; i < settings.columnOrder.length; i++) {
      cElem = settings.columnOrder[i];
      if (hiddenColumns.includes(cElem)) {
        // Hidden columns appear as a dummy element
        headerGroups.push([]);
        continue;
      }
      elem = headers.filter(e => getColumnOrderName(e) === cElem)[0];
      if (elem.classList.contains("bom-checkbox")) {
        if (lastCompoundClass === "bom-checkbox") {
          cbGroup = headerGroups.pop();
          cbGroup.push(elem);
          headerGroups.push(cbGroup);
        } else {
          lastCompoundClass = "bom-checkbox";
          headerGroups.push([elem])
        }
      } else {
        headerGroups.push([elem])
      }
    }

    // Copy settings.columnOrder
    var columns = Array.from(settings.columnOrder)

    // Set up array with indices of hidden columns
    var hiddenIndices = hiddenColumns.map(e => settings.columnOrder.indexOf(e));
    var dragIndex = columns.indexOf(dragName);
    var swapIndex = dragIndex;
    var swapDone = false;

    // Check if the current dragged element is swapable with the left or right element
    if (dragIndex > 0) {
      // Get left headers boundingbox
      swapIndex = dragIndex - 1;
      while (hiddenIndices.includes(swapIndex) && swapIndex > 0)
        swapIndex--;
      if (!hiddenIndices.includes(swapIndex)) {
        box = getBoundingClientRectFromMultiple(headerGroups[swapIndex]);
        if (e.clientX < box.left + window.scrollX + (box.width / 2)) {
          swapElement = columns[dragIndex];
          columns.splice(dragIndex, 1);
          columns.splice(swapIndex, 0, swapElement);
          forcePopulation = true;
          swapDone = true;
        }
      }
    }
    if ((!swapDone) && dragIndex < headerGroups.length - 1) {
      // Get right headers boundingbox
      swapIndex = dragIndex + 1;
      while (hiddenIndices.includes(swapIndex))
        swapIndex++;
      if (swapIndex < headerGroups.length) {
        box = getBoundingClientRectFromMultiple(headerGroups[swapIndex]);
        if (e.clientX > box.left + window.scrollX + (box.width / 2)) {
          swapElement = columns[dragIndex];
          columns.splice(dragIndex, 1);
          columns.splice(swapIndex, 0, swapElement);
          forcePopulation = true;
          swapDone = true;
        }
      }
    }

    // Write back change to storage
    if (swapDone) {
      settings.columnOrder = columns
      writeStorage("columnOrder", JSON.stringify(columns));
    }

  }

  const mouseDownHandler = function(e) {
    var target = e.target;
    if (target.tagName.toLowerCase() != "td")
      target = target.parentElement;

    // Used to check if a dragging has ever happened
    wasDragged = false;

    // Create new element which will be displayed as the dragged column
    draggingElement = document.createElement("div")
    draggingElement.classList.add("dragging");
    draggingElement.style.display = "none";
    draggingElement.style.position = "absolute";
    draggingElement.style.overflow = "hidden";

    // Get bomhead and bombody elements
    const bh = document.getElementById("bomhead");
    const bb = document.getElementById("bombody");

    // Get all compound headers for the current column
    var compoundHeaders;
    if (target.classList.contains("bom-checkbox")) {
      compoundHeaders = Array.from(bh.querySelectorAll("th.bom-checkbox"));
    } else {
      compoundHeaders = [target];
    }

    // Create new table which will display the column
    var newTable = document.createElement("table");
    newTable.classList.add("bom");
    newTable.style.background = "white";
    draggingElement.append(newTable);

    // Create new header element
    var newHeader = document.createElement("thead");
    newTable.append(newHeader);

    // Set up array for storing all placeholder elements
    placeHolderElements = [];

    // Add all compound headers to the new thead element and placeholders
    compoundHeaders.forEach(function(h) {
      clone = cloneElementWithDimensions(h);
      newHeader.append(clone);
      placeHolderElements.push(clone);
    });

    // Create new body element
    var newBody = document.createElement("tbody");
    newTable.append(newBody);

    // Get indices for compound headers
    var idxs = compoundHeaders.map(e => getBomTableHeaderIndex(e));

    // For each row in the BOM body...
    var rows = bb.querySelectorAll("tr");
    rows.forEach(function(row) {
      // ..get the cells for the compound column
      const tds = row.querySelectorAll("td");
      var copytds = idxs.map(i => tds[i]);
      // Add them to the new element and the placeholders
      var newRow = document.createElement("tr");
      copytds.forEach(function(td) {
        clone = cloneElementWithDimensions(td);
        newRow.append(clone);
        placeHolderElements.push(clone);
      });
      newBody.append(newRow);
    });

    // Compute width for compound header
    var width = compoundHeaders.reduce((acc, x) => acc + x.clientWidth, 0);
    draggingElement.style.width = `${width}px`;

    // Insert the new dragging element and disable selection on BOM
    bom.insertBefore(draggingElement, null);
    bom.style.userSelect = "none";

    // Determine the mouse position offset
    xOffset = e.screenX - compoundHeaders.reduce((acc, x) => Math.min(acc, x.offsetLeft), compoundHeaders[0].offsetLeft);
    yOffset = e.screenY - compoundHeaders[0].offsetTop;

    // Get name for the column in settings.columnOrder
    dragName = getColumnOrderName(target);

    // Change text and class for placeholder elements
    placeHolderElements = placeHolderElements.map(function(e) {
      newElem = cloneElementWithDimensions(e);
      newElem.textContent = "";
      newElem.classList.add("placeholder");
      return newElem;
    });

    // On next mouse move, the whole BOM needs to be redrawn to show the placeholders
    forcePopulation = true;

    // Add listeners for move and up on mouse
    document.addEventListener('mousemove', mouseMoveHandler);
    document.addEventListener('mouseup', mouseUpHandler);
  }

  // In netlist mode, there is nothing to reorder
  if (settings.bommode === "netlist")
    return;

  // Add mouseDownHandler to every column except the numCol
  bom.querySelectorAll("th")
    .forEach(function(head) {
      if (!head.classList.contains("numCol")) {
        head.onmousedown = mouseDownHandler;
      }
    });

}

function getBoundingClientRectFromMultiple(elements) {
  var elems = Array.from(elements);

  if (elems.length == 0)
    return null;

  var box = elems.shift()
    .getBoundingClientRect();

  elems.forEach(function(elem) {
    var elembox = elem.getBoundingClientRect();
    box.left = Math.min(elembox.left, box.left);
    box.top = Math.min(elembox.top, box.top);
    box.width += elembox.width;
    box.height = Math.max(elembox.height, box.height);
  });

  return box;
}

function cloneElementWithDimensions(elem) {
  var newElem = elem.cloneNode(true);
  newElem.style.height = window.getComputedStyle(elem).height;
  newElem.style.width = window.getComputedStyle(elem).width;
  return newElem;
}

function getBomTableHeaderIndex(elem) {
  const bh = document.getElementById('bomhead');
  const ths = Array.from(bh.querySelectorAll("th"));
  return ths.indexOf(elem);
}

function getColumnOrderName(elem) {
  var cname = elem.getAttribute("col_name");
  if (cname === "bom-checkbox")
    return "checkboxes";
  else
    return cname;
}

function resizableGrid(tablehead) {
  var cols = tablehead.firstElementChild.children;
  var rowWidth = tablehead.offsetWidth;

  for (var i = 1; i < cols.length; i++) {
    if (cols[i].classList.contains("bom-checkbox"))
      continue;
    cols[i].style.width = ((cols[i].clientWidth - paddingDiff(cols[i])) * 100 / rowWidth) + '%';
  }

  for (var i = 1; i < cols.length - 1; i++) {
    var div = document.createElement('div');
    div.className = "column-width-handle";
    cols[i].appendChild(div);
    setListeners(div);
  }

  function setListeners(div) {
    var startX, curCol, nxtCol, curColWidth, nxtColWidth, rowWidth;

    div.addEventListener('mousedown', function(e) {
      e.preventDefault();
      e.stopPropagation();

      curCol = e.target.parentElement;
      nxtCol = curCol.nextElementSibling;
      startX = e.pageX;

      var padding = paddingDiff(curCol);

      rowWidth = curCol.parentElement.offsetWidth;
      curColWidth = curCol.clientWidth - padding;
      nxtColWidth = nxtCol.clientWidth - padding;
    });

    document.addEventListener('mousemove', function(e) {
      if (startX) {
        var diffX = e.pageX - startX;
        diffX = -Math.min(-diffX, curColWidth - 20);
        diffX = Math.min(diffX, nxtColWidth - 20);

        curCol.style.width = ((curColWidth + diffX) * 100 / rowWidth) + '%';
        nxtCol.style.width = ((nxtColWidth - diffX) * 100 / rowWidth) + '%';
        console.log(`${curColWidth + nxtColWidth} ${(curColWidth + diffX) * 100 / rowWidth + (nxtColWidth - diffX) * 100 / rowWidth}`);
      }
    });

    document.addEventListener('mouseup', function(e) {
      curCol = undefined;
      nxtCol = undefined;
      startX = undefined;
      nxtColWidth = undefined;
      curColWidth = undefined
    });
  }

  function paddingDiff(col) {

    if (getStyleVal(col, 'box-sizing') == 'border-box') {
      return 0;
    }

    var padLeft = getStyleVal(col, 'padding-left');
    var padRight = getStyleVal(col, 'padding-right');
    return (parseInt(padLeft) + parseInt(padRight));

  }

  function getStyleVal(elm, css) {
    return (window.getComputedStyle(elm, null).getPropertyValue(css))
  }
}

///////////////////////////////////////////////

///////////////////////////////////////////////
/* DOM manipulation and misc code */

var bomsplit;
var canvassplit;
var initDone = false;
var bomSortFunction = null;
var currentSortColumn = null;
var currentSortOrder = null;
var currentHighlightedRowId;
var highlightHandlers = [];
var footprintIndexToHandler = {};
var netsToHandler = {};
var markedFootprints = new Set();
var highlightedFootprints = [];
var highlightedNet = null;
var lastClicked;

function dbg(html) {
  dbgdiv.innerHTML = html;
}

function redrawIfInitDone() {
  if (initDone) {
    redrawCanvas(allcanvas.front);
    redrawCanvas(allcanvas.back);
  }
}

function padsVisible(value) {
  writeStorage("padsVisible", value);
  settings.renderPads = value;
  redrawIfInitDone();
}

function referencesVisible(value) {
  writeStorage("referencesVisible", value);
  settings.renderReferences = value;
  redrawIfInitDone();
}

function valuesVisible(value) {
  writeStorage("valuesVisible", value);
  settings.renderValues = value;
  redrawIfInitDone();
}

function tracksVisible(value) {
  writeStorage("tracksVisible", value);
  settings.renderTracks = value;
  redrawIfInitDone();
}

function zonesVisible(value) {
  writeStorage("zonesVisible", value);
  settings.renderZones = value;
  redrawIfInitDone();
}

function dnpOutline(value) {
  writeStorage("dnpOutline", value);
  settings.renderDnpOutline = value;
  redrawIfInitDone();
}

function setDarkMode(value) {
  if (value) {
    topmostdiv.classList.add("dark");
  } else {
    topmostdiv.classList.remove("dark");
  }
  writeStorage("darkmode", value);
  settings.darkMode = value;
  redrawIfInitDone();
}

function setShowBOMColumn(field, value) {
  if (field === "references") {
    var rl = document.getElementById("reflookup");
    rl.disabled = !value;
    if (!value) {
      rl.value = "";
      updateRefLookup("");
    }
  }

  var n = settings.hiddenColumns.indexOf(field);
  if (value) {
    if (n != -1) {
      settings.hiddenColumns.splice(n, 1);
    }
  } else {
    if (n == -1) {
      settings.hiddenColumns.push(field);
    }
  }

  writeStorage("hiddenColumns", JSON.stringify(settings.hiddenColumns));

  if (initDone) {
    populateBomTable();
  }

  redrawIfInitDone();
}


function setFullscreen(value) {
  if (value) {
    document.documentElement.requestFullscreen();
  } else {
    document.exitFullscreen();
  }
}

function fabricationVisible(value) {
  writeStorage("fabricationVisible", value);
  settings.renderFabrication = value;
  redrawIfInitDone();
}

function silkscreenVisible(value) {
  writeStorage("silkscreenVisible", value);
  settings.renderSilkscreen = value;
  redrawIfInitDone();
}

function setHighlightPin1(value) {
  writeStorage("highlightpin1", value);
  settings.highlightpin1 = value;
  redrawIfInitDone();
}

function getStoredCheckboxRefs(checkbox) {
  function convert(ref) {
    var intref = parseInt(ref);
    if (isNaN(intref)) {
      for (var i = 0; i < pcbdata.footprints.length; i++) {
        if (pcbdata.footprints[i].ref == ref) {
          return i;
        }
      }
      return -1;
    } else {
      return intref;
    }
  }
  if (!(checkbox in settings.checkboxStoredRefs)) {
    var val = readStorage("checkbox_" + checkbox);
    settings.checkboxStoredRefs[checkbox] = val ? val : "";
  }
  if (!settings.checkboxStoredRefs[checkbox]) {
    return new Set();
  } else {
    return new Set(settings.checkboxStoredRefs[checkbox].split(",").map(r => convert(r)).filter(a => a >= 0));
  }
}

function getCheckboxState(checkbox, references) {
  var storedRefsSet = getStoredCheckboxRefs(checkbox);
  var currentRefsSet = new Set(references.map(r => r[1]));
  // Get difference of current - stored
  var difference = new Set(currentRefsSet);
  for (ref of storedRefsSet) {
    difference.delete(ref);
  }
  if (difference.size == 0) {
    // All the current refs are stored
    return "checked";
  } else if (difference.size == currentRefsSet.size) {
    // None of the current refs are stored
    return "unchecked";
  } else {
    // Some of the refs are stored
    return "indeterminate";
  }
}

function setBomCheckboxState(checkbox, element, references) {
  var state = getCheckboxState(checkbox, references);
  element.checked = (state == "checked");
  element.indeterminate = (state == "indeterminate");
}

function createCheckboxChangeHandler(checkbox, references, row) {
  return function () {
    refsSet = getStoredCheckboxRefs(checkbox);
    var markWhenChecked = settings.markWhenChecked == checkbox;
    eventArgs = {
      checkbox: checkbox,
      refs: references,
    }
    if (this.checked) {
      // checkbox ticked
      for (var ref of references) {
        refsSet.add(ref[1]);
      }
      if (markWhenChecked) {
        row.classList.add("checked");
        for (var ref of references) {
          markedFootprints.add(ref[1]);
        }
        drawHighlights();
      }
      eventArgs.state = 'checked';
    } else {
      // checkbox unticked
      for (var ref of references) {
        refsSet.delete(ref[1]);
      }
      if (markWhenChecked) {
        row.classList.remove("checked");
        for (var ref of references) {
          markedFootprints.delete(ref[1]);
        }
        drawHighlights();
      }
      eventArgs.state = 'unchecked';
    }
    settings.checkboxStoredRefs[checkbox] = [...refsSet].join(",");
    writeStorage("checkbox_" + checkbox, settings.checkboxStoredRefs[checkbox]);
    updateCheckboxStats(checkbox);
    EventHandler.emitEvent(IBOM_EVENT_TYPES.CHECKBOX_CHANGE_EVENT, eventArgs);
  }
}

function clearHighlightedFootprints() {
  if (currentHighlightedRowId) {
    document.getElementById(currentHighlightedRowId).classList.remove("highlighted");
    currentHighlightedRowId = null;
    highlightedFootprints = [];
    highlightedNet = null;
  }
}

function createRowHighlightHandler(rowid, refs, net) {
  return function () {
    if (currentHighlightedRowId) {
      if (currentHighlightedRowId == rowid) {
        return;
      }
      document.getElementById(currentHighlightedRowId).classList.remove("highlighted");
    }
    document.getElementById(rowid).classList.add("highlighted");
    currentHighlightedRowId = rowid;
    highlightedFootprints = refs ? refs.map(r => r[1]) : [];
    highlightedNet = net;
    drawHighlights();
    EventHandler.emitEvent(
      IBOM_EVENT_TYPES.HIGHLIGHT_EVENT, {
      rowid: rowid,
      refs: refs,
      net: net
    });
  }
}

function entryMatches(entry) {
  if (settings.bommode == "netlist") {
    // entry is just a net name
    return entry.toLowerCase().indexOf(filter) >= 0;
  }
  // check refs
  if (!settings.hiddenColumns.includes("references")) {
    for (var ref of entry) {
      if (ref[0].toLowerCase().indexOf(filter) >= 0) {
        return true;
      }
    }
  }
  // check fields
  for (var i in config.fields) {
    var f = config.fields[i];
    if (!settings.hiddenColumns.includes(f)) {
      for (var ref of entry) {
        if (pcbdata.bom.fields[ref[1]][i].toLowerCase().indexOf(filter) >= 0) {
          return true;
        }
      }
    }
  }
  return false;
}

function findRefInEntry(entry) {
  return entry.filter(r => r[0].toLowerCase() == reflookup);
}

function highlightFilter(s) {
  if (!filter) {
    return s;
  }
  var parts = s.toLowerCase().split(filter);
  if (parts.length == 1) {
    return s;
  }
  var r = "";
  var pos = 0;
  for (var i in parts) {
    if (i > 0) {
      r += '<mark class="highlight">' +
        s.substring(pos, pos + filter.length) +
        '</mark>';
      pos += filter.length;
    }
    r += s.substring(pos, pos + parts[i].length);
    pos += parts[i].length;
  }
  return r;
}

function checkboxSetUnsetAllHandler(checkboxname) {
  return function () {
    var checkboxnum = 0;
    while (checkboxnum < settings.checkboxes.length &&
      settings.checkboxes[checkboxnum].toLowerCase() != checkboxname.toLowerCase()) {
      checkboxnum++;
    }
    if (checkboxnum >= settings.checkboxes.length) {
      return;
    }
    var allset = true;
    var checkbox;
    var row;
    for (row of bombody.childNodes) {
      checkbox = row.childNodes[checkboxnum + 1].childNodes[0];
      if (!checkbox.checked || checkbox.indeterminate) {
        allset = false;
        break;
      }
    }
    for (row of bombody.childNodes) {
      checkbox = row.childNodes[checkboxnum + 1].childNodes[0];
      checkbox.checked = !allset;
      checkbox.indeterminate = false;
      checkbox.onchange();
    }
  }
}

function createColumnHeader(name, cls, comparator, is_checkbox = false) {
  var th = document.createElement("TH");
  th.innerHTML = name;
  th.classList.add(cls);
  if (is_checkbox)
    th.setAttribute("col_name", "bom-checkbox");
  else
    th.setAttribute("col_name", name);
  var span = document.createElement("SPAN");
  span.classList.add("sortmark");
  span.classList.add("none");
  th.appendChild(span);
  var spacer = document.createElement("div");
  spacer.className = "column-spacer";
  th.appendChild(spacer);
  spacer.onclick = function () {
    if (currentSortColumn && th !== currentSortColumn) {
      // Currently sorted by another column
      currentSortColumn.childNodes[1].classList.remove(currentSortOrder);
      currentSortColumn.childNodes[1].classList.add("none");
      currentSortColumn = null;
      currentSortOrder = null;
    }
    if (currentSortColumn && th === currentSortColumn) {
      // Already sorted by this column
      if (currentSortOrder == "asc") {
        // Sort by this column, descending order
        bomSortFunction = function (a, b) {
          return -comparator(a, b);
        }
        currentSortColumn.childNodes[1].classList.remove("asc");
        currentSortColumn.childNodes[1].classList.add("desc");
        currentSortOrder = "desc";
      } else {
        // Unsort
        bomSortFunction = null;
        currentSortColumn.childNodes[1].classList.remove("desc");
        currentSortColumn.childNodes[1].classList.add("none");
        currentSortColumn = null;
        currentSortOrder = null;
      }
    } else {
      // Sort by this column, ascending order
      bomSortFunction = comparator;
      currentSortColumn = th;
      currentSortColumn.childNodes[1].classList.remove("none");
      currentSortColumn.childNodes[1].classList.add("asc");
      currentSortOrder = "asc";
    }
    populateBomBody();
  }
  if (is_checkbox) {
    spacer.onclick = fancyDblClickHandler(
      spacer, spacer.onclick, checkboxSetUnsetAllHandler(name));
  }
  return th;
}

function populateBomHeader(placeHolderColumn = null, placeHolderElements = null) {
  while (bomhead.firstChild) {
    bomhead.removeChild(bomhead.firstChild);
  }
  var tr = document.createElement("TR");
  var th = document.createElement("TH");
  th.classList.add("numCol");

  var vismenu = document.createElement("div");
  vismenu.id = "vismenu";
  vismenu.classList.add("menu");

  var visbutton = document.createElement("div");
  visbutton.classList.add("visbtn");
  visbutton.classList.add("hideonprint");

  var viscontent = document.createElement("div");
  viscontent.classList.add("menu-content");
  viscontent.id = "vismenu-content";

  settings.columnOrder.forEach(column => {
    if (typeof column !== "string")
      return;

    // Skip empty columns
    if (column === "checkboxes" && settings.checkboxes.length == 0)
      return;
    else if (column === "Quantity" && settings.bommode == "ungrouped")
      return;

    var label = document.createElement("label");
    label.classList.add("menu-label");

    var input = document.createElement("input");
    input.classList.add("visibility_checkbox");
    input.type = "checkbox";
    input.onchange = function (e) {
      setShowBOMColumn(column, e.target.checked)
    };
    input.checked = !(settings.hiddenColumns.includes(column));

    label.appendChild(input);
    if (column.length > 0)
      label.append(column[0].toUpperCase() + column.slice(1));

    viscontent.appendChild(label);
  });

  viscontent.childNodes[0].classList.add("menu-label-top");

  vismenu.appendChild(visbutton);
  if (settings.bommode != "netlist") {
    vismenu.appendChild(viscontent);
    th.appendChild(vismenu);
  }
  tr.appendChild(th);

  var checkboxCompareClosure = function (checkbox) {
    return (a, b) => {
      var stateA = getCheckboxState(checkbox, a);
      var stateB = getCheckboxState(checkbox, b);
      if (stateA > stateB) return -1;
      if (stateA < stateB) return 1;
      return 0;
    }
  }
  var stringFieldCompareClosure = function (fieldIndex) {
    return (a, b) => {
      var fa = pcbdata.bom.fields[a[0][1]][fieldIndex];
      var fb = pcbdata.bom.fields[b[0][1]][fieldIndex];
      if (fa != fb) return fa > fb ? 1 : -1;
      else return 0;
    }
  }
  var referenceRegex = /(?<prefix>[^0-9]+)(?<number>[0-9]+)/;
  var compareRefs = (a, b) => {
    var ra = referenceRegex.exec(a);
    var rb = referenceRegex.exec(b);
    if (ra === null || rb === null) {
      if (a != b) return a > b ? 1 : -1;
      return 0;
    } else {
      if (ra.groups.prefix != rb.groups.prefix) {
        return ra.groups.prefix > rb.groups.prefix ? 1 : -1;
      }
      if (ra.groups.number != rb.groups.number) {
        return parseInt(ra.groups.number) > parseInt(rb.groups.number) ? 1 : -1;
      }
      return 0;
    }
  }
  if (settings.bommode == "netlist") {
    th = createColumnHeader("Net name", "bom-netname", (a, b) => {
      if (a > b) return -1;
      if (a < b) return 1;
      return 0;
    });
    tr.appendChild(th);
  } else {
    // Filter hidden columns
    var columns = settings.columnOrder.filter(e => !settings.hiddenColumns.includes(e));
    var valueIndex = config.fields.indexOf("Value");
    var footprintIndex = config.fields.indexOf("Footprint");
    columns.forEach((column) => {
      if (column === placeHolderColumn) {
        var n = 1;
        if (column === "checkboxes")
          n = settings.checkboxes.length;
        for (i = 0; i < n; i++) {
          td = placeHolderElements.shift();
          tr.appendChild(td);
        }
        return;
      } else if (column === "checkboxes") {
        for (var checkbox of settings.checkboxes) {
          th = createColumnHeader(
            checkbox, "bom-checkbox", checkboxCompareClosure(checkbox), true);
          tr.appendChild(th);
        }
      } else if (column === "References") {
        tr.appendChild(createColumnHeader("References", "references", (a, b) => {
          var i = 0;
          while (i < a.length && i < b.length) {
            if (a[i] != b[i]) return compareRefs(a[i][0], b[i][0]);
            i++;
          }
          return a.length - b.length;
        }));
      } else if (column === "Value") {
        tr.appendChild(createColumnHeader("Value", "value", (a, b) => {
          var ra = a[0][1], rb = b[0][1];
          return valueCompare(
            pcbdata.bom.parsedValues[ra], pcbdata.bom.parsedValues[rb],
            pcbdata.bom.fields[ra][valueIndex], pcbdata.bom.fields[rb][valueIndex]);
        }));
        return;
      } else if (column === "Footprint") {
        tr.appendChild(createColumnHeader(
          "Footprint", "footprint", stringFieldCompareClosure(footprintIndex)));
      } else if (column === "Quantity" && settings.bommode == "grouped") {
        tr.appendChild(createColumnHeader("Quantity", "quantity", (a, b) => {
          return a.length - b.length;
        }));
      } else {
        // Other fields
        var i = config.fields.indexOf(column);
        if (i < 0)
          return;
        tr.appendChild(createColumnHeader(
          column, `field${i + 1}`, stringFieldCompareClosure(i)));
      }
    });
  }
  bomhead.appendChild(tr);
}

function populateBomBody(placeholderColumn = null, placeHolderElements = null) {
  while (bom.firstChild) {
    bom.removeChild(bom.firstChild);
  }
  highlightHandlers = [];
  footprintIndexToHandler = {};
  netsToHandler = {};
  currentHighlightedRowId = null;
  var first = true;
  if (settings.bommode == "netlist") {
    bomtable = pcbdata.nets.slice();
  } else {
    switch (settings.canvaslayout) {
      case 'F':
        bomtable = pcbdata.bom.F.slice();
        break;
      case 'FB':
        bomtable = pcbdata.bom.both.slice();
        break;
      case 'B':
        bomtable = pcbdata.bom.B.slice();
        break;
    }
    if (settings.bommode == "ungrouped") {
      // expand bom table
      expandedTable = []
      for (var bomentry of bomtable) {
        for (var ref of bomentry) {
          expandedTable.push([ref]);
        }
      }
      bomtable = expandedTable;
    }
  }
  if (bomSortFunction) {
    bomtable = bomtable.sort(bomSortFunction);
  }
  for (var i in bomtable) {
    var bomentry = bomtable[i];
    if (filter && !entryMatches(bomentry)) {
      continue;
    }
    var references = null;
    var netname = null;
    var tr = document.createElement("TR");
    var td = document.createElement("TD");
    var rownum = +i + 1;
    tr.id = "bomrow" + rownum;
    td.textContent = rownum;
    tr.appendChild(td);
    if (settings.bommode == "netlist") {
      netname = bomentry;
      td = document.createElement("TD");
      td.innerHTML = highlightFilter(netname ? netname : "&lt;no net&gt;");
      tr.appendChild(td);
    } else {
      if (reflookup) {
        references = findRefInEntry(bomentry);
        if (references.length == 0) {
          continue;
        }
      } else {
        references = bomentry;
      }
      // Filter hidden columns
      var columns = settings.columnOrder.filter(e => !settings.hiddenColumns.includes(e));
      columns.forEach((column) => {
        if (column === placeholderColumn) {
          var n = 1;
          if (column === "checkboxes")
            n = settings.checkboxes.length;
          for (i = 0; i < n; i++) {
            td = placeHolderElements.shift();
            tr.appendChild(td);
          }
          return;
        } else if (column === "checkboxes") {
          for (var checkbox of settings.checkboxes) {
            if (checkbox) {
              td = document.createElement("TD");
              var input = document.createElement("input");
              input.type = "checkbox";
              input.onchange = createCheckboxChangeHandler(checkbox, references, tr);
              setBomCheckboxState(checkbox, input, references);
              if (input.checked && settings.markWhenChecked == checkbox) {
                tr.classList.add("checked");
              }
              td.appendChild(input);
              tr.appendChild(td);
            }
          }
        } else if (column === "References") {
          td = document.createElement("TD");
          td.innerHTML = highlightFilter(references.map(r => r[0]).join(", "));
          tr.appendChild(td);
        } else if (column === "Quantity" && settings.bommode == "grouped") {
          // Quantity
          td = document.createElement("TD");
          td.textContent = references.length;
          tr.appendChild(td);
        } else {
          // All the other fields
          var field_index = config.fields.indexOf(column)
          if (field_index < 0)
            return;
          var valueSet = new Set();
          references.map(r => r[1]).forEach((id) => valueSet.add(pcbdata.bom.fields[id][field_index]));
          td = document.createElement("TD");
          td.innerHTML = highlightFilter(Array.from(valueSet).join(", "));
          tr.appendChild(td);
        }
      });
    }
    bom.appendChild(tr);
    var handler = createRowHighlightHandler(tr.id, references, netname);
    tr.onmousemove = handler;
    highlightHandlers.push({
      id: tr.id,
      handler: handler,
    });
    if (references !== null) {
      for (var refIndex of references.map(r => r[1])) {
        footprintIndexToHandler[refIndex] = handler;
      }
    }
    if (netname !== null) {
      netsToHandler[netname] = handler;
    }
    if ((filter || reflookup) && first) {
      handler();
      first = false;
    }
  }
  EventHandler.emitEvent(
    IBOM_EVENT_TYPES.BOM_BODY_CHANGE_EVENT, {
    filter: filter,
    reflookup: reflookup,
    checkboxes: settings.checkboxes,
    bommode: settings.bommode,
  });
}

function highlightPreviousRow() {
  if (!currentHighlightedRowId) {
    highlightHandlers[highlightHandlers.length - 1].handler();
  } else {
    if (highlightHandlers.length > 1 &&
      highlightHandlers[0].id == currentHighlightedRowId) {
      highlightHandlers[highlightHandlers.length - 1].handler();
    } else {
      for (var i = 0; i < highlightHandlers.length - 1; i++) {
        if (highlightHandlers[i + 1].id == currentHighlightedRowId) {
          highlightHandlers[i].handler();
          break;
        }
      }
    }
  }
  smoothScrollToRow(currentHighlightedRowId);
}

function highlightNextRow() {
  if (!currentHighlightedRowId) {
    highlightHandlers[0].handler();
  } else {
    if (highlightHandlers.length > 1 &&
      highlightHandlers[highlightHandlers.length - 1].id == currentHighlightedRowId) {
      highlightHandlers[0].handler();
    } else {
      for (var i = 1; i < highlightHandlers.length; i++) {
        if (highlightHandlers[i - 1].id == currentHighlightedRowId) {
          highlightHandlers[i].handler();
          break;
        }
      }
    }
  }
  smoothScrollToRow(currentHighlightedRowId);
}

function populateBomTable() {
  populateBomHeader();
  populateBomBody();
  setBomHandlers();
  resizableGrid(bomhead);
}

function footprintsClicked(footprintIndexes) {
  var lastClickedIndex = footprintIndexes.indexOf(lastClicked);
  for (var i = 1; i <= footprintIndexes.length; i++) {
    var refIndex = footprintIndexes[(lastClickedIndex + i) % footprintIndexes.length];
    if (refIndex in footprintIndexToHandler) {
      lastClicked = refIndex;
      footprintIndexToHandler[refIndex]();
      smoothScrollToRow(currentHighlightedRowId);
      break;
    }
  }
}

function netClicked(net) {
  if (net in netsToHandler) {
    netsToHandler[net]();
    smoothScrollToRow(currentHighlightedRowId);
  } else {
    clearHighlightedFootprints();
    highlightedNet = net;
    drawHighlights();
  }
}

function updateFilter(input) {
  filter = input.toLowerCase();
  populateBomTable();
}

function updateRefLookup(input) {
  reflookup = input.toLowerCase();
  populateBomTable();
}

function changeCanvasLayout(layout) {
  document.getElementById("fl-btn").classList.remove("depressed");
  document.getElementById("fb-btn").classList.remove("depressed");
  document.getElementById("bl-btn").classList.remove("depressed");
  switch (layout) {
    case 'F':
      document.getElementById("fl-btn").classList.add("depressed");
      if (settings.bomlayout != "bom-only") {
        canvassplit.collapse(1);
      }
      break;
    case 'B':
      document.getElementById("bl-btn").classList.add("depressed");
      if (settings.bomlayout != "bom-only") {
        canvassplit.collapse(0);
      }
      break;
    default:
      document.getElementById("fb-btn").classList.add("depressed");
      if (settings.bomlayout != "bom-only") {
        canvassplit.setSizes([50, 50]);
      }
  }
  settings.canvaslayout = layout;
  writeStorage("canvaslayout", layout);
  resizeAll();
  changeBomMode(settings.bommode);
}

function populateMetadata() {
  document.getElementById("title").innerHTML = pcbdata.metadata.title;
  document.getElementById("revision").innerHTML = "Rev: " + pcbdata.metadata.revision;
  document.getElementById("company").innerHTML = pcbdata.metadata.company;
  document.getElementById("filedate").innerHTML = pcbdata.metadata.date;
  if (pcbdata.metadata.title != "") {
    document.title = pcbdata.metadata.title + " BOM";
  }
  // Calculate board stats
  var fp_f = 0,
    fp_b = 0,
    pads_f = 0,
    pads_b = 0,
    pads_th = 0;
  for (var i = 0; i < pcbdata.footprints.length; i++) {
    if (pcbdata.bom.skipped.includes(i)) continue;
    var mod = pcbdata.footprints[i];
    if (mod.layer == "F") {
      fp_f++;
    } else {
      fp_b++;
    }
    for (var pad of mod.pads) {
      if (pad.type == "th") {
        pads_th++;
      } else {
        if (pad.layers.includes("F")) {
          pads_f++;
        }
        if (pad.layers.includes("B")) {
          pads_b++;
        }
      }
    }
  }
  document.getElementById("stats-components-front").innerHTML = fp_f;
  document.getElementById("stats-components-back").innerHTML = fp_b;
  document.getElementById("stats-components-total").innerHTML = fp_f + fp_b;
  document.getElementById("stats-groups-front").innerHTML = pcbdata.bom.F.length;
  document.getElementById("stats-groups-back").innerHTML = pcbdata.bom.B.length;
  document.getElementById("stats-groups-total").innerHTML = pcbdata.bom.both.length;
  document.getElementById("stats-smd-pads-front").innerHTML = pads_f;
  document.getElementById("stats-smd-pads-back").innerHTML = pads_b;
  document.getElementById("stats-smd-pads-total").innerHTML = pads_f + pads_b;
  document.getElementById("stats-th-pads").innerHTML = pads_th;
  // Update version string
  document.getElementById("github-link").innerHTML = "InteractiveHtmlBom&nbsp;" +
    /^v\d+\.\d+/.exec(pcbdata.ibom_version)[0];
}

function changeBomLayout(layout) {
  document.getElementById("bom-btn").classList.remove("depressed");
  document.getElementById("lr-btn").classList.remove("depressed");
  document.getElementById("tb-btn").classList.remove("depressed");
  switch (layout) {
    case 'bom-only':
      document.getElementById("bom-btn").classList.add("depressed");
      if (bomsplit) {
        bomsplit.destroy();
        bomsplit = null;
        canvassplit.destroy();
        canvassplit = null;
      }
      document.getElementById("frontcanvas").style.display = "none";
      document.getElementById("backcanvas").style.display = "none";
      document.getElementById("bot").style.height = "";
      break;
    case 'top-bottom':
      document.getElementById("tb-btn").classList.add("depressed");
      document.getElementById("frontcanvas").style.display = "";
      document.getElementById("backcanvas").style.display = "";
      document.getElementById("bot").style.height = "calc(100% - 80px)";
      document.getElementById("bomdiv").classList.remove("split-horizontal");
      document.getElementById("canvasdiv").classList.remove("split-horizontal");
      document.getElementById("frontcanvas").classList.add("split-horizontal");
      document.getElementById("backcanvas").classList.add("split-horizontal");
      if (bomsplit) {
        bomsplit.destroy();
        bomsplit = null;
        canvassplit.destroy();
        canvassplit = null;
      }
      bomsplit = Split(['#bomdiv', '#canvasdiv'], {
        sizes: [50, 50],
        onDragEnd: resizeAll,
        direction: "vertical",
        gutterSize: 5
      });
      canvassplit = Split(['#frontcanvas', '#backcanvas'], {
        sizes: [50, 50],
        gutterSize: 5,
        onDragEnd: resizeAll
      });
      break;
    case 'left-right':
      document.getElementById("lr-btn").classList.add("depressed");
      document.getElementById("frontcanvas").style.display = "";
      document.getElementById("backcanvas").style.display = "";
      document.getElementById("bot").style.height = "calc(100% - 80px)";
      document.getElementById("bomdiv").classList.add("split-horizontal");
      document.getElementById("canvasdiv").classList.add("split-horizontal");
      document.getElementById("frontcanvas").classList.remove("split-horizontal");
      document.getElementById("backcanvas").classList.remove("split-horizontal");
      if (bomsplit) {
        bomsplit.destroy();
        bomsplit = null;
        canvassplit.destroy();
        canvassplit = null;
      }
      bomsplit = Split(['#bomdiv', '#canvasdiv'], {
        sizes: [50, 50],
        onDragEnd: resizeAll,
        gutterSize: 5
      });
      canvassplit = Split(['#frontcanvas', '#backcanvas'], {
        sizes: [50, 50],
        gutterSize: 5,
        direction: "vertical",
        onDragEnd: resizeAll
      });
  }
  settings.bomlayout = layout;
  writeStorage("bomlayout", layout);
  changeCanvasLayout(settings.canvaslayout);
}

function changeBomMode(mode) {
  document.getElementById("bom-grouped-btn").classList.remove("depressed");
  document.getElementById("bom-ungrouped-btn").classList.remove("depressed");
  document.getElementById("bom-netlist-btn").classList.remove("depressed");
  var chkbxs = document.getElementsByClassName("visibility_checkbox");

  switch (mode) {
    case 'grouped':
      document.getElementById("bom-grouped-btn").classList.add("depressed");
      for (var i = 0; i < chkbxs.length; i++) {
        chkbxs[i].disabled = false;
      }
      break;
    case 'ungrouped':
      document.getElementById("bom-ungrouped-btn").classList.add("depressed");
      for (var i = 0; i < chkbxs.length; i++) {
        chkbxs[i].disabled = false;
      }
      break;
    case 'netlist':
      document.getElementById("bom-netlist-btn").classList.add("depressed");
      for (var i = 0; i < chkbxs.length; i++) {
        chkbxs[i].disabled = true;
      }
  }

  writeStorage("bommode", mode);
  if (mode != settings.bommode) {
    settings.bommode = mode;
    bomSortFunction = null;
    currentSortColumn = null;
    currentSortOrder = null;
    clearHighlightedFootprints();
  }
  populateBomTable();
}

function focusFilterField() {
  focusInputField(document.getElementById("filter"));
}

function focusRefLookupField() {
  focusInputField(document.getElementById("reflookup"));
}

function toggleBomCheckbox(bomrowid, checkboxnum) {
  if (!bomrowid || checkboxnum > settings.checkboxes.length) {
    return;
  }
  var bomrow = document.getElementById(bomrowid);
  var checkbox = bomrow.childNodes[checkboxnum].childNodes[0];
  checkbox.checked = !checkbox.checked;
  checkbox.indeterminate = false;
  checkbox.onchange();
}

function checkBomCheckbox(bomrowid, checkboxname) {
  var checkboxnum = 0;
  while (checkboxnum < settings.checkboxes.length &&
    settings.checkboxes[checkboxnum].toLowerCase() != checkboxname.toLowerCase()) {
    checkboxnum++;
  }
  if (!bomrowid || checkboxnum >= settings.checkboxes.length) {
    return;
  }
  var bomrow = document.getElementById(bomrowid);
  var checkbox = bomrow.childNodes[checkboxnum + 1].childNodes[0];
  checkbox.checked = true;
  checkbox.indeterminate = false;
  checkbox.onchange();
}

function setBomCheckboxes(value) {
  writeStorage("bomCheckboxes", value);
  settings.checkboxes = value.split(",").map((e) => e.trim()).filter((e) => e);
  prepCheckboxes();
  populateMarkWhenCheckedOptions();
  setMarkWhenChecked(settings.markWhenChecked);
}

function setMarkWhenChecked(value) {
  writeStorage("markWhenChecked", value);
  settings.markWhenChecked = value;
  markedFootprints.clear();
  for (var ref of (value ? getStoredCheckboxRefs(value) : [])) {
    markedFootprints.add(ref);
  }
  populateBomTable();
  drawHighlights();
}

function prepCheckboxes() {
  var table = document.getElementById("checkbox-stats");
  while (table.childElementCount > 1) {
    table.removeChild(table.lastChild);
  }
  if (settings.checkboxes.length) {
    table.style.display = "";
  } else {
    table.style.display = "none";
  }
  for (var checkbox of settings.checkboxes) {
    var tr = document.createElement("TR");
    var td = document.createElement("TD");
    td.innerHTML = checkbox;
    tr.appendChild(td);
    td = document.createElement("TD");
    td.id = "checkbox-stats-" + checkbox;
    var progressbar = document.createElement("div");
    progressbar.classList.add("bar");
    td.appendChild(progressbar);
    var text = document.createElement("div");
    text.classList.add("text");
    td.appendChild(text);
    tr.appendChild(td);
    table.appendChild(tr);
    updateCheckboxStats(checkbox);
  }
}

function populateMarkWhenCheckedOptions() {
  var container = document.getElementById("markWhenCheckedContainer");

  if (settings.checkboxes.length == 0) {
    container.parentElement.style.display = "none";
    return;
  }

  container.innerHTML = '';
  container.parentElement.style.display = "inline-block";

  function createOption(name, displayName) {
    var id = "markWhenChecked-" + name;

    var div = document.createElement("div");
    div.classList.add("radio-container");

    var input = document.createElement("input");
    input.type = "radio";
    input.name = "markWhenChecked";
    input.value = name;
    input.id = id;
    input.onchange = () => setMarkWhenChecked(name);
    div.appendChild(input);

    // Preserve the selected element when the checkboxes change
    if (name == settings.markWhenChecked) {
      input.checked = true;
    }

    var label = document.createElement("label");
    label.innerHTML = displayName;
    label.htmlFor = id;
    div.appendChild(label);

    container.appendChild(div);
  }
  createOption("", "None");
  for (var checkbox of settings.checkboxes) {
    createOption(checkbox, checkbox);
  }
}

function updateCheckboxStats(checkbox) {
  var checked = getStoredCheckboxRefs(checkbox).size;
  var total = pcbdata.footprints.length - pcbdata.bom.skipped.length;
  var percent = checked * 100.0 / total;
  var td = document.getElementById("checkbox-stats-" + checkbox);
  td.firstChild.style.width = percent + "%";
  td.lastChild.innerHTML = checked + "/" + total + " (" + Math.round(percent) + "%)";
}

function constrain(number, min, max){
  return Math.min(Math.max(parseInt(number), min), max);
}

document.onkeydown = function (e) {
  switch (e.key) {
    case "n":
      if (document.activeElement.type == "text") {
        return;
      }
      if (currentHighlightedRowId !== null) {
        checkBomCheckbox(currentHighlightedRowId, "placed");
        highlightNextRow();
        e.preventDefault();
      }
      break;
    case "ArrowUp":
      highlightPreviousRow();
      e.preventDefault();
      break;
    case "ArrowDown":
      highlightNextRow();
      e.preventDefault();
      break;
    case "ArrowLeft":
    case "ArrowRight":
      if (document.activeElement.type != "text"){
        e.preventDefault();
        let boardRotationElement = document.getElementById("boardRotation")
        settings.boardRotation = parseInt(boardRotationElement.value);  // degrees / 5
        if (e.key == "ArrowLeft"){
            settings.boardRotation += 3;  // 15 degrees
        }
        else{
            settings.boardRotation -= 3;
        }
        settings.boardRotation = constrain(settings.boardRotation, boardRotationElement.min, boardRotationElement.max);
        boardRotationElement.value = settings.boardRotation
        setBoardRotation(settings.boardRotation);
      }
      break;      
    default:
      break;
  }
  if (e.altKey) {
    switch (e.key) {
      case "f":
        focusFilterField();
        e.preventDefault();
        break;
      case "r":
        focusRefLookupField();
        e.preventDefault();
        break;
      case "z":
        changeBomLayout("bom-only");
        e.preventDefault();
        break;
      case "x":
        changeBomLayout("left-right");
        e.preventDefault();
        break;
      case "c":
        changeBomLayout("top-bottom");
        e.preventDefault();
        break;
      case "v":
        changeCanvasLayout("F");
        e.preventDefault();
        break;
      case "b":
        changeCanvasLayout("FB");
        e.preventDefault();
        break;
      case "n":
        changeCanvasLayout("B");
        e.preventDefault();
        break;
      default:
        break;
    }
    if (e.key >= '1' && e.key <= '9') {
      toggleBomCheckbox(currentHighlightedRowId, parseInt(e.key));
      e.preventDefault();
    }
  }
}

function hideNetlistButton() {
  document.getElementById("bom-ungrouped-btn").classList.remove("middle-button");
  document.getElementById("bom-ungrouped-btn").classList.add("right-most-button");
  document.getElementById("bom-netlist-btn").style.display = "none";
}

window.onload = function (e) {
  initUtils();
  initRender();
  initStorage();
  initDefaults();
  cleanGutters();
  populateMetadata();
  dbgdiv = document.getElementById("dbg");
  bom = document.getElementById("bombody");
  bomhead = document.getElementById("bomhead");
  filter = "";
  reflookup = "";
  if (!("nets" in pcbdata)) {
    hideNetlistButton();
  }
  initDone = true;
  setBomCheckboxes(document.getElementById("bomCheckboxes").value);
  // Triggers render
  changeBomLayout(settings.bomlayout);

  // Users may leave fullscreen without touching the checkbox. Uncheck.
  document.addEventListener('fullscreenchange', () => {
    if (!document.fullscreenElement)
      document.getElementById('fullscreenCheckbox').checked = false;
  });
}

window.onresize = resizeAll;
window.matchMedia("print").addListener(resizeAll);

///////////////////////////////////////////////

///////////////////////////////////////////////

///////////////////////////////////////////////
  </script>
</head>

<body>

<div id="topmostdiv" class="topmostdiv">
  <div id="top">
    <div style="float: right; height: 100%;">
      <div class="hideonprint menu" style="float: right; top: 8px;">
        <button class="menubtn"></button>
        <div class="menu-content">
          <label class="menu-label menu-label-top" style="width: calc(50% - 18px)">
            <input id="darkmodeCheckbox" type="checkbox" onchange="setDarkMode(this.checked)">
            Dark mode
          </label><!-- This comment eats space! All of it!
          --><label class="menu-label menu-label-top" style="width: calc(50% - 17px); border-left: 0;">
            <input id="fullscreenCheckbox" type="checkbox" onchange="setFullscreen(this.checked)">
            Full Screen
          </label>
          <label class="menu-label" style="width: calc(50% - 18px)">
            <input id="fabricationCheckbox" type="checkbox" checked onchange="fabricationVisible(this.checked)">
            Fab layer
          </label><!-- This comment eats space! All of it!
          --><label class="menu-label" style="width: calc(50% - 17px); border-left: 0;">
            <input id="silkscreenCheckbox" type="checkbox" checked onchange="silkscreenVisible(this.checked)">
            Silkscreen
          </label>
          <label class="menu-label" style="width: calc(50% - 18px)">
            <input id="referencesCheckbox" type="checkbox" checked onchange="referencesVisible(this.checked)">
            References
          </label><!-- This comment eats space! All of it!
          --><label class="menu-label" style="width: calc(50% - 17px); border-left: 0;">
            <input id="valuesCheckbox" type="checkbox" checked onchange="valuesVisible(this.checked)">
            Values
          </label>
          <div id="tracksAndZonesCheckboxes">
            <label class="menu-label" style="width: calc(50% - 18px)">
              <input id="tracksCheckbox" type="checkbox" checked onchange="tracksVisible(this.checked)">
              Tracks
            </label><!-- This comment eats space! All of it!
            --><label class="menu-label" style="width: calc(50% - 17px); border-left: 0;">
              <input id="zonesCheckbox" type="checkbox" checked onchange="zonesVisible(this.checked)">
              Zones
            </label>
          </div>
          <label class="menu-label" style="width: calc(50% - 18px)">
            <input id="padsCheckbox" type="checkbox" checked onchange="padsVisible(this.checked)">
            Pads
          </label><!-- This comment eats space! All of it!
          --><label class="menu-label" style="width: calc(50% - 17px); border-left: 0;">
            <input id="dnpOutlineCheckbox" type="checkbox" checked onchange="dnpOutline(this.checked)">
            DNP outlined
          </label>
          <label class="menu-label">
            <input id="highlightpin1Checkbox" type="checkbox" onchange="setHighlightPin1(this.checked)">
            Highlight first pin
          </label>
          <label class="menu-label">
            <input id="dragCheckbox" type="checkbox" checked onchange="setRedrawOnDrag(this.checked)">
            Continuous redraw on drag
          </label>
          <label class="menu-label">
            <span>Board rotation</span>
            <span style="float: right"><span id="rotationDegree">0</span>&#176;</span>
            <input id="boardRotation" type="range" min="-36" max="36" value="0" class="slider" oninput="setBoardRotation(this.value)">
          </label>
          <label class="menu-label">
            <div style="margin-left: 5px">Bom checkboxes</div>
            <input id="bomCheckboxes" class="menu-textbox" type=text
                   oninput="setBomCheckboxes(this.value)">
          </label>
          <label class="menu-label">
            <div style="margin-left: 5px">Mark when checked</div>
            <div id="markWhenCheckedContainer"></div>
          </label>
          <label class="menu-label">
            <span class="shameless-plug">
              <span>Created using</span>
              <a id="github-link" target="blank" href="https://github.com/openscopeproject/InteractiveHtmlBom">InteractiveHtmlBom</a>
              <a target="blank" title="Mouse and keyboard help" href="https://github.com/openscopeproject/InteractiveHtmlBom/wiki/Usage#bom-page-mouse-actions" style="text-decoration: none;"><label class="help-link">?</label></a>
            </span>
          </label>
        </div>
      </div>
      <div class="button-container hideonprint"
           style="float: right; position: relative; top: 8px">
        <button id="fl-btn" class="left-most-button" onclick="changeCanvasLayout('F')"
                title="Front only">F
        </button>
        <button id="fb-btn" class="middle-button" onclick="changeCanvasLayout('FB')"
                title="Front and Back">FB
        </button>
        <button id="bl-btn" class="right-most-button" onclick="changeCanvasLayout('B')"
                title="Back only">B
        </button>
      </div>
      <div class="button-container hideonprint"
           style="float: right; position: relative; top: 8px">
        <button id="bom-btn" class="left-most-button" onclick="changeBomLayout('bom-only')"
                title="BOM only"></button>
        <button id="lr-btn" class="middle-button" onclick="changeBomLayout('left-right')"
                title="BOM left, drawings right"></button>
        <button id="tb-btn" class="right-most-button" onclick="changeBomLayout('top-bottom')"
                title="BOM top, drawings bot"></button>
      </div>
      <div class="button-container hideonprint"
           style="float: right; position: relative; top: 8px">
        <button id="bom-grouped-btn" class="left-most-button" onclick="changeBomMode('grouped')"
                title="Grouped BOM"></button>
        <button id="bom-ungrouped-btn" class="middle-button" onclick="changeBomMode('ungrouped')"
                title="Ungrouped BOM"></button>
        <button id="bom-netlist-btn" class="right-most-button" onclick="changeBomMode('netlist')"
                title="Netlist"></button>
      </div>
      <div class="hideonprint menu" style="float: right; top: 8px;">
        <button class="statsbtn"></button>
        <div class="menu-content">
          <table class="stats">
            <tbody>
              <tr>
                <td width="40%">Board stats</td>
                <td>Front</td>
                <td>Back</td>
                <td>Total</td>
              </tr>
              <tr>
                <td>Components</td>
                <td id="stats-components-front">~</td>
                <td id="stats-components-back">~</td>
                <td id="stats-components-total">~</td>
              </tr>
              <tr>
                <td>Groups</td>
                <td id="stats-groups-front">~</td>
                <td id="stats-groups-back">~</td>
                <td id="stats-groups-total">~</td>
              </tr>
              <tr>
                <td>SMD pads</td>
                <td id="stats-smd-pads-front">~</td>
                <td id="stats-smd-pads-back">~</td>
                <td id="stats-smd-pads-total">~</td>
              </tr>
              <tr>
                <td>TH pads</td>
                <td colspan=3 id="stats-th-pads">~</td>
              </tr>
            </tbody>
          </table>
          <table class="stats">
            <col width="40%"/><col />
            <tbody id="checkbox-stats">
              <tr>
                <td colspan=2 style="border-top: 0">Checkboxes</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="hideonprint menu" style="float: right; top: 8px;">
        <button class="iobtn"></button>
        <div class="menu-content">
          <div class="menu-label menu-label-top">
            <div style="margin-left: 5px;">Save board image</div>
            <div class="flexbox">
              <input id="render-save-width" class="menu-textbox" type="text" value="1000" placeholder="Width"
                  style="flex-grow: 1; width: 50px;" oninput="validateSaveImgDimension(this)">
              <span>X</span>
              <input id="render-save-height" class="menu-textbox" type="text" value="1000" placeholder="Height"
                  style="flex-grow: 1; width: 50px;" oninput="validateSaveImgDimension(this)">
            </div>
            <label>
              <input id="render-save-transparent" type="checkbox">
              Transparent background
            </label>
            <div class="flexbox">
              <button class="savebtn" onclick="saveImage('F')">Front</button>
              <button class="savebtn" onclick="saveImage('B')">Back</button>
            </div>
          </div>
          <div class="menu-label">
            <span style="margin-left: 5px;">Config and checkbox state</span>
            <div class="flexbox">
              <button class="savebtn" onclick="saveSettings()">Export</button>
              <button class="savebtn" onclick="loadSettings()">Import</button>
            </div>
          </div>
          <div class="menu-label">
            <span style="margin-left: 5px;">Save bom table as</span>
            <div class="flexbox">
              <button class="savebtn" onclick="saveBomTable('csv')">csv</button>
              <button class="savebtn" onclick="saveBomTable('txt')">txt</button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="fileinfodiv" style="overflow: auto;">
      <table class="fileinfo">
        <tbody>
          <tr>
            <td id="title" class="title" style="width: 70%">
              Title
            </td>
            <td id="revision" class="title" style="width: 30%">
              Revision
            </td>
          </tr>
          <tr>
            <td id="company">
              Company
            </td>
            <td id="filedate">
              Date
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div id="bot" class="split" style="height: calc(100% - 80px)">
    <div id="bomdiv" class="split split-horizontal">
      <div style="width: 100%">
        <input id="reflookup" class="textbox searchbox reflookup hideonprint" type="text" placeholder="Ref lookup"
               oninput="updateRefLookup(this.value)">
        <input id="filter" class="textbox searchbox filter hideonprint" type="text" placeholder="Filter"
               oninput="updateFilter(this.value)">
        <div class="button-container hideonprint" style="float: left; margin: 0;">
          <button id="copy" title="Copy bom table to clipboard"
               onclick="saveBomTable('clipboard')"></button>
        </div>
      </div>
      <div id="dbg"></div>
      <table class="bom" id="bomtable">
        <thead id="bomhead">
        </thead>
        <tbody id="bombody">
        </tbody>
      </table>
    </div>
    <div id="canvasdiv" class="split split-horizontal">
      <div id="frontcanvas" class="split" touch-action="none" style="overflow: hidden">
        <div style="position: relative; width: 100%; height: 100%;">
          <canvas id="F_bg" style="position: absolute; left: 0; top: 0; z-index: 0;"></canvas>
          <canvas id="F_fab" style="position: absolute; left: 0; top: 0; z-index: 1;"></canvas>
          <canvas id="F_slk" style="position: absolute; left: 0; top: 0; z-index: 2;"></canvas>
          <canvas id="F_hl" style="position: absolute; left: 0; top: 0; z-index: 3;"></canvas>
        </div>
      </div>
      <div id="backcanvas" class="split" touch-action="none" style="overflow: hidden">
        <div style="position: relative; width: 100%; height: 100%;">
          <canvas id="B_bg" style="position: absolute; left: 0; top: 0; z-index: 0;"></canvas>
          <canvas id="B_fab" style="position: absolute; left: 0; top: 0; z-index: 1;"></canvas>
          <canvas id="B_slk" style="position: absolute; left: 0; top: 0; z-index: 2;"></canvas>
          <canvas id="B_hl" style="position: absolute; left: 0; top: 0; z-index: 3;"></canvas>
        </div>
      </div>
    </div>
  </div>
</div>

</body>

</html>
