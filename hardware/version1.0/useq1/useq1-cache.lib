EESchema-LIBRARY Version 2.4
#encoding utf-8
#
# 74xx_74HC245
#
DEF 74xx_74HC245 U 0 40 Y Y 1 L N
F0 "U" -300 650 50 H V C CNN
F1 "74xx_74HC245" -300 -650 50 H V C CNN
F2 "" 0 0 50 H I C CNN
F3 "" 0 0 50 H I C CNN
ALIAS 74HC245
$FPLIST
 DIP?20*
$ENDFPLIST
DRAW
S -300 600 300 -600 1 1 10 f
P 3 1 0 0 -25 -50 -25 50 25 50 N
P 4 1 0 0 -50 -50 25 -50 25 50 50 50 N
X A->B 1 -500 -400 200 R 50 50 1 0 I
X GND 10 0 -800 200 U 50 50 1 0 W
X B7 11 500 -200 200 L 50 50 1 0 T
X B6 12 500 -100 200 L 50 50 1 0 T
X B5 13 500 0 200 L 50 50 1 0 T
X B4 14 500 100 200 L 50 50 1 0 T
X B3 15 500 200 200 L 50 50 1 0 T
X B2 16 500 300 200 L 50 50 1 0 T
X B1 17 500 400 200 L 50 50 1 0 T
X B0 18 500 500 200 L 50 50 1 0 T
X CE 19 -500 -500 200 R 50 50 1 0 I I
X A0 2 -500 500 200 R 50 50 1 0 T
X VCC 20 0 800 200 D 50 50 1 0 W
X A1 3 -500 400 200 R 50 50 1 0 T
X A2 4 -500 300 200 R 50 50 1 0 T
X A3 5 -500 200 200 R 50 50 1 0 T
X A4 6 -500 100 200 R 50 50 1 0 T
X A5 7 -500 0 200 R 50 50 1 0 T
X A6 8 -500 -100 200 R 50 50 1 0 T
X A7 9 -500 -200 200 R 50 50 1 0 T
ENDDRAW
ENDDEF
#
# Device_CP
#
DEF Device_CP C 0 10 N Y 1 F N
F0 "C" 25 100 50 H V L CNN
F1 "Device_CP" 25 -100 50 H V L CNN
F2 "" 38 -150 50 H I C CNN
F3 "" 0 0 50 H I C CNN
$FPLIST
 CP_*
$ENDFPLIST
DRAW
S -90 20 90 40 0 1 0 N
S 90 -20 -90 -40 0 1 0 F
P 2 0 1 0 -70 90 -30 90 N
P 2 0 1 0 -50 110 -50 70 N
X ~ 1 0 150 110 D 50 50 1 1 P
X ~ 2 0 -150 110 U 50 50 1 1 P
ENDDRAW
ENDDEF
#
# Device_CP1
#
DEF Device_CP1 C 0 10 N N 1 F N
F0 "C" 25 100 50 H V L CNN
F1 "Device_CP1" 25 -100 50 H V L CNN
F2 "" 0 0 50 H I C CNN
F3 "" 0 0 50 H I C CNN
$FPLIST
 CP_*
$ENDFPLIST
DRAW
A 0 -150 128 1287 513 0 1 20 N -80 -50 80 -50
P 2 0 1 20 -80 30 80 30 N
P 2 0 1 0 -70 90 -30 90 N
P 2 0 1 0 -50 70 -50 110 N
X ~ 1 0 150 110 D 50 50 1 1 P
X ~ 2 0 -150 130 U 50 50 1 1 P
ENDDRAW
ENDDEF
#
# Device_Ferrite_Bead
#
DEF Device_Ferrite_Bead FB 0 0 N Y 1 F N
F0 "FB" -150 25 50 V V C CNN
F1 "Device_Ferrite_Bead" 150 0 50 V V C CNN
F2 "" -70 0 50 V I C CNN
F3 "" 0 0 50 H I C CNN
$FPLIST
 Inductor_*
 L_*
 *Ferrite*
$ENDFPLIST
DRAW
P 2 0 1 0 0 -50 0 -48 N
P 2 0 1 0 0 50 0 51 N
P 5 0 1 0 -109 16 -67 89 109 -12 66 -85 -109 16 N
X ~ 1 0 150 100 D 50 50 1 1 P
X ~ 2 0 -150 100 U 50 50 1 1 P
ENDDRAW
ENDDEF
#
# Device_LED
#
DEF Device_LED D 0 40 N N 1 F N
F0 "D" 0 100 50 H V C CNN
F1 "Device_LED" 0 -100 50 H V C CNN
F2 "" 0 0 50 H I C CNN
F3 "" 0 0 50 H I C CNN
$FPLIST
 LED*
 LED_SMD:*
 LED_THT:*
$ENDFPLIST
DRAW
P 2 0 1 10 -50 -50 -50 50 N
P 2 0 1 0 -50 0 50 0 N
P 4 0 1 10 50 -50 50 50 -50 0 50 -50 N
P 5 0 1 0 -120 -30 -180 -90 -150 -90 -180 -90 -180 -60 N
P 5 0 1 0 -70 -30 -130 -90 -100 -90 -130 -90 -130 -60 N
X K 1 -150 0 100 R 50 50 1 1 P
X A 2 150 0 100 L 50 50 1 1 P
ENDDRAW
ENDDEF
#
# Device_R
#
DEF Device_R R 0 0 N Y 1 F N
F0 "R" 80 0 50 V V C CNN
F1 "Device_R" 0 0 50 V V C CNN
F2 "" -70 0 50 V I C CNN
F3 "" 0 0 50 H I C CNN
$FPLIST
 R_*
$ENDFPLIST
DRAW
S -40 -100 40 100 0 1 10 N
X ~ 1 0 150 50 D 50 50 1 1 P
X ~ 2 0 -150 50 U 50 50 1 1 P
ENDDRAW
ENDDEF
#
# Diode_1N4001
#
DEF Diode_1N4001 D 0 40 N N 1 F N
F0 "D" 0 100 50 H V C CNN
F1 "Diode_1N4001" 0 -100 50 H V C CNN
F2 "Diode_THT:D_DO-41_SOD81_P10.16mm_Horizontal" 0 -175 50 H I C CNN
F3 "" 0 0 50 H I C CNN
ALIAS 1N4002 1N4003 1N4004 1N4005 1N4006 1N4007 BA157 BA158 BA159
$FPLIST
 D*DO?41*
$ENDFPLIST
DRAW
P 2 0 1 10 -50 50 -50 -50 N
P 2 0 1 0 50 0 -50 0 N
P 4 0 1 10 50 50 50 -50 -50 0 50 50 N
X K 1 -150 0 100 R 50 50 1 1 P
X A 2 150 0 100 L 50 50 1 1 P
ENDDRAW
ENDDEF
#
# Diode_1N4148
#
DEF Diode_1N4148 D 0 40 N N 1 F N
F0 "D" 0 100 50 H V C CNN
F1 "Diode_1N4148" 0 -100 50 H V C CNN
F2 "Diode_THT:D_DO-35_SOD27_P7.62mm_Horizontal" 0 -175 50 H I C CNN
F3 "" 0 0 50 H I C CNN
ALIAS 1N4448 1N4149 1N4151 1N914 BA243 BA244 BA282 BA283 BAV17 BAV18 BAV19 BAV20 BAV21 BAW75 BAW76 BAY93
$FPLIST
 D*DO?35*
$ENDFPLIST
DRAW
P 2 0 1 10 -50 50 -50 -50 N
P 2 0 1 0 50 0 -50 0 N
P 4 0 1 10 50 50 50 -50 -50 0 50 50 N
X K 1 -150 0 100 R 50 50 1 1 P
X A 2 150 0 100 L 50 50 1 1 P
ENDDRAW
ENDDEF
#
# MCU_RaspberryPi_and_Boards_Pico
#
DEF MCU_RaspberryPi_and_Boards_Pico U 0 20 Y Y 1 F N
F0 "U" -550 1100 50 H V C CNN
F1 "MCU_RaspberryPi_and_Boards_Pico" 0 750 50 H V C CNN
F2 "RPi_Pico:RPi_Pico_SMD_TH" 0 0 50 V I C CNN
F3 "" 0 0 50 H I C CNN
DRAW
T 0 0 850 50 0 0 0 "Raspberry Pi Pico" Normal 0 C C
S -600 1050 600 -1050 0 1 0 f
X GPIO0 1 -700 950 100 R 50 50 1 1 B
X GPIO7 10 -700 50 100 R 50 50 1 1 B
X GPIO8 11 -700 -50 100 R 50 50 1 1 B
X GPIO9 12 -700 -150 100 R 50 50 1 1 B
X GND 13 -700 -250 100 R 50 50 1 1 W
X GPIO10 14 -700 -350 100 R 50 50 1 1 B
X GPIO11 15 -700 -450 100 R 50 50 1 1 B
X GPIO12 16 -700 -550 100 R 50 50 1 1 B
X GPIO13 17 -700 -650 100 R 50 50 1 1 B
X GND 18 -700 -750 100 R 50 50 1 1 W
X GPIO14 19 -700 -850 100 R 50 50 1 1 B
X GPIO1 2 -700 850 100 R 50 50 1 1 B
X GPIO15 20 -700 -950 100 R 50 50 1 1 B
X GPIO16 21 700 -950 100 L 50 50 1 1 B
X GPIO17 22 700 -850 100 L 50 50 1 1 B
X GND 23 700 -750 100 L 50 50 1 1 W
X GPIO18 24 700 -650 100 L 50 50 1 1 B
X GPIO19 25 700 -550 100 L 50 50 1 1 B
X GPIO20 26 700 -450 100 L 50 50 1 1 B
X GPIO21 27 700 -350 100 L 50 50 1 1 B
X GND 28 700 -250 100 L 50 50 1 1 W
X GPIO22 29 700 -150 100 L 50 50 1 1 B
X GND 3 -700 750 100 R 50 50 1 1 W
X RUN 30 700 -50 100 L 50 50 1 1 I
X GPIO26_ADC0 31 700 50 100 L 50 50 1 1 B
X GPIO27_ADC1 32 700 150 100 L 50 50 1 1 B
X AGND 33 700 250 100 L 50 50 1 1 W
X GPIO28_ADC2 34 700 350 100 L 50 50 1 1 B
X ADC_VREF 35 700 450 100 L 50 50 1 1 W
X 3V3 36 700 550 100 L 50 50 1 1 W
X 3V3_EN 37 700 650 100 L 50 50 1 1 I
X GND 38 700 750 100 L 50 50 1 1 B
X VSYS 39 700 850 100 L 50 50 1 1 W
X GPIO2 4 -700 650 100 R 50 50 1 1 B
X VBUS 40 700 950 100 L 50 50 1 1 W
X SWCLK 41 -100 -1150 100 U 50 50 1 1 I
X GND 42 0 -1150 100 U 50 50 1 1 W
X SWDIO 43 100 -1150 100 U 50 50 1 1 B
X GPIO3 5 -700 550 100 R 50 50 1 1 B
X GPIO4 6 -700 450 100 R 50 50 1 1 B
X GPIO5 7 -700 350 100 R 50 50 1 1 B
X GND 8 -700 250 100 R 50 50 1 1 W
X GPIO6 9 -700 150 100 R 50 50 1 1 B
ENDDRAW
ENDDEF
#
# dk_Transistors-Bipolar-BJT-Single_2N3904
#
DEF dk_Transistors-Bipolar-BJT-Single_2N3904 Q 0 0 Y Y 1 F N
F0 "Q" -126 166 60 H V L CNN
F1 "dk_Transistors-Bipolar-BJT-Single_2N3904" 206 0 60 V V C CNN
F2 "digikey-footprints:TO-92-3" 200 200 60 H I L CNN
F3 "https://my.centralsemi.com/get_document.php?cmp=1&mergetype=pd&mergepath=pd&pdf_id=LSSGP072.PDF" 200 300 60 H I L CNN
F4 "2N3904CS-ND" 200 400 60 H I L CNN "Digi-Key_PN"
F5 "2N3904" 200 500 60 H I L CNN "MPN"
F6 "Discrete Semiconductor Products" 200 600 60 H I L CNN "Category"
F7 "Transistors - Bipolar (BJT) - Single" 200 700 60 H I L CNN "Family"
F8 "https://my.centralsemi.com/get_document.php?cmp=1&mergetype=pd&mergepath=pd&pdf_id=LSSGP072.PDF" 200 800 60 H I L CNN "DK_Datasheet_Link"
F9 "/product-detail/en/central-semiconductor-corp/2N3904/2N3904CS-ND/4806876" 200 900 60 H I L CNN "DK_Detail_Page"
F10 "TRANS NPN 40V TO-92" 200 1000 60 H I L CNN "Description"
F11 "Central Semiconductor Corp" 200 1100 60 H I L CNN "Manufacturer"
F12 "Active" 200 1200 60 H I L CNN "Status"
DRAW
C 0 0 128 0 1 0 f
P 2 0 1 0 -150 0 -100 0 N
P 2 0 1 0 -140 0 0 0 N
P 2 0 1 0 0 -50 100 -100 N
P 2 0 1 0 0 50 100 100 N
P 2 0 1 0 0 100 0 -100 N
P 4 0 1 0 60 -50 80 -90 40 -100 60 -50 F
X E 1 100 -200 100 U 50 50 1 1 P
X C 2 100 200 100 D 50 50 1 1 P
X B 3 -200 0 100 R 50 50 1 1 I
ENDDRAW
ENDDEF
#
# eurocad_PIN_HEADER_2x8
#
DEF eurocad_PIN_HEADER_2x8 H 0 40 Y Y 1 F N
F0 "H" 0 -450 60 H V C CNN
F1 "eurocad_PIN_HEADER_2x8" 0 450 60 H V C CNN
F2 "" 100 150 60 H V C CNN
F3 "" 100 150 60 H V C CNN
DRAW
S -150 400 150 -400 0 1 0 f
X 1 1 -350 350 200 R 50 50 1 1 B
X 10 10 350 -50 200 L 50 50 1 1 B
X 11 11 -350 -150 200 R 50 50 1 1 B
X 12 12 350 -150 200 L 50 50 1 1 B
X 13 13 -350 -250 200 R 50 50 1 1 B
X 14 14 350 -250 200 L 50 50 1 1 B
X 15 15 -350 -350 200 R 50 50 1 1 B
X 16 16 350 -350 200 L 50 50 1 1 B
X 2 2 350 350 200 L 50 50 1 1 B
X 3 3 -350 250 200 R 50 50 1 1 B
X 4 4 350 250 200 L 50 50 1 1 B
X 5 5 -350 150 200 R 50 50 1 1 B
X 6 6 350 150 200 L 50 50 1 1 B
X 7 7 -350 50 200 R 50 50 1 1 B
X 8 8 350 50 200 L 50 50 1 1 B
X 9 9 -350 -50 200 R 50 50 1 1 B
ENDDRAW
ENDDEF
#
# eurocad_PJ301M-12
#
DEF eurocad_PJ301M-12 J 0 40 Y Y 1 F N
F0 "J" -350 -200 50 H V C CNN
F1 "eurocad_PJ301M-12" -150 250 50 H V C CNN
F2 "" 0 0 50 H V C CNN
F3 "" 0 0 50 H V C CNN
DRAW
S -450 150 -400 -100 0 1 0 F
S 300 -150 -400 200 0 1 0 N
P 3 0 1 0 150 0 300 0 300 0 N
P 4 0 1 0 0 -100 -50 -50 -100 -100 -100 -100 N
P 4 0 1 0 0 -100 300 -100 300 -100 300 -100 N
P 4 0 1 0 50 -50 100 -100 150 -50 150 -50 N
P 4 0 1 0 150 0 100 0 100 -100 100 -100 N
P 5 0 1 0 300 150 -250 150 -300 100 -350 150 -350 150 N
X ~ 1 450 150 150 L 50 50 1 1 P
X ~ 2 450 0 150 L 50 50 1 1 P
X ~ 3 450 -100 150 L 50 50 1 1 P
ENDDRAW
ENDDEF
#
# power_GND
#
DEF power_GND #PWR 0 0 Y Y 1 F P
F0 "#PWR" 0 -250 50 H I C CNN
F1 "power_GND" 0 -150 50 H V C CNN
F2 "" 0 0 50 H I C CNN
F3 "" 0 0 50 H I C CNN
DRAW
P 6 0 1 0 0 0 0 -50 50 -50 0 -100 -50 -50 0 -50 N
X GND 1 0 0 0 D 50 50 1 1 W N
ENDDRAW
ENDDEF
#
#End Library
