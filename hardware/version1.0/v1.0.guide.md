# uSEQ v1.0

## Features

- 2 attenuverted analogue inputs (sensitive in -5V to +5V) (when unplugged, the voltage of these inputs is set with the knobs)
- 2 gate inputs
- 3 CV outputs (-5V to +5V)
- 3 gate outputs (0V or 5V)
- RP2040 Microprocessor, running a LISP livecoding engine
- USB-C Connection to a computer
- Send commands from any device that has a serial terminal
- Dedicated uSEQ Editor
  - Stream waveforms from the module and send them on as MIDI or open sound control
  - Take OSC or MIDI input from the computer
  - Synchronise modular with external equipment

## Build Guide

You will need: a soldering iron (pref. temp controlled), solder wire, tweezers, cutters, pliers. Blue tack might be helpful.

1. Solder on the USB connector
    1. Hold in place with blue tack while soldering
    2. A hot iron (>400C) helps with this connector, theres a lot of metal to heat up (including the ground plane on the PCB)
2. Test: Power up the board and check if the RP2040 appears.  If not, check for bridged pads, use a hot iron or solder braid to remove bridges
3. Solder the reset and boot select SMT buttons next to the USB socket
    1. For each button, but a blob of solder onto one of the pads
    2. With tweezers, place the button to the side of the pad
    3. Heat the blob of solder and slide the button into place, ensuring it's flat against the PCB
    4. Solder the button in place
5. Solder on the pin headers for J7, J8 and J15.
    1. hint: while holding them in place, tack one of the pins on with a blob of solder, then solder on properly. Careful not to hold the pin that you tack on.
6. Unscrew the nuts from sockets, knobs and the switch
7. Insert all the LEDs, matching colour groups to types of IO. Square pads = Ground (short leg).
8. Insert all the components.  For the momentary switch, there's a side with a captial letter, this goes to the left.
9. Put on the front panel
    1. hint: wiggle it gentlly until it falls into place
10. Put the nuts onto all of the front panel components and tighten loosely
11. Double check everything is in place, and all the component legs are protruding through the PCB
12. Put the cap on the momentary switch.
13. Solder the switch: hold the switch in place so that it's flat against the PCB (as far back from the front panel as possible) and then tack a few of the pins in place. Then solder all pins fully.
14. Solder the switches, knobs and pots
15. With the front panel face down, push the LEDs into the holes
16. Lift the module up and look underneath to check the LEDs are all in place
17. Solder the LEDs
    1. Careful not to knock them out of place while you're soldering; stop and check they're in place if you think one may have moved.
18. Trim the LED legs
19. Tighten up all of the nuts on the front panel
20. Finished! Now test the module...

## Testing Guide

Power up the module outside of the rack. Flash the module with the uSEQ firmware.

### Test 1: Panel light

Is the yellow light on on the back of the PCB?

### Test 2: CV inputs

With the CV inputs unplugged, move the attenuvertor knobs.  Do the corresponding LEDs vary from off to full brightness as you move the knob from left to right?

Problem?  

1. Check soldering on the knobs and LEDs
2. Check the polarity of the LEDs
    1. If you need to de-solder an LED, trim the head off with clippers, then heat the remaining wires next to the hole and pull out with pliers
    2. Remove remaining solder with braid or a solder sucker, being carefull not to remove the pad.
    3. Replace with LED with a 3mm flat top LED (https://store.brightcomponents.co.uk/product-category/leds/round-leds/flat-top/3mm-flat-top/)


### Test 3

Run this script:

[to follow - a single function which tests all aspects of the module]
