(kicad_pcb (version 20221018) (generator pcbnew)

  (general
    (thickness 1.6)
  )

  (paper "A4")
  (layers
    (0 "F.Cu" signal)
    (31 "B.Cu" signal)
    (32 "B.Adhes" user "B.Adhesive")
    (33 "F.Adhes" user "F.Adhesive")
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user "B.Silkscreen")
    (37 "F.SilkS" user "F.Silkscreen")
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user "User.Drawings")
    (41 "Cmts.User" user "User.Comments")
    (42 "Eco1.User" user "User.Eco1")
    (43 "Eco2.User" user "User.Eco2")
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user "B.Courtyard")
    (47 "F.CrtYd" user "F.Courtyard")
    (48 "B.Fab" user)
    (49 "F.Fab" user)
    (50 "User.1" user)
    (51 "User.2" user)
    (52 "User.3" user)
    (53 "User.4" user)
    (54 "User.5" user)
    (55 "User.6" user)
    (56 "User.7" user)
    (57 "User.8" user)
    (58 "User.9" user)
  )

  (setup
    (stackup
      (layer "F.SilkS" (type "Top Silk Screen"))
      (layer "F.Paste" (type "Top Solder Paste"))
      (layer "F.Mask" (type "Top Solder Mask") (thickness 0.01))
      (layer "F.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 1" (type "core") (thickness 1.51) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "B.Cu" (type "copper") (thickness 0.035))
      (layer "B.Mask" (type "Bottom Solder Mask") (thickness 0.01))
      (layer "B.Paste" (type "Bottom Solder Paste"))
      (layer "B.SilkS" (type "Bottom Silk Screen"))
      (copper_finish "None")
      (dielectric_constraints no)
    )
    (pad_to_mask_clearance 0)
    (grid_origin 172.65 65.35)
    (pcbplotparams
      (layerselection 0x00010fc_ffffffff)
      (plot_on_all_layers_selection 0x0000000_00000000)
      (disableapertmacros false)
      (usegerberextensions false)
      (usegerberattributes true)
      (usegerberadvancedattributes true)
      (creategerberjobfile true)
      (dashed_line_dash_ratio 12.000000)
      (dashed_line_gap_ratio 3.000000)
      (svgprecision 6)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (dxfpolygonmode true)
      (dxfimperialunits true)
      (dxfusepcbnewfont true)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 0)
      (scaleselection 1)
      (outputdirectory "gbr/")
    )
  )

  (net 0 "")
  (net 1 "GND")
  (net 2 "+12V")
  (net 3 "unconnected-(RV2-Pad1)")
  (net 4 "unconnected-(RV2-Pad2)")
  (net 5 "+3V3")
  (net 6 "unconnected-(RV2-Pad3)")
  (net 7 "-12V")
  (net 8 "/GPIO26_ADC0")
  (net 9 "/+3V3_SIG")
  (net 10 "+1V1")
  (net 11 "/XIN")
  (net 12 "Net-(C28-Pad1)")
  (net 13 "Net-(D3-K)")
  (net 14 "Net-(D4-A)")
  (net 15 "Net-(D5-A)")
  (net 16 "Net-(FB1-Pad1)")
  (net 17 "Net-(FB2-Pad1)")
  (net 18 "Net-(J1-CC1)")
  (net 19 "/USB_D+")
  (net 20 "/USB_D-")
  (net 21 "unconnected-(J1-SBU1-PadA8)")
  (net 22 "Net-(J1-CC2)")
  (net 23 "unconnected-(J1-SBU2-PadB8)")
  (net 24 "Net-(R29-Pad2)")
  (net 25 "/GPIO1_Ext2")
  (net 26 "/GPIO0_Ext1")
  (net 27 "/SWCLK")
  (net 28 "/SWD")
  (net 29 "/GPIO8_GateInput1")
  (net 30 "/GPIO9_GateInput2")
  (net 31 "Net-(U7-USB_DP)")
  (net 32 "Net-(U7-USB_DM)")
  (net 33 "/GPIO5_LED_In1")
  (net 34 "/GPIO4_LED_In2")
  (net 35 "/GPIO3_LED_A1")
  (net 36 "/GPIO2_LED_A2")
  (net 37 "/GPIO24_LED_CVIn2")
  (net 38 "/GPIO25_LED_CVIn1")
  (net 39 "/QSPI_SS")
  (net 40 "/GPIO11_LED_A3")
  (net 41 "/GPIO12_LED_D1")
  (net 42 "/GPIO13_LED_D2")
  (net 43 "/GPIO22_LED_D3")
  (net 44 "/XOUT")
  (net 45 "/GPIO21_PWM1")
  (net 46 "/GPIO20_PWM2")
  (net 47 "/GPIO19_PWM3")
  (net 48 "/RUN")
  (net 49 "/GPIO10_SWM")
  (net 50 "/GPIO14_SWT")
  (net 51 "/GPIO7")
  (net 52 "/GPIO15")
  (net 53 "/GPIO16_Digital3")
  (net 54 "/GPIO17_Digital2")
  (net 55 "/GPIO18_Digital1")
  (net 56 "/GPIO23_SWTB")
  (net 57 "/GPIO27_ADC1")
  (net 58 "/GPIO28_ADC2")
  (net 59 "/GPIO29_ADC3")
  (net 60 "/QSPI_SD3")
  (net 61 "/QSPI_SCLK")
  (net 62 "/QSPI_SD0")
  (net 63 "/QSPI_SD2")
  (net 64 "/QSPI_SD1")
  (net 65 "unconnected-(J1-VBUS-PadA4B9)")
  (net 66 "unconnected-(J1-VBUS-PadA9B4)")
  (net 67 "/GPIO6_LED_TEST")
  (net 68 "unconnected-(SW1-NC_2-Pad4)")
  (net 69 "unconnected-(SW1-NC_1-Pad1)")

  (footprint "Resistor_SMD:R_0805_2012Metric" (layer "F.Cu")
    (tstamp 11177cf5-e764-45f4-a8c3-c3502673453c)
    (at 184.9 22.4975 -90)
    (descr "Resistor SMD 0805 (2012 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Ferrite bead")
    (property "ki_keywords" "L ferrite bead inductor filter")
    (path "/be084fc5-7036-4895-9b5a-c31f1bd0546d")
    (attr smd)
    (fp_text reference "FB2" (at -0.5815 1.836 90) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 7899de90-17a9-4f9f-8756-38f1a0b218d1)
    )
    (fp_text value "FerriteBead GZ2012D101TF" (at 0 1.65 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 1e7b1074-a655-4260-94ae-61ad37cdbc73)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab")
        (effects (font (size 0.5 0.5) (thickness 0.08)))
      (tstamp 03e4c29e-48ec-4125-9ab9-0dd598352241)
    )
    (fp_line (start -0.227064 -0.735) (end 0.227064 -0.735)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 58a32ff2-f152-46bc-9653-71f2d8efb296))
    (fp_line (start -0.227064 0.735) (end 0.227064 0.735)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp d2196466-a53c-4875-8c2d-fb5130ceb480))
    (fp_line (start -1.68 -0.95) (end 1.68 -0.95)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 90043eb3-fb03-4194-bb4f-9c39c5382439))
    (fp_line (start -1.68 0.95) (end -1.68 -0.95)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 367578d1-471a-4227-8bf8-6955ff26bef7))
    (fp_line (start 1.68 -0.95) (end 1.68 0.95)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 3ff3dafc-7d25-4642-a3d9-0b83c91d14cc))
    (fp_line (start 1.68 0.95) (end -1.68 0.95)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 417eb301-2e9b-408b-bb98-089b8bd1789e))
    (fp_line (start -1 -0.625) (end 1 -0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2af1a493-3610-49b3-bd74-133bd17a7052))
    (fp_line (start -1 0.625) (end -1 -0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 48f27fc4-ac68-4365-89ae-81e6a0c9964d))
    (fp_line (start 1 -0.625) (end 1 0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 52112636-c682-4fff-aa17-8eff0fe78bb8))
    (fp_line (start 1 0.625) (end -1 0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8037146b-4a6e-47e1-add9-cd5575754966))
    (pad "1" smd roundrect (at -0.9125 0 270) (size 1.025 1.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.243902439)
      (net 17 "Net-(FB2-Pad1)") (pintype "passive") (tstamp af1909a4-27b8-4066-b704-80d0e3b6a470))
    (pad "2" smd roundrect (at 0.9125 0 270) (size 1.025 1.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.243902439)
      (net 14 "Net-(D4-A)") (pintype "passive") (tstamp aba22550-fd24-4ba1-b5ec-a04bc571ccd5))
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0805_2012Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_0402_1005Metric" (layer "F.Cu")
    (tstamp 14d85fcf-b7d3-4ea0-9cdc-024454c7215f)
    (at 152.66 22.18 180)
    (descr "Resistor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Resistor")
    (property "ki_keywords" "R res resistor")
    (path "/c9ba9bab-5f45-4459-ab2e-1037f104c47d")
    (attr smd)
    (fp_text reference "R3" (at -1.76 0.17) (layer "F.SilkS")
        (effects (font (size 0.5 0.5) (thickness 0.125)))
      (tstamp 549afdeb-9ad6-46a8-8121-590042b7d24a)
    )
    (fp_text value "5.1k" (at 0 1.17) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp a618903e-6425-4b56-8084-31f7f0004b89)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.26 0.26) (thickness 0.04)))
      (tstamp f801a925-aab1-4451-89ff-1f8e6f71d0e3)
    )
    (fp_line (start -0.153641 -0.38) (end 0.153641 -0.38)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp aadd3ff2-4a82-4c70-bf3d-d9e683dbb16d))
    (fp_line (start -0.153641 0.38) (end 0.153641 0.38)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp bd27beda-8167-4852-9635-621e8257f4bf))
    (fp_line (start -0.93 -0.47) (end 0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 5a0a78b0-2a64-4182-a295-306e9e602c90))
    (fp_line (start -0.93 0.47) (end -0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 18cd86d4-d2f3-485e-b7fd-8faa00f0eace))
    (fp_line (start 0.93 -0.47) (end 0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 5bd36760-d64e-4a6f-9962-c68cd3b49f24))
    (fp_line (start 0.93 0.47) (end -0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 996024a9-6a1d-4d24-b0f7-1c3e76ccb2ae))
    (fp_line (start -0.525 -0.27) (end 0.525 -0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f6f9fb47-e1d7-47b9-b461-976cf3acf17a))
    (fp_line (start -0.525 0.27) (end -0.525 -0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 29c585b9-b4ce-4fa1-899f-dc42c4579e9f))
    (fp_line (start 0.525 -0.27) (end 0.525 0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 547b7b9b-bd10-409a-b32d-5650abcea725))
    (fp_line (start 0.525 0.27) (end -0.525 0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a8d974c4-908f-42b9-af06-9ce9c93770c2))
    (pad "1" smd roundrect (at -0.51 0 180) (size 0.54 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 22 "Net-(J1-CC2)") (pintype "passive") (tstamp 75682425-3d5e-4d91-a17d-815dea4e55e3))
    (pad "2" smd roundrect (at 0.51 0 180) (size 0.54 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp 657380b3-44de-4036-aca5-1654327bf2f2))
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "F.Cu")
    (tstamp 1b6a9ead-8dcb-40a1-8b76-b5ebe20a31aa)
    (at 162.405622 45.116425 180)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/7d31a6d7-88f5-4c78-83fa-083d2412cd54")
    (attr smd)
    (fp_text reference "C20" (at 0 -1.16) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 0fd546ab-beda-4788-8996-e4cb110bf45f)
    )
    (fp_text value "100n" (at 0 1.16) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp b68b5d8e-da99-477b-8c9b-1376575221d1)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp 09d770bf-5ee8-4523-b2f4-e4c5f56f546a)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 32b8b904-efa2-45a7-a86c-3a9f4a683f65))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 628e9b0a-6fcb-46d9-877c-1b138157de72))
    (fp_line (start -0.91 -0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp e10f60de-8548-47e2-93f4-585efc1154e7))
    (fp_line (start -0.91 0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 91a1d022-2d51-4894-9e41-f51f79788102))
    (fp_line (start 0.91 -0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp e36b4e6c-abc5-4c27-a689-edd2ad4a6b60))
    (fp_line (start 0.91 0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 58446383-957a-44fd-a0a2-33c333827d0b))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 148aba24-c1ac-48de-a66f-00ae5d1ad56d))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 266517f7-d3e8-4b68-a23d-cad49f4403cc))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 4d1d04c8-6e00-4117-a6a6-4cefeac756f7))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2258ebfb-fa03-49a5-b7ec-0cae37e3ce2d))
    (pad "1" smd roundrect (at -0.48 0 180) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pintype "passive") (tstamp cc3c4a20-20b7-4ab4-8c58-22f7a544a7a7))
    (pad "2" smd roundrect (at 0.48 0 180) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp 44dc07a2-b52c-4172-bb74-1636900d8ace))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Connector_PinHeader_2.54mm:PinHeader_1x03_P2.54mm_Vertical" (layer "F.Cu")
    (tstamp 1c5ead23-b380-4a1e-8dee-c153276bc18e)
    (at 134.825 46.525)
    (descr "Through hole straight pin header, 1x03, 2.54mm pitch, single row")
    (tags "Through hole pin header THT 1x03 2.54mm single row")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Generic connector, single row, 01x03, script generated (kicad-library-utils/schlib/autogen/connector/)")
    (property "ki_keywords" "connector")
    (path "/054f5c89-2b63-4ac0-99fa-84cdbe5d8d78")
    (attr through_hole exclude_from_pos_files)
    (fp_text reference "J15" (at -0.1 7.555) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 4ea38664-d21e-4d80-ba68-df7db4530eef)
    )
    (fp_text value "SWD" (at 0 7.41) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 946960f3-e5ac-4cc6-9c38-4a6aab2dfda0)
    )
    (fp_text user "${REFERENCE}" (at 0 2.54 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp a27f6b46-03c0-4d77-ad29-282b8439b38b)
    )
    (fp_line (start -1.33 -1.33) (end 0 -1.33)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp aefb64f7-4a0f-4d6c-aeae-270092a1568c))
    (fp_line (start -1.33 0) (end -1.33 -1.33)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp ceb6c937-5c40-4267-9e3b-c173dfdc1d1a))
    (fp_line (start -1.33 1.27) (end -1.33 6.41)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 03754060-c70a-4515-9001-3455b5290804))
    (fp_line (start -1.33 1.27) (end 1.33 1.27)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp cb2dca2c-8ac2-434a-9ca8-25a0bea075f9))
    (fp_line (start -1.33 6.41) (end 1.33 6.41)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 79a4cf85-d92a-4b56-9ff5-8dfc22266813))
    (fp_line (start 1.33 1.27) (end 1.33 6.41)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f678df67-05e9-464a-b956-e58bfb10928e))
    (fp_line (start -1.8 -1.8) (end -1.8 6.85)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp fe91f02c-bed6-4422-a0e1-8f7866e1d03f))
    (fp_line (start -1.8 6.85) (end 1.8 6.85)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 3c93237d-7630-44c2-a881-fc3d50e0aa8e))
    (fp_line (start 1.8 -1.8) (end -1.8 -1.8)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 5d26e10a-6668-4b17-a7bf-d488951fbcca))
    (fp_line (start 1.8 6.85) (end 1.8 -1.8)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 9d3a0905-ff16-4bc4-b642-e9139aa45e84))
    (fp_line (start -1.27 -0.635) (end -0.635 -1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7c23ef34-e3ff-4dee-8508-dbb452cf7b72))
    (fp_line (start -1.27 6.35) (end -1.27 -0.635)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7b9c3e0d-9cc5-4901-9763-0bc1abafe37c))
    (fp_line (start -0.635 -1.27) (end 1.27 -1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 91874796-8f4c-41c0-a926-1c4c55a9b56b))
    (fp_line (start 1.27 -1.27) (end 1.27 6.35)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 65750119-cb7f-44b7-96f8-01d6764517fa))
    (fp_line (start 1.27 6.35) (end -1.27 6.35)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp aba4059a-7cd5-40b3-967a-2fe1ccbef57d))
    (pad "1" thru_hole rect (at 0 0) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask")
      (net 27 "/SWCLK") (pinfunction "Pin_1") (pintype "passive") (tstamp 913d68b7-6c44-4b8a-857b-3b9cb40367ed))
    (pad "2" thru_hole oval (at 0 2.54) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask")
      (net 1 "GND") (pinfunction "Pin_2") (pintype "passive") (tstamp 9af99eba-0f65-4fd1-ada9-e1a46bcac53e))
    (pad "3" thru_hole oval (at 0 5.08) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask")
      (net 28 "/SWD") (pinfunction "Pin_3") (pintype "passive") (tstamp 65898403-aa1c-444c-8116-66da617c5f89))
    (model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_1x03_P2.54mm_Vertical.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "TestPoint:TestPoint_Pad_1.5x1.5mm" (layer "F.Cu")
    (tstamp 35b69be2-6c4e-4d2d-9ce6-cb87d67771c4)
    (at 148.44 21.73)
    (descr "SMD rectangular pad as test Point, square 1.5mm side length")
    (tags "test point SMD pad rectangle square")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "test point (alternative probe-style design)")
    (property "ki_keywords" "test point tp")
    (path "/5eaa3aeb-2445-44e3-93ff-456bfb9f62ef")
    (attr exclude_from_pos_files)
    (fp_text reference "TP1" (at 2.33 -0.65) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 2c814a08-2ee3-41e0-8665-aa0f744e0feb)
    )
    (fp_text value "RP2040 Power" (at 0 1.75) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 75a0bb93-5450-4608-b426-24628cc7ed82)
    )
    (fp_text user "${REFERENCE}" (at 0 -1.65) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 16b545d1-5c8a-47bd-8ed1-2875f9178789)
    )
    (fp_line (start -0.95 -0.95) (end 0.95 -0.95)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 4708f25b-016f-408d-aead-89b6e545d807))
    (fp_line (start -0.95 0.95) (end -0.95 -0.95)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 0453a5be-0a78-4bf3-9782-7d1ce285095c))
    (fp_line (start 0.95 -0.95) (end 0.95 0.95)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 25ed11ed-cc0e-4415-b060-8731fd98eaa6))
    (fp_line (start 0.95 0.95) (end -0.95 0.95)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 10d21503-340d-41d4-960c-6c9a2b4fac58))
    (fp_line (start -1.25 -1.25) (end -1.25 1.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 2e0c32f5-cb35-4d57-9910-bf439d451299))
    (fp_line (start -1.25 -1.25) (end 1.25 -1.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp bd3c517f-4af2-4969-bc7c-694a3de14cef))
    (fp_line (start 1.25 1.25) (end -1.25 1.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 3f7e23b4-0ca9-4d10-84cd-33c36fa490a0))
    (fp_line (start 1.25 1.25) (end 1.25 -1.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp b47394e9-0945-4ab3-9e90-920dee804872))
    (pad "1" smd rect (at 0 0) (size 1.5 1.5) (layers "F.Cu" "F.Mask")
      (net 5 "+3V3") (pinfunction "1") (pintype "passive") (tstamp 701f8eb5-4230-4b11-9203-c85db17f892f))
  )

  (footprint "emutelab:RP2040-QFN-56" (layer "F.Cu")
    (tstamp 41352470-5a01-4324-ae96-10d62957ad34)
    (at 168.400622 44.091425)
    (descr "QFN, 56 Pin (http://www.cypress.com/file/416486/download#page=40), generated with kicad-footprint-generator ipc_dfn_qfn_generator.py")
    (tags "QFN DFN_QFN")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (path "/f1e3a72e-f940-492c-9bcd-9b71befb4d45")
    (attr smd)
    (fp_text reference "U7" (at 0 -4.82) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp fbd74ba8-e7ac-401a-8873-8187fb5f7984)
    )
    (fp_text value "RP2040" (at 0 4.82) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 20a9ab6c-a7a8-43d7-81bf-4fa1e537903b)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp ca7fe76d-b909-40fd-aabb-ad5db97e53ab)
    )
    (fp_line (start -3.61 3.61) (end -3.61 2.96)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 93e7fa70-ea03-45c9-a810-dea5cdef6d57))
    (fp_line (start -2.96 -3.61) (end -3.61 -3.61)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp a1b933e3-1174-4f83-98bb-66312c71c123))
    (fp_line (start -2.96 3.61) (end -3.61 3.61)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp e144aca1-1825-4def-9b2e-806d9cccec4c))
    (fp_line (start 2.96 -3.61) (end 3.61 -3.61)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 16a37173-8a43-4213-99a8-ad9d716a7f9e))
    (fp_line (start 2.96 3.61) (end 3.61 3.61)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp d23001d4-27ef-4b11-a08a-ca496e64ca64))
    (fp_line (start 3.61 -3.61) (end 3.61 -2.96)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp e12b74e4-a030-4a6d-ad5e-39f99aae2d62))
    (fp_line (start 3.61 3.61) (end 3.61 2.96)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 111ae147-bca7-49b1-80f6-0262d48d8184))
    (fp_line (start -4.12 -4.12) (end -4.12 4.12)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp b8b459c2-a84c-4602-9134-ac5c6e475508))
    (fp_line (start -4.12 4.12) (end 4.12 4.12)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp d662a813-b4f4-4077-a361-da4d7b325315))
    (fp_line (start 4.12 -4.12) (end -4.12 -4.12)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp d4b5954e-8018-4a84-9881-b842f0035b3f))
    (fp_line (start 4.12 4.12) (end 4.12 -4.12)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 839d478a-c414-4a3d-b864-b9944b6d45d7))
    (fp_line (start -3.5 -2.5) (end -2.5 -3.5)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 49fd6c8c-775d-4e53-9cae-4532780b3d5b))
    (fp_line (start -3.5 3.5) (end -3.5 -2.5)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c2fe1c00-dc56-43de-b4e7-1089b5d4c3b5))
    (fp_line (start -2.5 -3.5) (end 3.5 -3.5)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1b30f5b5-d133-4370-aaa3-ae0a4b3d6048))
    (fp_line (start 3.5 -3.5) (end 3.5 3.5)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 987c0f38-a262-45a8-a90c-e2cd59072eac))
    (fp_line (start 3.5 3.5) (end -3.5 3.5)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 5ab567b1-5c65-4c78-b3ce-4af9f446eb6c))
    (pad "" smd roundrect (at -0.6375 -0.6375) (size 1.084435 1.084435) (layers "F.Paste") (roundrect_rratio 0.2305347946) (tstamp 5eaaf9c8-d0f3-4c81-92fa-07234acfad6e))
    (pad "" smd roundrect (at -0.6375 0.6375) (size 1.084435 1.084435) (layers "F.Paste") (roundrect_rratio 0.2305347946) (tstamp 2945f1c0-55d6-4fcf-9d29-28fbceddf450))
    (pad "" smd roundrect (at 0.6375 -0.6375) (size 1.084435 1.084435) (layers "F.Paste") (roundrect_rratio 0.2305347946) (tstamp c2074e3f-bd8f-4edc-91f8-22152a5e5afa))
    (pad "" smd roundrect (at 0.6375 0.6375) (size 1.084435 1.084435) (layers "F.Paste") (roundrect_rratio 0.2305347946) (tstamp a22a4b0c-096e-4871-9cc5-3f7c724b72bc))
    (pad "1" smd roundrect (at -3.4375 -2.6) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pinfunction "IOVDD") (pintype "power_in") (tstamp fe01571d-3738-408e-9661-7662611314fb))
    (pad "2" smd roundrect (at -3.4375 -2.2) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 26 "/GPIO0_Ext1") (pinfunction "GPIO0") (pintype "bidirectional") (tstamp d325d3ce-ce6c-471f-9e29-2d5ebf402b5d))
    (pad "3" smd roundrect (at -3.4375 -1.8) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 25 "/GPIO1_Ext2") (pinfunction "GPIO1") (pintype "bidirectional") (tstamp c7d41600-b188-46b6-818e-8c94ffc65e0e))
    (pad "4" smd roundrect (at -3.4375 -1.4) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 36 "/GPIO2_LED_A2") (pinfunction "GPIO2") (pintype "bidirectional") (tstamp e32c3566-4f44-44f0-b457-921a0ae15b15))
    (pad "5" smd roundrect (at -3.4375 -1) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 35 "/GPIO3_LED_A1") (pinfunction "GPIO3") (pintype "bidirectional") (tstamp f760e984-2406-46da-aa3a-9622864b81e1))
    (pad "6" smd roundrect (at -3.4375 -0.6) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 34 "/GPIO4_LED_In2") (pinfunction "GPIO4") (pintype "bidirectional") (tstamp 5d0d4349-947b-45dc-a098-2b8721762f27))
    (pad "7" smd roundrect (at -3.4375 -0.2) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 33 "/GPIO5_LED_In1") (pinfunction "GPIO5") (pintype "bidirectional") (tstamp b2ac391b-ed79-4a57-b389-a5ba90731151))
    (pad "8" smd roundrect (at -3.4375 0.2) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 67 "/GPIO6_LED_TEST") (pinfunction "GPIO6") (pintype "bidirectional") (tstamp 244fb99a-cc13-4513-8546-b45d0a0bb70f))
    (pad "9" smd roundrect (at -3.4375 0.6) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 51 "/GPIO7") (pinfunction "GPIO7") (pintype "bidirectional") (tstamp c3c9e632-be40-47d8-8c4e-b46236832927))
    (pad "10" smd roundrect (at -3.4375 1) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pinfunction "IOVDD") (pintype "power_in") (tstamp 3b616761-fad7-461e-ad13-28e49125a250))
    (pad "11" smd roundrect (at -3.4375 1.4) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 29 "/GPIO8_GateInput1") (pinfunction "GPIO8") (pintype "bidirectional") (tstamp 77671fe7-eae3-45ac-9a7f-194a4e4d6120))
    (pad "12" smd roundrect (at -3.4375 1.8) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 30 "/GPIO9_GateInput2") (pinfunction "GPIO9") (pintype "bidirectional") (tstamp c07a0b9a-8002-48ab-8b3a-364dfb5140b8))
    (pad "13" smd roundrect (at -3.4375 2.2) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 49 "/GPIO10_SWM") (pinfunction "GPIO10") (pintype "bidirectional") (tstamp 410f4f9c-e0a3-415d-bbce-1db5fdb96cfe))
    (pad "14" smd roundrect (at -3.4375 2.6) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 40 "/GPIO11_LED_A3") (pinfunction "GPIO11") (pintype "bidirectional") (tstamp 172c368a-9135-42af-bcad-589ee2c99e9c))
    (pad "15" smd roundrect (at -2.6 3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 41 "/GPIO12_LED_D1") (pinfunction "GPIO12") (pintype "bidirectional") (tstamp 206047f0-3796-44c3-98e3-9c53fabb0d56))
    (pad "16" smd roundrect (at -2.2 3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 42 "/GPIO13_LED_D2") (pinfunction "GPIO13") (pintype "bidirectional") (tstamp ce8eb850-891b-43e8-aba9-73fff878b4dd))
    (pad "17" smd roundrect (at -1.8 3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 50 "/GPIO14_SWT") (pinfunction "GPIO14") (pintype "bidirectional") (tstamp 0742aed3-8c32-44ae-923a-17b195854a67))
    (pad "18" smd roundrect (at -1.4 3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 52 "/GPIO15") (pinfunction "GPIO15") (pintype "bidirectional") (tstamp 609a10ee-c883-4e10-a7ef-fcf786ba93f8))
    (pad "19" smd roundrect (at -1 3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pinfunction "TESTEN") (pintype "passive") (tstamp 0a3136fd-80c3-4f9d-91ce-f397823cb209))
    (pad "20" smd roundrect (at -0.6 3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 11 "/XIN") (pinfunction "XIN") (pintype "input") (tstamp a7edd205-ea24-480b-b97b-79c3dfe9d679))
    (pad "21" smd roundrect (at -0.2 3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 44 "/XOUT") (pinfunction "XOUT") (pintype "passive") (tstamp 660bc054-77a6-47b8-ad7b-cbf16060bf1c))
    (pad "22" smd roundrect (at 0.2 3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pinfunction "IOVDD") (pintype "power_in") (tstamp 291c2e50-3bea-408c-8625-1d23085410de))
    (pad "23" smd roundrect (at 0.6 3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 10 "+1V1") (pinfunction "DVDD") (pintype "power_in") (tstamp 155d8b42-b910-401b-a321-f0575aa70f39))
    (pad "24" smd roundrect (at 1 3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 27 "/SWCLK") (pinfunction "SWCLK") (pintype "output") (tstamp c9788cd4-30c5-4e05-9899-64d73f480c07))
    (pad "25" smd roundrect (at 1.4 3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 28 "/SWD") (pinfunction "SWD") (pintype "bidirectional") (tstamp 6abd732b-498b-4142-8a07-d8c02efc631b))
    (pad "26" smd roundrect (at 1.8 3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 48 "/RUN") (pinfunction "RUN") (pintype "input") (tstamp 6f3af806-4ee6-4472-bbc9-a9bf7635b760))
    (pad "27" smd roundrect (at 2.2 3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 53 "/GPIO16_Digital3") (pinfunction "GPIO16") (pintype "bidirectional") (tstamp b2428412-4e75-4667-a744-09c2e1a15bcb))
    (pad "28" smd roundrect (at 2.6 3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 54 "/GPIO17_Digital2") (pinfunction "GPIO17") (pintype "bidirectional") (tstamp 0de84c1a-940c-4bb7-afc5-d11c3a94843a))
    (pad "29" smd roundrect (at 3.4375 2.6) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 55 "/GPIO18_Digital1") (pinfunction "GPIO18") (pintype "bidirectional") (tstamp c3558ad7-d295-4e2e-9c62-4fd0315b8d83))
    (pad "30" smd roundrect (at 3.4375 2.2) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 47 "/GPIO19_PWM3") (pinfunction "GPIO19") (pintype "bidirectional") (tstamp 13d57dfe-a4ce-410b-a48a-10f077c1a7c8))
    (pad "31" smd roundrect (at 3.4375 1.8) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 46 "/GPIO20_PWM2") (pinfunction "GPIO20") (pintype "bidirectional") (tstamp d59188f9-6efe-445e-8822-3d8ad7e6d415))
    (pad "32" smd roundrect (at 3.4375 1.4) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 45 "/GPIO21_PWM1") (pinfunction "GPIO21") (pintype "bidirectional") (tstamp f3145f91-6c12-4ea8-a45d-136e2da52f88))
    (pad "33" smd roundrect (at 3.4375 1) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pinfunction "IOVDD") (pintype "power_in") (tstamp d9dedd80-ab4d-4472-86cd-e93143422388))
    (pad "34" smd roundrect (at 3.4375 0.6) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 43 "/GPIO22_LED_D3") (pinfunction "GPIO22") (pintype "bidirectional") (tstamp 05781b8a-01d4-4cb9-b976-fc4990207c7c))
    (pad "35" smd roundrect (at 3.4375 0.2) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 56 "/GPIO23_SWTB") (pinfunction "GPIO23") (pintype "bidirectional") (tstamp b831895f-c8a8-4303-b1ac-4f02c984d1d9))
    (pad "36" smd roundrect (at 3.4375 -0.2) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 37 "/GPIO24_LED_CVIn2") (pinfunction "GPIO24") (pintype "bidirectional") (tstamp 5c40636b-30c4-42d2-a584-5267ec9ddca6))
    (pad "37" smd roundrect (at 3.4375 -0.6) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 38 "/GPIO25_LED_CVIn1") (pinfunction "GPIO25") (pintype "bidirectional") (tstamp 15dbd408-4884-49b2-be9e-fc1089aeedb0))
    (pad "38" smd roundrect (at 3.4375 -1) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 8 "/GPIO26_ADC0") (pinfunction "GPIO26_ADC0") (pintype "bidirectional") (tstamp 5f874cf2-e882-4a1b-99f7-68adda29a9f6))
    (pad "39" smd roundrect (at 3.4375 -1.4) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 57 "/GPIO27_ADC1") (pinfunction "GPIO27_ADC1") (pintype "bidirectional") (tstamp 31edada4-6bca-443b-b6b5-5e0f9dc6e4fc))
    (pad "40" smd roundrect (at 3.4375 -1.8) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 58 "/GPIO28_ADC2") (pinfunction "GPIO28_ADC2") (pintype "bidirectional") (tstamp 6818715b-c3e1-4e08-89d7-1f415db2f044))
    (pad "41" smd roundrect (at 3.4375 -2.2) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 59 "/GPIO29_ADC3") (pinfunction "GPIO29_ADC3") (pintype "bidirectional") (tstamp b30cc14c-c22b-4d68-b544-7029ad5af273))
    (pad "42" smd roundrect (at 3.4375 -2.6) (size 0.875 0.2) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pinfunction "IOVDD") (pintype "power_in") (tstamp 2cf51fdb-1ff2-4c81-9af0-a07b347628c6))
    (pad "43" smd roundrect (at 2.6 -3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 9 "/+3V3_SIG") (pinfunction "ADC_AVDD") (pintype "power_in") (tstamp 250f10cc-9f62-4a3b-b068-24e3b6837e37))
    (pad "44" smd roundrect (at 2.2 -3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pinfunction "VREG_IN") (pintype "power_in") (tstamp cfa4ecc8-2201-477a-9d01-25247e7156da))
    (pad "45" smd roundrect (at 1.8 -3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 10 "+1V1") (pinfunction "VREG_VOUT") (pintype "power_out") (tstamp fd544211-c66c-4323-b98a-f9511a8c1336))
    (pad "46" smd roundrect (at 1.4 -3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 32 "Net-(U7-USB_DM)") (pinfunction "USB_DM") (pintype "bidirectional") (tstamp 825ab1e8-5fea-4df3-b627-d06b6fe627ea))
    (pad "47" smd roundrect (at 1 -3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 31 "Net-(U7-USB_DP)") (pinfunction "USB_DP") (pintype "bidirectional") (tstamp b265fad6-c1f5-469e-abfe-2c0691337d2d))
    (pad "48" smd roundrect (at 0.6 -3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pinfunction "USB_VDD") (pintype "power_in") (tstamp 0d778701-121f-44a0-a016-cd11afe9cf7f))
    (pad "49" smd roundrect (at 0.2 -3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pinfunction "IOVDD") (pintype "power_in") (tstamp 53a74f66-ce3f-4ade-89c0-22a23aae570d))
    (pad "50" smd roundrect (at -0.2 -3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 10 "+1V1") (pinfunction "DVDD") (pintype "power_in") (tstamp 64d2f92e-080b-46ad-966f-78c331ea011b))
    (pad "51" smd roundrect (at -0.6 -3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 60 "/QSPI_SD3") (pinfunction "QSPI_SD3") (pintype "bidirectional") (tstamp d89ad8ab-886c-4f3d-ba11-2587725b5281))
    (pad "52" smd roundrect (at -1 -3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 61 "/QSPI_SCLK") (pinfunction "QSPI_SCLK") (pintype "output") (tstamp abc204c4-fa43-4862-a38e-22c82b00278a))
    (pad "53" smd roundrect (at -1.4 -3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 62 "/QSPI_SD0") (pinfunction "QSPI_SD0") (pintype "bidirectional") (tstamp 042c029a-8589-4355-97fd-576b57fd0e6f))
    (pad "54" smd roundrect (at -1.8 -3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 63 "/QSPI_SD2") (pinfunction "QSPI_SD2") (pintype "bidirectional") (tstamp 51fca494-26bf-419f-95aa-a99af443faa3))
    (pad "55" smd roundrect (at -2.2 -3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 64 "/QSPI_SD1") (pinfunction "QSPI_SD1") (pintype "bidirectional") (tstamp 918bcdbf-dcf4-472e-bf8e-2b032095a4e1))
    (pad "56" smd roundrect (at -2.6 -3.4375) (size 0.2 0.875) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 39 "/QSPI_SS") (pinfunction "QSPI_SS") (pintype "bidirectional") (tstamp 5ddeea49-ea04-4622-a40e-5712fa7af680))
    (pad "57" thru_hole circle (at -1.275 -1.275) (size 0.6 0.6) (drill 0.35) (layers "*.Cu")
      (net 1 "GND") (pinfunction "GND") (pintype "power_in") (tstamp bc202fc1-77e7-4947-8c5b-568348c14af4))
    (pad "57" thru_hole circle (at -1.275 0) (size 0.6 0.6) (drill 0.35) (layers "*.Cu")
      (net 1 "GND") (pinfunction "GND") (pintype "power_in") (tstamp 0fe92cb0-67fc-4e46-a914-c30bcf5c4bf6))
    (pad "57" thru_hole circle (at -1.275 1.275) (size 0.6 0.6) (drill 0.35) (layers "*.Cu")
      (net 1 "GND") (pinfunction "GND") (pintype "power_in") (tstamp d6a81b36-e914-4798-85e0-49958efe7b50))
    (pad "57" thru_hole circle (at 0 -1.275) (size 0.6 0.6) (drill 0.35) (layers "*.Cu")
      (net 1 "GND") (pinfunction "GND") (pintype "power_in") (tstamp 9b6789de-0257-431c-a744-fb29b02e062b))
    (pad "57" thru_hole circle (at 0 0) (size 0.6 0.6) (drill 0.35) (layers "*.Cu")
      (net 1 "GND") (pinfunction "GND") (pintype "power_in") (tstamp 66160bd7-b62d-41ee-9393-01527eae8422))
    (pad "57" smd roundrect (at 0 0) (size 3.2 3.2) (layers "F.Cu" "F.Mask") (roundrect_rratio 0.045)
      (net 1 "GND") (pinfunction "GND") (pintype "power_in") (zone_connect 2) (tstamp ddccbe07-b4de-499d-80c0-1fec58023a67))
    (pad "57" thru_hole circle (at 0 1.275) (size 0.6 0.6) (drill 0.35) (layers "*.Cu")
      (net 1 "GND") (pinfunction "GND") (pintype "power_in") (tstamp 03afb35e-0b50-4d13-97a6-6df667c8e629))
    (pad "57" thru_hole circle (at 1.275 -1.275) (size 0.6 0.6) (drill 0.35) (layers "*.Cu")
      (net 1 "GND") (pinfunction "GND") (pintype "power_in") (tstamp eef97c76-4760-4fd7-96ca-c78797573533))
    (pad "57" thru_hole circle (at 1.275 0) (size 0.6 0.6) (drill 0.35) (layers "*.Cu")
      (net 1 "GND") (pinfunction "GND") (pintype "power_in") (tstamp f20ef9d1-21af-48c5-adc9-f3d9109fc268))
    (pad "57" thru_hole circle (at 1.275 1.275) (size 0.6 0.6) (drill 0.35) (layers "*.Cu")
      (net 1 "GND") (pinfunction "GND") (pintype "power_in") (tstamp 5bf21ba0-3d34-4872-b59f-5e7668452c4c))
    (model "${KISYS3DMOD}/Package_DFN_QFN.3dshapes/QFN-56-1EP_7x7mm_P0.4mm_EP5.6x5.6mm.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "F.Cu")
    (tstamp 464ddf0c-1efb-423f-a1c4-f5a85c1e113c)
    (at 170.625622 49.866425 -90)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/297774b4-286d-4ebe-978e-82c91cf5d231")
    (attr smd)
    (fp_text reference "C10" (at 1.05 -2.55 90) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp b6bac053-d2b2-4176-baa1-cf38dbef7b72)
    )
    (fp_text value "100n" (at 0 1.16 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp f0fbdd1b-25a4-4200-8faa-626d4cee06aa)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp b7a57cc5-af00-4507-a382-bfeac50dce16)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f77698cb-6ab1-4cf4-9ba9-7128f3bd37b3))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp c0ed2586-80da-4606-a4a9-443c613f3aee))
    (fp_line (start -0.91 -0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 3b662e03-eab2-4a38-be45-490bbe81a546))
    (fp_line (start -0.91 0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 30bbf172-3b1a-46ae-9fe2-b0b99477318d))
    (fp_line (start 0.91 -0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 34db3a80-2770-4e84-8846-0c6e4afd5110))
    (fp_line (start 0.91 0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 2888acc3-e980-48df-905a-9c9e8f756002))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c3c032f1-a94c-4e61-9ec2-5e1a0300be2a))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp de0a3011-a509-4922-9b08-c8cfe36eaf8e))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 806c4935-6d2b-4ed2-b8f8-df0d1c5c2e1e))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 047f5dea-197c-4cbe-a72b-89c65c3a03a1))
    (pad "1" smd roundrect (at -0.48 0 270) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 10 "+1V1") (pintype "passive") (tstamp 27e8c678-d680-4c3b-b55d-fa56f82cf216))
    (pad "2" smd roundrect (at 0.48 0 270) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp 01eb885d-eeb0-4f24-a65b-b64ff111cc04))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_0805_2012Metric" (layer "F.Cu")
    (tstamp 51480ca1-8ad7-4c91-94b5-9e78796700f1)
    (at 184.11 36.73)
    (descr "Resistor SMD 0805 (2012 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Ferrite bead")
    (property "ki_keywords" "L ferrite bead inductor filter")
    (path "/7da6f4e0-967c-434e-9b68-25e273d276b3")
    (attr smd)
    (fp_text reference "FB1" (at 0.732 -1.352) (layer "F.SilkS")
        (effects (font (size 0.5 0.5) (thickness 0.125)))
      (tstamp 1c9bf5bc-d833-4e07-9d47-6bee00f1edb1)
    )
    (fp_text value "FerriteBead GZ2012D101TF" (at 0 1.65) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 2b691cf3-b3cc-4a92-8af7-fb555858df33)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.5 0.5) (thickness 0.08)))
      (tstamp 164334fb-f7fc-4ec5-9331-d6d41a01dffb)
    )
    (fp_line (start -0.227064 -0.735) (end 0.227064 -0.735)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp d2cd1f2d-373f-43de-9d53-771165d39e5b))
    (fp_line (start -0.227064 0.735) (end 0.227064 0.735)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 598c2aeb-30ca-4129-8ab4-1291f2d72e5c))
    (fp_line (start -1.68 -0.95) (end 1.68 -0.95)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 0d8c3c6b-c7e5-43d7-b76e-cf887490b262))
    (fp_line (start -1.68 0.95) (end -1.68 -0.95)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 808a9e64-1c46-4c72-a55b-bd99640b56d8))
    (fp_line (start 1.68 -0.95) (end 1.68 0.95)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp f0355779-50e4-43d3-9c0a-300bea1c9f78))
    (fp_line (start 1.68 0.95) (end -1.68 0.95)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 131e94e8-abc5-42e4-a3b1-cf621a7af19b))
    (fp_line (start -1 -0.625) (end 1 -0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d5990fff-7d50-4a56-b4ea-01ee678b07dd))
    (fp_line (start -1 0.625) (end -1 -0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b7e89261-68f7-4b5f-bda1-9e06e0c9ccb6))
    (fp_line (start 1 -0.625) (end 1 0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d270239e-e319-47d4-9c96-74f632de90a3))
    (fp_line (start 1 0.625) (end -1 0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 736a2286-5b6b-49fa-91cd-c8ea19bd59a8))
    (pad "1" smd roundrect (at -0.9125 0) (size 1.025 1.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.243902439)
      (net 16 "Net-(FB1-Pad1)") (pintype "passive") (tstamp b9b3bad6-fc51-4aa4-ad7c-2e630e3294d8))
    (pad "2" smd roundrect (at 0.9125 0) (size 1.025 1.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.243902439)
      (net 13 "Net-(D3-K)") (pintype "passive") (tstamp f696c579-de9a-48a3-93d8-12fd95174e67))
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0805_2012Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_0402_1005Metric" (layer "F.Cu")
    (tstamp 53ab4a77-3345-4f5b-acfd-b53ac2a17d7f)
    (at 153.52 26.13 180)
    (descr "Resistor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Resistor")
    (property "ki_keywords" "R res resistor")
    (path "/368a115f-155e-4c71-9c6d-d243ac971404")
    (attr smd)
    (fp_text reference "R29" (at -2.4 0.1) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 44d6c03e-d49c-4fe8-a178-7400f525e8d6)
    )
    (fp_text value "1k" (at 0 1.17) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp fea4fb3b-3d14-4ebc-9868-5cf8051a81a4)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.26 0.26) (thickness 0.04)))
      (tstamp 97a78d7f-c59c-4ed0-b2a9-d0b7cf3fd286)
    )
    (fp_line (start -0.153641 -0.38) (end 0.153641 -0.38)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 09b59716-0bc8-4a70-8bf4-22db732606a2))
    (fp_line (start -0.153641 0.38) (end 0.153641 0.38)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 31008acc-512b-4585-b9b3-cac07d00fc91))
    (fp_line (start -0.93 -0.47) (end 0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 044bf6e4-3cee-4277-a18e-2d8e1011a4dd))
    (fp_line (start -0.93 0.47) (end -0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 254e88f4-1e25-4176-929b-acbe1501de72))
    (fp_line (start 0.93 -0.47) (end 0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp bb7aa0af-9d76-4ae7-8b54-bd0e9873dc28))
    (fp_line (start 0.93 0.47) (end -0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 654bf653-846d-4491-b6f1-7edfef6e7e44))
    (fp_line (start -0.525 -0.27) (end 0.525 -0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ae26b98d-7754-4c30-8053-943dfb891963))
    (fp_line (start -0.525 0.27) (end -0.525 -0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b74a615f-9e47-4cae-b0c5-67e2b1aabbd2))
    (fp_line (start 0.525 -0.27) (end 0.525 0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b88f803e-0f75-4963-a34d-af7da310a0ed))
    (fp_line (start 0.525 0.27) (end -0.525 0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c2fc6d1a-0e04-44e6-aac3-444cbbc52fde))
    (pad "1" smd roundrect (at -0.51 0 180) (size 0.54 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 39 "/QSPI_SS") (pintype "passive") (tstamp c71a043e-a947-4882-9746-5522c7c8a8c6))
    (pad "2" smd roundrect (at 0.51 0 180) (size 0.54 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 24 "Net-(R29-Pad2)") (pintype "passive") (tstamp d52e5fc9-40b5-4bd8-8e55-e608f44f8d7b))
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "F.Cu")
    (tstamp 54fb6f8a-423e-4990-a9c9-65d1721ad792)
    (at 162.675622 41.566425 180)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/b541e50f-56a8-4e12-9d30-a383b9b70282")
    (attr smd)
    (fp_text reference "C18" (at 0 -1.16) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp b3ec5282-192d-4977-94c5-768d4595d7f7)
    )
    (fp_text value "100n" (at 0 1.16) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 7232fdb6-0dd9-42cb-b345-5ad1ae851ac1)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp 3dd2d519-c062-4fc6-95a5-57effcc803ac)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 74e9fbe5-4722-4d6f-81cf-22f0cd4a2b00))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 323e752b-e92f-4309-a1db-6ccaa990c49e))
    (fp_line (start -0.91 -0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 94fc8ffd-af72-4270-b632-a8b9b0a90dc8))
    (fp_line (start -0.91 0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 4059ebc2-03bd-4e98-bea6-c1b2512d2b55))
    (fp_line (start 0.91 -0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 50074730-42c5-4d19-b12f-c8d0496b0bc6))
    (fp_line (start 0.91 0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 0441cef1-401d-427e-9445-6f858322aa92))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp dd43c251-ec13-4519-bd30-a8a1dd9da15f))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ef2cfb52-41e1-42dd-a8fd-c48a0f527f37))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp fa82e801-d7b8-4938-b3be-f7887a43e19f))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 67b856ca-3c15-471c-8a10-a2730888ebaf))
    (pad "1" smd roundrect (at -0.48 0 180) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pintype "passive") (tstamp b7d25be5-8e2d-4eaa-9db6-eb063e7df376))
    (pad "2" smd roundrect (at 0.48 0 180) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp 9f5a27cb-d2f1-4e72-bde3-2fb49f03104f))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "F.Cu")
    (tstamp 597b13ad-22e0-4c2c-ace2-1a78ce1f5ee5)
    (at 166.936211 38.377014 135)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/a2b6fbe6-11cb-40c8-abcf-c0127347d624")
    (attr smd)
    (fp_text reference "C11" (at 0.085686 -1.272792 180) (layer "F.SilkS")
        (effects (font (size 0.5 0.5) (thickness 0.125) bold))
      (tstamp c872d358-3645-4669-805a-e5dabb6715c0)
    )
    (fp_text value "100n" (at 0 1.16 135) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 57801697-391e-4447-8424-c314efcdbeaf)
    )
    (fp_text user "${REFERENCE}" (at 0 0 135) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp 9eb659b9-8450-4265-aa00-0ee9ca531b1b)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp dd3c1e6e-087f-4aac-8400-867c6f6adf6e))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 452eef5c-dd67-4707-b5e4-7aa86d5634c8))
    (fp_line (start -0.91 -0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 6985e17d-7bba-4df4-b772-cda42657ee32))
    (fp_line (start -0.91 0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 4de8aa95-5b6b-4074-a752-9d3ee350424e))
    (fp_line (start 0.91 -0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp e107b473-41f5-420d-b686-1ad8b3dfa799))
    (fp_line (start 0.91 0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp edad2ffc-1181-4bcb-9c55-013235c477ef))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ce084143-43e1-4103-a007-9e1e7979b670))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1e675978-3288-46ce-833a-945fd0855908))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b2a4b879-4d09-4e06-8869-bf17cdb1c728))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 94709ab0-1c95-482b-8e75-3a36488cfd6b))
    (pad "1" smd roundrect (at -0.48 0 135) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 10 "+1V1") (pintype "passive") (tstamp 210510ef-42e7-4abf-863f-4b1b3eba4690))
    (pad "2" smd roundrect (at 0.48 0 135) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp cd15fc95-60cf-498d-8745-b76cd5f632e4))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0805_2012Metric" (layer "F.Cu")
    (tstamp 5e4e18c3-2a31-4851-805d-519f25809d29)
    (at 180.24 38.78)
    (descr "Capacitor SMD 0805 (2012 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf, https://docs.google.com/spreadsheets/d/1BsfQQcO9C6DZCsRaXUlFlo91Tg2WpOkGARC1WS5S8t0/edit?usp=sharing), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/35a06014-1fcb-4254-a261-be4efdf255ff")
    (attr smd)
    (fp_text reference "C4" (at -0.94 -1.43) (layer "F.SilkS")
        (effects (font (size 0.5 0.5) (thickness 0.125)))
      (tstamp 27e80272-bc3b-46e5-a7c1-20886ff88430)
    )
    (fp_text value "22u" (at 0 1.68) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 971b6551-af54-4bb6-b0b6-aad5a0180850)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.5 0.5) (thickness 0.08)))
      (tstamp 072d5a62-a3c6-46e3-a746-1b30144e4b8f)
    )
    (fp_line (start -0.261252 -0.735) (end 0.261252 -0.735)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp a8651eeb-56d5-402c-97d5-cbe18725584e))
    (fp_line (start -0.261252 0.735) (end 0.261252 0.735)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp a42f5e60-8847-47b0-a31d-903b1f99c794))
    (fp_line (start -1.7 -0.98) (end 1.7 -0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 8692c253-6ec0-4c47-9eed-3708d3fdf51d))
    (fp_line (start -1.7 0.98) (end -1.7 -0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 704ff5fa-f1a2-4131-ac96-b917deb9a855))
    (fp_line (start 1.7 -0.98) (end 1.7 0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 08934f53-cd8e-4fa4-b07b-26be9aaa8d1c))
    (fp_line (start 1.7 0.98) (end -1.7 0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 249e842e-076f-4afe-991b-c08172ec6710))
    (fp_line (start -1 -0.625) (end 1 -0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 072788bb-5bc7-478c-b8fd-1000d1e49372))
    (fp_line (start -1 0.625) (end -1 -0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 62ca2af1-6f27-42bb-8a59-9bc68255d22b))
    (fp_line (start 1 -0.625) (end 1 0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 48413116-bdb4-4350-ad57-dcc301b7308d))
    (fp_line (start 1 0.625) (end -1 0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 53f41ee3-2a7e-4fac-9eb7-639e6122a422))
    (pad "1" smd roundrect (at -0.95 0) (size 1 1.45) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp 10a0bfb0-64c0-4a6a-898c-25b2eb68ac52))
    (pad "2" smd roundrect (at 0.95 0) (size 1 1.45) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 7 "-12V") (pintype "passive") (tstamp d34125ba-bb28-4b84-8d9b-865906f2261b))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0805_2012Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "F.Cu")
    (tstamp 633470d5-1c05-4e59-82f0-c73b66ddc0d2)
    (at 173.675622 42.266425)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/8313a131-c836-4641-a633-061b298fd756")
    (attr smd)
    (fp_text reference "C24" (at 1.84 -0.28) (layer "F.SilkS")
        (effects (font (size 0.5 0.5) (thickness 0.125)))
      (tstamp 89cc21e1-dd72-4856-bdcc-fd9466720e25)
    )
    (fp_text value "100n" (at 0 1.16) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 31c727af-fd1a-4a98-88f6-24cba9574683)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp c0f14dfe-d456-46fc-9581-01710ac2183b)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp c3298d40-6d8d-467b-9a43-a206daa9f1fe))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp a24ba6a4-c04a-465a-a667-622f7ed43b80))
    (fp_line (start -0.91 -0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 0c13d049-9275-47c6-beb6-91ada93037e8))
    (fp_line (start -0.91 0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 95fcee98-c1e9-46a1-aeb5-9421e438f421))
    (fp_line (start 0.91 -0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 0919d428-cd32-4d44-93ee-71bf4f85f289))
    (fp_line (start 0.91 0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 166abe10-ecdc-4674-9abf-448e0c26bca9))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d552c9c8-99cc-4faa-b02e-c7972761a8f9))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2870474e-759d-432a-9412-55569dda67f4))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0db71555-b52c-4fde-9934-c1ae3ee723f7))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp cffbec40-9650-4ecf-a09b-3cef3ead6ef4))
    (pad "1" smd roundrect (at -0.48 0) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pintype "passive") (tstamp e067888b-0f20-4774-b9bb-b3f367b964f8))
    (pad "2" smd roundrect (at 0.48 0) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp 104b44b0-f500-4f35-9ebe-6c66ddf9cd72))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_0603_1608Metric" (layer "F.Cu")
    (tstamp 643ab074-fe14-4355-911a-f109beadfd92)
    (at 169.095622 51.916425 180)
    (descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Resistor")
    (property "ki_keywords" "R res resistor")
    (path "/e8a67363-4973-4744-b0d4-b537ac4aaca8")
    (attr smd)
    (fp_text reference "R36" (at -1.18 -1.55 180) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 341c9c04-8803-41e9-b7c8-a0fd3e8da32f)
    )
    (fp_text value "1k" (at 0 1.43 180) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 5edb81c7-aae3-431c-aade-47bcfd7cd276)
    )
    (fp_text user "${REFERENCE}" (at 0 0 180) (layer "F.Fab")
        (effects (font (size 0.4 0.4) (thickness 0.06)))
      (tstamp 14ad1390-e551-4094-afcd-34ac1580bda1)
    )
    (fp_line (start -0.237258 -0.5225) (end 0.237258 -0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp dc4bbc52-15de-4e58-b5f4-c3499e4ede20))
    (fp_line (start -0.237258 0.5225) (end 0.237258 0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 490eec0f-9d3c-4a36-a5f7-30a63c50eba9))
    (fp_line (start -1.48 -0.73) (end 1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp a032552c-ec63-4c6d-8754-3391a5949ddd))
    (fp_line (start -1.48 0.73) (end -1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 5f4f8b27-56d4-4501-90fa-83a0fa942506))
    (fp_line (start 1.48 -0.73) (end 1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 88bb4d26-2176-428d-a54e-61e1820a0ccc))
    (fp_line (start 1.48 0.73) (end -1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 2a21510f-90cd-4783-b318-c0a6ad4fa761))
    (fp_line (start -0.8 -0.4125) (end 0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e5a6b1ea-76fe-46cc-9806-ac39d5c6755c))
    (fp_line (start -0.8 0.4125) (end -0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f4b35db3-71d5-45cb-a765-f26c7e5bd1cd))
    (fp_line (start 0.8 -0.4125) (end 0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 294dcc82-ae10-437d-b923-346b39e0438c))
    (fp_line (start 0.8 0.4125) (end -0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b64ef04d-a04f-46b3-b1db-7cd2c79a03f9))
    (pad "1" smd roundrect (at -0.825 0 180) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 44 "/XOUT") (pintype "passive") (tstamp 685a0434-7560-43ee-b017-3daba9b6dd28))
    (pad "2" smd roundrect (at 0.825 0 180) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 12 "Net-(C28-Pad1)") (pintype "passive") (tstamp d20af61d-9183-428e-a7fc-5ebe6ab14a82))
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Connector_PinHeader_2.54mm:PinHeader_2x05_P2.54mm_Vertical_SMD" (layer "F.Cu")
    (tstamp 697e7565-e523-4f7d-8bd3-1892d09808ba)
    (at 176.83 26.235 90)
    (descr "surface-mounted straight pin header, 2x05, 2.54mm pitch, double rows")
    (tags "Surface mounted pin header SMD 2x05 2.54mm double row")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (path "/d2003179-ab70-4d82-81c7-29282c698520")
    (zone_connect 2)
    (attr smd)
    (fp_text reference "H1" (at 0 -7.41 90) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp ef06f2a8-46cd-4309-8686-d4057a802e34)
    )
    (fp_text value "PIN_HEADER_2x5 POWER" (at 0 7.41 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp ee0a7e7a-5928-4ed1-88c6-396e5bba1dbb)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp f4472e4a-72a3-4164-86ab-2d8c4cfde49d)
    )
    (fp_line (start -4.04 -5.84) (end -2.6 -5.84)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 726de3d5-c2e1-4f63-be3d-f416e74e9024))
    (fp_line (start -2.6 -6.41) (end -2.6 -5.84)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp b6083842-c8b7-4703-8eb8-c5c0e84fc8b8))
    (fp_line (start -2.6 -6.41) (end 2.6 -6.41)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp b7974c35-1a4f-45ee-82ad-c6da39a8ed1b))
    (fp_line (start -2.6 -4.32) (end -2.6 -3.3)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 6f26f169-ddb0-484d-9641-55ce1da29366))
    (fp_line (start -2.6 -1.78) (end -2.6 -0.76)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp ec0f2699-44b0-4ab8-bfd8-bb8c165552af))
    (fp_line (start -2.6 0.76) (end -2.6 1.78)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 79880e7c-264a-4da1-8c35-f0e3a1040a79))
    (fp_line (start -2.6 3.3) (end -2.6 4.32)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp b27477de-a5e4-41fe-9abc-272fce273b0e))
    (fp_line (start -2.6 5.84) (end -2.6 6.41)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f934b6c5-e1ad-4cdc-9c38-cbcb860ec903))
    (fp_line (start -2.6 6.41) (end 2.6 6.41)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp a56f420c-9af2-4f0a-bf86-5debea4f0d7e))
    (fp_line (start 2.6 -6.41) (end 2.6 -5.84)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 2cb533d9-1f11-4213-914d-9d615525d592))
    (fp_line (start 2.6 -4.32) (end 2.6 -3.3)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 507b8c2a-f5f2-4e36-a9a4-4a486f9985d7))
    (fp_line (start 2.6 -1.78) (end 2.6 -0.76)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 2a896c9c-3b70-4a1c-9dfd-888d7108dd8d))
    (fp_line (start 2.6 0.76) (end 2.6 1.78)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f4ccba89-7f42-462b-b879-4ca5bcf16ac9))
    (fp_line (start 2.6 3.3) (end 2.6 4.32)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 3a16efb7-caa1-4c96-bf5e-47d21cb566e7))
    (fp_line (start 2.6 5.84) (end 2.6 6.41)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f64d9ab1-035c-43fb-a124-18e8a567bd46))
    (fp_line (start -5.9 -6.85) (end -5.9 6.85)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 6a7073ce-d0f0-4129-a0d8-9e43fd6c2832))
    (fp_line (start -5.9 6.85) (end 5.9 6.85)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 93e8b0a4-822f-4597-acf3-4fafecb06959))
    (fp_line (start 5.9 -6.85) (end -5.9 -6.85)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 08d34e1b-e421-471d-85f6-c390c0aa95ed))
    (fp_line (start 5.9 6.85) (end 5.9 -6.85)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 787d8f3a-c995-4bbd-8c28-10092b6fcfb8))
    (fp_line (start -3.6 -5.4) (end -3.6 -4.76)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 15a370fd-ad10-4229-86d5-5ad91c92fad4))
    (fp_line (start -3.6 -4.76) (end -2.54 -4.76)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 213ac80d-d58c-46ad-8304-3ce2086dc99f))
    (fp_line (start -3.6 -2.86) (end -3.6 -2.22)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7f381846-40d8-40b1-8acb-53df6bfa0662))
    (fp_line (start -3.6 -2.22) (end -2.54 -2.22)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 65dc7764-8bee-4613-8491-435528c58de0))
    (fp_line (start -3.6 -0.32) (end -3.6 0.32)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0db2834e-02ee-4b2f-bb60-e8e2db36c81b))
    (fp_line (start -3.6 0.32) (end -2.54 0.32)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 99d49971-3ee4-4758-a34b-aa3b38757bfe))
    (fp_line (start -3.6 2.22) (end -3.6 2.86)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0985c9e8-1343-4a71-8b74-c9907f45c1f3))
    (fp_line (start -3.6 2.86) (end -2.54 2.86)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 759145fb-275e-463d-b413-26f95adb92c3))
    (fp_line (start -3.6 4.76) (end -3.6 5.4)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e7eab085-2845-4677-81f4-c5f8e94b5459))
    (fp_line (start -3.6 5.4) (end -2.54 5.4)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ca1822bf-bd33-4819-b32f-4e047a471089))
    (fp_line (start -2.54 -5.4) (end -3.6 -5.4)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp fa9767d0-62cd-424a-9f1e-133796cbfc77))
    (fp_line (start -2.54 -5.4) (end -1.59 -6.35)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8a919954-30c3-41d6-80f6-3078ec20f0a5))
    (fp_line (start -2.54 -2.86) (end -3.6 -2.86)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1f76af6b-e0ee-4878-8779-de629604915b))
    (fp_line (start -2.54 -0.32) (end -3.6 -0.32)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 35997779-1ee3-42fe-b112-76fe363a2c62))
    (fp_line (start -2.54 2.22) (end -3.6 2.22)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e1aeebd0-166d-4a44-ad59-4132317b79b1))
    (fp_line (start -2.54 4.76) (end -3.6 4.76)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 323faf5c-ba7d-4a08-8cdb-b8f7fd49a7de))
    (fp_line (start -2.54 6.35) (end -2.54 -5.4)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1b04aa60-67f5-425b-9fce-38aad37ff6a3))
    (fp_line (start -1.59 -6.35) (end 2.54 -6.35)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 83e70539-dbb9-4d4d-bd67-fa1a8e434384))
    (fp_line (start 2.54 -6.35) (end 2.54 6.35)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 05a20495-0e10-4daa-b43f-c04111844541))
    (fp_line (start 2.54 -5.4) (end 3.6 -5.4)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 68d0ec91-8028-4e6f-8225-da340e6aa79d))
    (fp_line (start 2.54 -2.86) (end 3.6 -2.86)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 5a8e8827-4cec-4beb-9e39-0053187a01c2))
    (fp_line (start 2.54 -0.32) (end 3.6 -0.32)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2707fbcf-c0c2-4e89-bb58-fc40e1f95d0d))
    (fp_line (start 2.54 2.22) (end 3.6 2.22)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 15d77316-f77a-4ea2-83a2-7a1e31d4c679))
    (fp_line (start 2.54 4.76) (end 3.6 4.76)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 48b9fa6a-b63e-4623-9091-04bd3587ecf3))
    (fp_line (start 2.54 6.35) (end -2.54 6.35)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d6b32548-1806-4422-b163-dcc8378124b5))
    (fp_line (start 3.6 -5.4) (end 3.6 -4.76)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9f3ce85c-1c0d-4f93-a884-5215b86d58e9))
    (fp_line (start 3.6 -4.76) (end 2.54 -4.76)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 32685fac-7ec9-4adb-8923-473e9f04cf5e))
    (fp_line (start 3.6 -2.86) (end 3.6 -2.22)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 5c57eff4-c924-4699-aa08-293217cf6d9d))
    (fp_line (start 3.6 -2.22) (end 2.54 -2.22)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e120eb26-7c38-4dc9-b5a7-474d71cba312))
    (fp_line (start 3.6 -0.32) (end 3.6 0.32)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9f4051a4-de0a-4f31-adb7-5676fb4e9410))
    (fp_line (start 3.6 0.32) (end 2.54 0.32)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0dfa54b6-e745-4ac3-b20d-79ca042aa125))
    (fp_line (start 3.6 2.22) (end 3.6 2.86)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp db5b52d1-ba9e-418c-9b00-e27f87fc23f8))
    (fp_line (start 3.6 2.86) (end 2.54 2.86)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3816abe8-774f-4159-9860-ca1c6286dedb))
    (fp_line (start 3.6 4.76) (end 3.6 5.4)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp baeb6445-4461-4ff6-acd0-87e8b9123f95))
    (fp_line (start 3.6 5.4) (end 2.54 5.4)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d5d599ae-4299-400f-90b7-5ccbb4afdd71))
    (pad "1" smd rect (at -2.525 -5.08 90) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 17 "Net-(FB2-Pad1)") (pinfunction "1") (pintype "bidirectional") (tstamp 8b16518c-b416-4196-8ae9-73130d819138))
    (pad "2" smd rect (at 2.525 -5.08 90) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 17 "Net-(FB2-Pad1)") (pinfunction "2") (pintype "bidirectional") (tstamp 6dcdf6b7-f077-4347-a7c3-c155139f917f))
    (pad "3" smd rect (at -2.525 -2.54 90) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 1 "GND") (pinfunction "3") (pintype "bidirectional") (tstamp 77a95dd5-6378-4844-abfc-f12981dcb166))
    (pad "4" smd rect (at 2.525 -2.54 90) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 1 "GND") (pinfunction "4") (pintype "bidirectional") (tstamp 7206bf1e-27bd-446b-a0aa-0887ae3983f0))
    (pad "5" smd rect (at -2.525 0 90) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 1 "GND") (pinfunction "5") (pintype "bidirectional") (tstamp 427dd760-6503-48e4-93e3-f880a47a5a25))
    (pad "6" smd rect (at 2.525 0 90) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 1 "GND") (pinfunction "6") (pintype "bidirectional") (tstamp 66cf6b64-3eb4-4f0c-a35c-28b4228ae286))
    (pad "7" smd rect (at -2.525 2.54 90) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 1 "GND") (pinfunction "7") (pintype "bidirectional") (tstamp 730dbeba-0e63-43cf-9917-3fdfc46d9052))
    (pad "8" smd rect (at 2.525 2.54 90) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 1 "GND") (pinfunction "8") (pintype "bidirectional") (tstamp ad7db2ae-92ea-4dfb-9ba1-0850b24ad4a1))
    (pad "9" smd rect (at -2.525 5.08 90) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 16 "Net-(FB1-Pad1)") (pinfunction "9") (pintype "bidirectional") (tstamp 5e1c9df7-da45-465a-9868-2ba2df35cdc0))
    (pad "10" smd rect (at 2.525 5.08 90) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 16 "Net-(FB1-Pad1)") (pinfunction "10") (pintype "bidirectional") (tstamp 70e901be-548d-408b-9367-e04834a3a5f1))
    (model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_2x05_P2.54mm_Vertical_SMD.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Connector_PinHeader_2.54mm:PinHeader_1x02_P2.54mm_Vertical" (layer "F.Cu")
    (tstamp 6a3d1899-21f2-45b6-9847-174f8d3e2b06)
    (at 130.86 36.7 90)
    (descr "Through hole straight pin header, 1x02, 2.54mm pitch, single row")
    (tags "Through hole pin header THT 1x02 2.54mm single row")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Generic connector, double row, 02x01, this symbol is compatible with counter-clockwise, top-bottom and odd-even numbering schemes., script generated (kicad-library-utils/schlib/autogen/connector/)")
    (property "ki_keywords" "connector")
    (path "/d8b5e628-0b1a-4945-a72a-c9771f53d2d1")
    (attr through_hole exclude_from_pos_files)
    (fp_text reference "J8" (at 0.85 4.9 180) (layer "F.SilkS")
        (effects (font (size 0.6 0.6) (thickness 0.15)))
      (tstamp 9a6aea4e-0b92-4369-bdcc-2dc256714730)
    )
    (fp_text value "Conn_02x01" (at 0 4.87 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 96320c30-0d37-41f5-b65e-1da76ac08bb2)
    )
    (fp_text user "${REFERENCE}" (at 0 1.27) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp b61b1542-1b4c-4635-b552-7cb27dbe68a1)
    )
    (fp_line (start -1.33 -1.33) (end 0 -1.33)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 53d664e9-cb25-4523-892f-f26791cb98e7))
    (fp_line (start -1.33 0) (end -1.33 -1.33)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp ba3fd22e-8ae9-4482-b0f4-4b241d3b7582))
    (fp_line (start -1.33 1.27) (end -1.33 3.87)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 5a263cbf-8043-413e-ad03-d776c0a03431))
    (fp_line (start -1.33 1.27) (end 1.33 1.27)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp c1c3d24f-ae73-486e-9160-66e105d92933))
    (fp_line (start -1.33 3.87) (end 1.33 3.87)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 7eea08c4-11c3-44f3-b653-4861f13c8a2c))
    (fp_line (start 1.33 1.27) (end 1.33 3.87)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 2678e4fb-9b08-425a-8d55-cfa4df7fe67c))
    (fp_line (start -1.8 -1.8) (end -1.8 4.35)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 40ed432c-39f1-42b8-8211-af96aadd10a5))
    (fp_line (start -1.8 4.35) (end 1.8 4.35)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 7fbebc9f-795c-4292-8767-2195b95f0da6))
    (fp_line (start 1.8 -1.8) (end -1.8 -1.8)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp ac8b914d-9e47-40f5-ac75-4e268a452a02))
    (fp_line (start 1.8 4.35) (end 1.8 -1.8)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 66c4841f-2db5-40fc-bbaa-1a36b1791997))
    (fp_line (start -1.27 -0.635) (end -0.635 -1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6f605234-de36-4274-be71-c63622217a62))
    (fp_line (start -1.27 3.81) (end -1.27 -0.635)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3d5f0179-2e26-4e2d-9b84-5934b225c6d1))
    (fp_line (start -0.635 -1.27) (end 1.27 -1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8260172d-75f7-4e49-9a5f-d217c816d44f))
    (fp_line (start 1.27 -1.27) (end 1.27 3.81)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ae9b427b-017f-48dd-90d2-8566aa6a23fa))
    (fp_line (start 1.27 3.81) (end -1.27 3.81)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a4f9e271-8036-4d0f-929f-17af08109041))
    (pad "1" thru_hole rect (at 0 0 90) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask")
      (net 25 "/GPIO1_Ext2") (pinfunction "Pin_1") (pintype "passive") (tstamp 7aca7791-f45c-491d-9799-988cc9183049))
    (pad "2" thru_hole oval (at 0 2.54 90) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask")
      (net 26 "/GPIO0_Ext1") (pinfunction "Pin_2") (pintype "passive") (tstamp c6a67080-fe93-491a-bbdd-7d2481f1e581))
    (model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_1x02_P2.54mm_Vertical.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0805_2012Metric_Pad1.18x1.45mm_HandSolder" (layer "F.Cu")
    (tstamp 6ac24808-f5b3-44d9-8a06-3ca216dc5891)
    (at 177.9 43.04 -90)
    (descr "Capacitor SMD 0805 (2012 Metric), square (rectangular) end terminal, IPC_7351 nominal with elongated pad for handsoldering. (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf, https://docs.google.com/spreadsheets/d/1BsfQQcO9C6DZCsRaXUlFlo91Tg2WpOkGARC1WS5S8t0/edit?usp=sharing), generated with kicad-footprint-generator")
    (tags "capacitor handsolder")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/a66b3afb-cc2a-4842-ab1a-ea2a8d9122c6")
    (attr smd)
    (fp_text reference "C8" (at 3.075 0.175 90) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp d63d22a4-862d-44a3-9b66-8ded27e4e1f9)
    )
    (fp_text value "22u 0805 " (at 0 1.68 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp dec76159-af29-465e-83f6-716062f8a3db)
    )
    (fp_text user "${REFERENCE}" (at -2.57 -0.03 90) (layer "F.Fab")
        (effects (font (size 0.5 0.5) (thickness 0.08)))
      (tstamp 81b6e926-64bf-4d48-ba3d-124ed4ba9a0d)
    )
    (fp_line (start -0.261252 -0.735) (end 0.261252 -0.735)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp b8d36e8b-4a8e-4c76-ab5c-54b26f5471a0))
    (fp_line (start -0.261252 0.735) (end 0.261252 0.735)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp d7404f02-86f3-436d-8332-03b703005e8e))
    (fp_line (start -1.88 -0.98) (end 1.88 -0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 3600a3eb-cd1b-4d0d-81c7-dd02e47c9425))
    (fp_line (start -1.88 0.98) (end -1.88 -0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 8b7d98b9-697d-42e7-9d96-1e12ef285a60))
    (fp_line (start 1.88 -0.98) (end 1.88 0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 0560904a-7395-4d16-83fd-b6f034c410c9))
    (fp_line (start 1.88 0.98) (end -1.88 0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 1f32db87-9d22-48f5-a9ad-e3b7bdcf4541))
    (fp_line (start -1 -0.625) (end 1 -0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9a34d986-0b53-47d3-ac6d-eb488b7e1eb9))
    (fp_line (start -1 0.625) (end -1 -0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e2f1bd36-426a-4063-b167-8e9f8c95d327))
    (fp_line (start 1 -0.625) (end 1 0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f5f02d9f-a82c-4fe8-8c7d-e80cae3c4152))
    (fp_line (start 1 0.625) (end -1 0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1ae9b4cb-398a-490d-8042-14103a5af315))
    (pad "1" smd roundrect (at -1.0375 0 270) (size 1.175 1.45) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.2127659574)
      (net 9 "/+3V3_SIG") (pintype "passive") (tstamp 7aaf3e0c-2548-4953-a12b-100b9579f798))
    (pad "2" smd roundrect (at 1.0375 0 270) (size 1.175 1.45) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.2127659574)
      (net 1 "GND") (pintype "passive") (zone_connect 2) (tstamp 27bdc398-8d75-4865-bd7a-fce000b776db))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0805_2012Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_0603_1608Metric" (layer "F.Cu")
    (tstamp 6dd10a58-0ca3-4003-b148-73402c1236f8)
    (at 144.925 37.575 180)
    (descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Resistor")
    (property "ki_keywords" "R res resistor")
    (path "/dceeb258-6325-46a2-b973-79e5ab5dd749")
    (attr smd)
    (fp_text reference "R31" (at -0.05 1.55) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp af8f3b74-c5f0-40ec-ab22-81d0b40a7f83)
    )
    (fp_text value "1.5k" (at 0 1.43) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 6de6e81c-29aa-491e-b76e-5df9a59cbf42)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.4 0.4) (thickness 0.06)))
      (tstamp 8350b316-9a84-4ec5-9f7c-f86ff469965a)
    )
    (fp_line (start -0.237258 -0.5225) (end 0.237258 -0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp a1f698ec-0033-4b12-a049-cfa54de23ac9))
    (fp_line (start -0.237258 0.5225) (end 0.237258 0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp a678ccd5-884d-4382-8a7d-ae56ad750fd9))
    (fp_line (start -1.48 -0.73) (end 1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 6722a70e-811e-42d2-a7fe-081801c76598))
    (fp_line (start -1.48 0.73) (end -1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 62493e6b-6fad-4966-8711-9a932227f652))
    (fp_line (start 1.48 -0.73) (end 1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 0d000312-f930-4a0a-9ff5-5ebb349158eb))
    (fp_line (start 1.48 0.73) (end -1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp bc20d510-9db9-421d-9fa3-3af195122f83))
    (fp_line (start -0.8 -0.4125) (end 0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6ab3e7f5-b9df-49ff-9625-fed9b9494d01))
    (fp_line (start -0.8 0.4125) (end -0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1af8094e-d726-4742-8607-e988eb935db9))
    (fp_line (start 0.8 -0.4125) (end 0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f1e43104-a571-4449-a40c-2c8438a9ddfa))
    (fp_line (start 0.8 0.4125) (end -0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 46a8eb49-3348-47de-a00e-dca8cdd75f40))
    (pad "1" smd roundrect (at -0.825 0 180) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 26 "/GPIO0_Ext1") (pintype "passive") (tstamp 1a89c43d-060c-475b-a2cf-e804f0e184e1))
    (pad "2" smd roundrect (at 0.825 0 180) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pintype "passive") (tstamp 36d68e3a-4ffb-44fd-aa99-13244397614a))
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "F.Cu")
    (tstamp 6f1f0250-aaa6-46eb-9446-64d44c3fb3db)
    (at 171.435622 39.296425)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/ff7b528a-6667-43db-bafe-7eaaa306c5e6")
    (attr smd)
    (fp_text reference "C12" (at 1.28028 -1.068148) (layer "F.SilkS")
        (effects (font (size 0.5 0.5) (thickness 0.125) bold))
      (tstamp 8b2f5c7b-9c48-4488-b74e-9d7cb16ce62b)
    )
    (fp_text value "1u" (at 0 1.16) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp c232036d-e242-4dbc-897e-f59fa700e445)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp bcb6ea76-a1be-486f-8099-0a545f165f6e)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 6cb64273-d451-4017-a642-25f3e8308ab7))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 0b1b75da-e95c-43fc-9cfa-bfc5127212fc))
    (fp_line (start -0.91 -0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp c00a5cc6-e27f-4809-8965-60e68fd54c8c))
    (fp_line (start -0.91 0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 711638c2-09ad-47c8-a679-9b2c72b17b5e))
    (fp_line (start 0.91 -0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 589dc6ac-2010-4c57-9d5f-eef4bfbfab24))
    (fp_line (start 0.91 0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 5788b780-a007-43ff-a6cb-d837e5e9558a))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2477be6b-13fa-4f38-8dda-507b30f68f97))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b88b0c77-fd89-434a-9c89-30c06a867e09))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ee701f8f-ebc6-4d45-a3a6-c1448de3624a))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a921a6bd-2244-4202-80be-96fa42a81b6d))
    (pad "1" smd roundrect (at -0.48 0) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 10 "+1V1") (pintype "passive") (tstamp b8fe5e7b-8740-44cc-9629-367ebdc79768))
    (pad "2" smd roundrect (at 0.48 0) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp 46f90940-3104-481d-b159-4535d912b433))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "F.Cu")
    (tstamp 7176f37e-5582-4232-8880-85329b1c14c0)
    (at 170.800622 37.851425 -90)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Resistor")
    (property "ki_keywords" "R res resistor")
    (path "/8ba16ef7-38d4-4250-a7b7-357f14a375ac")
    (attr smd)
    (fp_text reference "R22" (at -2.185 -0.075 90) (layer "F.SilkS")
        (effects (font (size 0.5 0.5) (thickness 0.125) bold))
      (tstamp 5945f10e-d35e-4a50-9b78-15ed7fe622a2)
    )
    (fp_text value "27" (at 0 1.16 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp a750bd34-ffd0-4609-bee2-73d3481cbc4d)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp 30863d8a-f348-427b-9506-1be1e04643d5)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 1bf65c00-4014-4fb8-96d9-1ef6a3f0ac42))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp a1e0c961-a7df-4e38-96f7-c16a8d5e5324))
    (fp_line (start -0.91 -0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 4d5b05e3-8752-4371-af72-2c21af713b00))
    (fp_line (start -0.91 0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp de7b5803-78ae-4381-a536-9d0b44158457))
    (fp_line (start 0.91 -0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 874170f4-ee59-486d-bf3c-9d613b6b117c))
    (fp_line (start 0.91 0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 2c0226c5-a62a-4916-a8ba-175ba8887ece))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a31c6acd-28bb-4848-b59c-07725e3495eb))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6961eb80-bf59-4eaf-bf97-83bedd3d803a))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c8fac4c7-9f1f-4153-b398-05b87d5fe772))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 60c234de-4a1e-4237-850c-4cf6c432a0a1))
    (pad "1" smd roundrect (at -0.48 0 270) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 20 "/USB_D-") (pintype "passive") (tstamp f1222e6d-cd2b-4c2f-9dce-96acc2fcbf9c))
    (pad "2" smd roundrect (at 0.48 0 270) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 32 "Net-(U7-USB_DM)") (pintype "passive") (tstamp 5b247ad8-81bf-4517-8e46-240697b880bc))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Package_SON:WSON-6-1EP_2x2mm_P0.65mm_EP1x1.6mm_ThermalVias" (layer "F.Cu")
    (tstamp 78818276-bd9e-478a-929c-2fdf350c38f8)
    (at 180.455 42.405)
    (descr "WSON, 6 Pin (http://www.ti.com/lit/ds/symlink/tps61040.pdf#page=35), generated with kicad-footprint-generator ipc_noLead_generator.py")
    (tags "WSON NoLead")
    (property "Link" "https://jlcpcb.com/partdetail/TexasInstruments-TLV76733DRVR/C2848334")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "1A Precision Linear Voltage Regulator, with enable pin, Fixed Output 3.3V, WSON6")
    (property "ki_keywords" "1A Precision Linear Voltage Regulator")
    (path "/55756b39-97f4-4213-9862-f2e0782554dd")
    (zone_connect 2)
    (attr smd)
    (fp_text reference "U4" (at -0.445 -1.845) (layer "F.SilkS")
        (effects (font (size 0.5 0.5) (thickness 0.125)))
      (tstamp dd533f5d-fd21-411e-bfa2-890ae6eeb826)
    )
    (fp_text value "TLV76733DRVR" (at 0 1.95) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp bb3037f7-6915-4051-b614-e271d96d30c2)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.5 0.5) (thickness 0.08)))
      (tstamp 9d902f1f-f0d9-4132-a5ca-28e4c04a45d8)
    )
    (fp_line (start -1 1.11) (end 1 1.11)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp a6f2b6e0-038a-4fd0-bcc5-774650512908))
    (fp_line (start 0 -1.11) (end 1 -1.11)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 44164350-9c6f-444a-b27a-b3a034622f57))
    (fp_line (start -1.32 -1.25) (end -1.32 1.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 68229529-388e-413f-8dc5-4cd8994012f6))
    (fp_line (start -1.32 1.25) (end 1.32 1.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp d14d98e3-fadd-497d-8a13-0485673d33fb))
    (fp_line (start 1.32 -1.25) (end -1.32 -1.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 4de5b1db-bf16-4f0b-ad14-3f0bdd4ad1cb))
    (fp_line (start 1.32 1.25) (end 1.32 -1.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 2db3aea3-3ec1-4beb-b44a-8cff54f8a829))
    (fp_line (start -1 -0.5) (end -0.5 -1)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 09bb097e-fdaa-4b78-a0a3-4ac3ceef482e))
    (fp_line (start -1 1) (end -1 -0.5)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3441955f-b20d-40ed-8625-c296b719303b))
    (fp_line (start -0.5 -1) (end 1 -1)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 73422c59-9fea-425c-8fab-9d033c2a153c))
    (fp_line (start 1 -1) (end 1 1)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 16dbeb8f-efda-45ce-be64-5da8394f1023))
    (fp_line (start 1 1) (end -1 1)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f779ba7b-3ff4-4119-870c-57f4f4d24057))
    (pad "" smd roundrect (at 0 -0.4) (size 0.87 0.69) (layers "F.Paste") (roundrect_rratio 0.25) (tstamp 142022d1-2590-4df9-ae47-43f9c846e25a))
    (pad "" smd roundrect (at 0 0.4) (size 0.87 0.69) (layers "F.Paste") (roundrect_rratio 0.25) (tstamp 4c7326ad-a7a8-48e1-acaa-6b39cc29d17c))
    (pad "1" smd roundrect (at -0.8875 -0.65) (size 0.375 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 9 "/+3V3_SIG") (pinfunction "OUT") (pintype "power_out") (tstamp d7dad4a8-7a60-4c7e-a467-ca94704611ef))
    (pad "2" smd roundrect (at -0.8875 0) (size 0.375 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 9 "/+3V3_SIG") (pinfunction "SNS") (pintype "input") (tstamp 52a21c7f-cde4-441e-b248-77dd0bb4c5c1))
    (pad "3" smd roundrect (at -0.8875 0.65) (size 0.375 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pinfunction "GND") (pintype "power_in") (tstamp d171d921-36e6-433c-877e-1cdb2e82b170))
    (pad "4" smd roundrect (at 0.8875 0.65) (size 0.375 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 2 "+12V") (pinfunction "EN") (pintype "input") (tstamp 557a9cfe-a8d7-4b84-ae7d-3397853891c4))
    (pad "5" smd roundrect (at 0.8875 0) (size 0.375 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pinfunction "GND") (pintype "passive") (tstamp f6472434-f2f2-4271-b88d-7b94cc9b8b11))
    (pad "6" smd roundrect (at 0.8875 -0.65) (size 0.375 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 2 "+12V") (pinfunction "IN") (pintype "power_in") (tstamp a5a12401-60de-4606-90e3-a7047fcc27db))
    (pad "7" thru_hole circle (at 0 -0.55) (size 0.5 0.5) (drill 0.2) (layers "*.Cu")
      (net 1 "GND") (pinfunction "PAD") (pintype "passive") (zone_connect 2) (tstamp b997fae3-80bc-4b37-a568-25f5080916ab))
    (pad "7" smd rect (at 0 0) (size 0.5 1.6) (layers "B.Cu")
      (net 1 "GND") (pinfunction "PAD") (pintype "passive") (tstamp 7b7d165c-4a84-4b28-8b8d-7fd78801a2fb))
    (pad "7" smd rect (at 0 0) (size 1 1.6) (layers "F.Cu" "F.Mask")
      (net 1 "GND") (pinfunction "PAD") (pintype "passive") (tstamp 1b0628d8-4c3d-400e-b778-0f0cc7fe81ce))
    (pad "7" thru_hole circle (at 0 0.55) (size 0.5 0.5) (drill 0.2) (layers "*.Cu")
      (net 1 "GND") (pinfunction "PAD") (pintype "passive") (zone_connect 2) (tstamp 74164c40-ceb0-4e1b-a2dc-0ae93e541360))
    (model "${KICAD6_3DMODEL_DIR}/Package_SON.3dshapes/WSON-6-1EP_2x2mm_P0.65mm_EP1x1.6mm.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Crystal:Crystal_SMD_3225-4Pin_3.2x2.5mm" (layer "F.Cu")
    (tstamp 9572093a-1916-4419-8909-969ae9859b0d)
    (at 165.375622 52.766425)
    (descr "SMD Crystal SERIES SMD3225/4 http://www.txccrystal.com/images/pdf/7m-accuracy.pdf, 3.2x2.5mm^2 package")
    (tags "SMD SMT crystal")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Four pin crystal, GND on pins 2 and 4")
    (property "ki_keywords" "quartz ceramic resonator oscillator")
    (path "/f307fec1-3eea-492e-ab37-be5d10e1d092")
    (attr smd)
    (fp_text reference "Y1" (at 0.15 2.7) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 6551aa5e-6798-4e82-abad-6945e4cb88b1)
    )
    (fp_text value "YXC X322512MSB4SI" (at 0 2.45) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 8e0213a9-c806-49a7-84d1-e63aa6e937fa)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.7 0.7) (thickness 0.105)))
      (tstamp 2acaf814-b1e3-4846-8330-1bfc24a09f57)
    )
    (fp_line (start -2 -1.65) (end -2 1.65)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp b2196c17-9583-444a-83f3-8657432006e7))
    (fp_line (start -2 1.65) (end 2 1.65)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp a05a58ed-5234-4e23-a005-b0b71e8e3225))
    (fp_line (start -2.1 -1.7) (end -2.1 1.7)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 824d8a24-28cf-42ee-b639-4c8beccdb333))
    (fp_line (start -2.1 1.7) (end 2.1 1.7)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 5f636b6f-74dc-4e00-af53-2de22986138f))
    (fp_line (start 2.1 -1.7) (end -2.1 -1.7)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp baafa70e-3972-4888-a0e6-a7098a1fda52))
    (fp_line (start 2.1 1.7) (end 2.1 -1.7)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 1e61cb00-f5e3-4cc7-9d16-99c1cbb060be))
    (fp_line (start -1.6 -1.25) (end -1.6 1.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0a2c2914-e9f5-4479-9ae5-4a97107175ed))
    (fp_line (start -1.6 0.25) (end -0.6 1.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 20d2c93c-fd8c-458b-b60c-97926f5b4d9c))
    (fp_line (start -1.6 1.25) (end 1.6 1.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ab3a8bba-eb90-491e-a730-dfe9bd33060e))
    (fp_line (start 1.6 -1.25) (end -1.6 -1.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ced32301-eb0f-45a8-aa22-fa1d8c461363))
    (fp_line (start 1.6 1.25) (end 1.6 -1.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp eceeedf7-a5ac-4b75-a2df-0bd6e284eebf))
    (pad "1" smd rect (at -1.1 0.85) (size 1.4 1.2) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 11 "/XIN") (pinfunction "1") (pintype "passive") (tstamp 1ec3f8b2-10e9-4968-a9b6-ad3725dee1f6))
    (pad "2" smd rect (at 1.1 0.85) (size 1.4 1.2) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 1 "GND") (pinfunction "2") (pintype "passive") (tstamp 1352212e-07e0-4c3c-82df-06d3749bc940))
    (pad "3" smd rect (at 1.1 -0.85) (size 1.4 1.2) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 12 "Net-(C28-Pad1)") (pinfunction "3") (pintype "passive") (tstamp 9b63e8b8-22b5-451d-8ea5-5a7e33f5fad5))
    (pad "4" smd rect (at -1.1 -0.85) (size 1.4 1.2) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 1 "GND") (pinfunction "4") (pintype "passive") (tstamp ab850e08-20fe-4385-ac39-0f718089c2d2))
    (model "${KICAD6_3DMODEL_DIR}/Crystal.3dshapes/Crystal_SMD_3225-4Pin_3.2x2.5mm.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder" (layer "F.Cu")
    (tstamp 98c71699-42e1-4e89-bf9a-afd48ade7c49)
    (at 180.75 35.85 -90)
    (descr "Capacitor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal with elongated pad for handsoldering. (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor handsolder")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/39fe015b-cf8c-45f7-95d1-5bc95d527acd")
    (attr smd)
    (fp_text reference "C1" (at 3.592 3.02 90) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp d6a9b4cc-a174-4e33-8b8a-e8472d957eac)
    )
    (fp_text value "1u" (at 0 1.43 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 011f6df7-9120-4e33-bbf9-1cd441a5c477)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab")
        (effects (font (size 0.4 0.4) (thickness 0.06)))
      (tstamp f96b2fe2-2f64-47a5-86cd-d29c4b373bf1)
    )
    (fp_line (start -0.146267 -0.51) (end 0.146267 -0.51)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp cf8596be-1f90-417d-ba0a-33854e2c1277))
    (fp_line (start -0.146267 0.51) (end 0.146267 0.51)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 7866597e-464c-4d9e-bda5-60ceb484ed4c))
    (fp_line (start -1.65 -0.73) (end 1.65 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 5844e088-8653-44c5-a548-355dcdcbb8e6))
    (fp_line (start -1.65 0.73) (end -1.65 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 41220ad9-9316-4cd8-91c4-0b08b709f647))
    (fp_line (start 1.65 -0.73) (end 1.65 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 233962dd-f7c2-46dc-b4ac-2a454ad4c920))
    (fp_line (start 1.65 0.73) (end -1.65 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 7971037f-55ee-4460-a729-b84bcccb83ac))
    (fp_line (start -0.8 -0.4) (end 0.8 -0.4)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d884b7f5-c032-4802-b8e5-0778f9e7544e))
    (fp_line (start -0.8 0.4) (end -0.8 -0.4)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 871bd013-2a49-4088-95e1-86b29e5df09d))
    (fp_line (start 0.8 -0.4) (end 0.8 0.4)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7b533994-351c-4d6c-b226-bfdb9bc82384))
    (fp_line (start 0.8 0.4) (end -0.8 0.4)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 34f6884c-8fd6-4194-812a-90ba514f46ed))
    (pad "1" smd roundrect (at -0.8625 0 270) (size 1.075 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 2 "+12V") (pintype "passive") (tstamp 5362b50e-cc6e-4d90-9559-7415d189ad32))
    (pad "2" smd roundrect (at 0.8625 0 270) (size 1.075 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp d10e58ce-0621-4269-80cc-3522a39527da))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "F.Cu")
    (tstamp 9981e019-1cf8-4a5c-8e62-bc84bd82fcb5)
    (at 150.225 8.155 -90)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/f013620a-7a64-4d41-9620-2af2fcc2ade1")
    (attr smd)
    (fp_text reference "C33" (at -1.85 0.9 -180) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 6c7e9e9d-1db7-42dd-a409-010916d1d8bf)
    )
    (fp_text value "100n" (at 0 1.16 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 0f7e1616-fd4c-49dc-817a-4b11b0f7878b)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp 9afd09c9-55c6-4925-96ae-73e7b3e64b86)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 75d55eaf-426c-4f49-9dc5-35d6622b32ef))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 34adc7c8-2fd7-42ab-908c-71b67f2fdca1))
    (fp_line (start -0.91 -0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 2480e4f1-9d39-4c68-91ed-ed3f18bcf7ca))
    (fp_line (start -0.91 0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp ff88b693-f66d-477e-870e-f535046182f5))
    (fp_line (start 0.91 -0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 0769581c-c24e-4dda-a197-1f08afad04e3))
    (fp_line (start 0.91 0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 3ce358e2-39d3-48ad-8987-f0981017fc3e))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7c1440f9-39f9-4e38-8a2a-b51d66498194))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 821ec55f-9fb1-45cc-bc51-08f7dc66e5ec))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0b08128c-74f9-4360-baeb-89b81f87d911))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e981f0f1-64dd-4b42-b169-30514ecb1ae9))
    (pad "1" smd roundrect (at -0.48 0 270) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pintype "passive") (tstamp a54e0e06-e2fc-4037-90f4-aa3079df8240))
    (pad "2" smd roundrect (at 0.48 0 270) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp a8bf8455-27fa-413c-9c05-33cad2a5c465))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_0603_1608Metric" (layer "F.Cu")
    (tstamp a1de2df3-1481-4605-b649-f29958aaae09)
    (at 137.525 76.95 -90)
    (descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "LCSC" "")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Resistor")
    (property "ki_keywords" "R res resistor")
    (path "/a6b26d49-3511-4379-b51c-29b09d1a74f5")
    (attr smd)
    (fp_text reference "R23" (at 0 -1.43 90) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 4504ba3c-6733-4f16-925b-a5d5d0d59802)
    )
    (fp_text value "18k" (at 0 1.43 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 748d7b2f-1179-4ae3-9574-919da3e913cf)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab")
        (effects (font (size 0.4 0.4) (thickness 0.06)))
      (tstamp adaf3a24-fb6e-4c9c-b580-a886bbcb4e1e)
    )
    (fp_line (start -0.237258 -0.5225) (end 0.237258 -0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp dde3d5a5-84f8-4b34-92b0-0c2e9830982c))
    (fp_line (start -0.237258 0.5225) (end 0.237258 0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp cdd632e3-554e-4215-96a7-163024b31f16))
    (fp_line (start -1.48 -0.73) (end 1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp edaf2359-041c-4d7c-b117-7e260a62bbcd))
    (fp_line (start -1.48 0.73) (end -1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp bdf33513-733e-4a2f-834c-db00a7cc50e4))
    (fp_line (start 1.48 -0.73) (end 1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp eb14cf58-23c2-4353-ab94-b898af132a00))
    (fp_line (start 1.48 0.73) (end -1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp c08d0456-1300-40e4-9139-456f5ee325ac))
    (fp_line (start -0.8 -0.4125) (end 0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7cd523bb-bb06-4a74-8be4-4ca26fdfe5ea))
    (fp_line (start -0.8 0.4125) (end -0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 23be697e-223d-44c2-8fb3-195d1e1fac13))
    (fp_line (start 0.8 -0.4125) (end 0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 74bdbe1b-2d9c-4d95-9776-d23545744954))
    (fp_line (start 0.8 0.4125) (end -0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c59dc06a-0d90-47e3-9a27-cba9e7252f11))
    (pad "1" smd roundrect (at -0.825 0 270) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 33 "/GPIO5_LED_In1") (pintype "passive") (tstamp f2106909-0c03-4258-9eb7-f7c783a50ccc))
    (pad "2" smd roundrect (at 0.825 0 270) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 15 "Net-(D5-A)") (pintype "passive") (tstamp 304d95e5-eea1-41e2-95bf-289bbcb3e527))
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "F.Cu")
    (tstamp a3ac4823-57f0-4d53-b553-b0ce6e0776b1)
    (at 173.755622 39.726425)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/11c05d23-3ab7-46de-ab53-c3b5a48fef01")
    (attr smd)
    (fp_text reference "C15" (at 0.48 -0.85) (layer "F.SilkS")
        (effects (font (size 0.5 0.5) (thickness 0.125) bold))
      (tstamp fe5ebac9-25ae-45a9-8c54-d1b79b5d72dc)
    )
    (fp_text value "1u" (at 0 1.16) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 7790396c-d82c-4f3c-8861-c3a84a760893)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp 9faa1944-99ae-4546-8487-cf1589dfad11)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f6c96a32-dee9-42e2-902a-6e6d52f38265))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 64a1a511-e6bb-4d1a-a8eb-859d85e17ac1))
    (fp_line (start -0.91 -0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 4ac6de90-cc43-4be7-bbe1-b1900ca1ff51))
    (fp_line (start -0.91 0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 258a1b5a-8792-489b-9cd2-f9dfb4b6359c))
    (fp_line (start 0.91 -0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 85dd7cda-e975-4a07-934f-44f17185b66f))
    (fp_line (start 0.91 0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 97fd6a56-e70e-4c0b-b464-34b379034cb0))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9a230687-7471-4246-956f-cce66b44dd0c))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d1472b81-0a95-4d2d-8b52-26e2f744b399))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c7490d67-2bd6-46cf-8080-0b261da14714))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 81fa34d3-488c-4465-9bb8-8f35e70c4eb3))
    (pad "1" smd roundrect (at -0.48 0) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pintype "passive") (tstamp c7dd259f-3c63-4134-b955-afd77cb34183))
    (pad "2" smd roundrect (at 0.48 0) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp 921f85c4-8dc1-4de9-adda-8cc310232bc7))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0805_2012Metric_Pad1.18x1.45mm_HandSolder" (layer "F.Cu")
    (tstamp a7818a5b-d2ee-4da2-98d7-e67c15b8305a)
    (at 175.9 35.2125 -90)
    (descr "Capacitor SMD 0805 (2012 Metric), square (rectangular) end terminal, IPC_7351 nominal with elongated pad for handsoldering. (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf, https://docs.google.com/spreadsheets/d/1BsfQQcO9C6DZCsRaXUlFlo91Tg2WpOkGARC1WS5S8t0/edit?usp=sharing), generated with kicad-footprint-generator")
    (tags "capacitor handsolder")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/59100c7d-237f-4f61-ad20-27fb3110f0fc")
    (attr smd)
    (fp_text reference "C2" (at 2.7675 -0.82 -180) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 82075693-8d92-49d3-9ba5-0f39442286bd)
    )
    (fp_text value "22u 0805 " (at 0 1.68 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 209a2707-6b4d-44e5-86fd-973a83c4d536)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab")
        (effects (font (size 0.5 0.5) (thickness 0.08)))
      (tstamp 783938d2-788d-4d98-b983-69aa0975689f)
    )
    (fp_line (start -0.261252 -0.735) (end 0.261252 -0.735)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 4b67e589-5424-4e14-93c7-45fb8cc3b952))
    (fp_line (start -0.261252 0.735) (end 0.261252 0.735)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp ab99ae58-bfd5-4fa6-8f04-17a08a256304))
    (fp_line (start -1.88 -0.98) (end 1.88 -0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 43dbdc3e-e27b-4c55-867a-986c1b2c7335))
    (fp_line (start -1.88 0.98) (end -1.88 -0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp e878ef31-cd41-4182-b145-99df37d696f6))
    (fp_line (start 1.88 -0.98) (end 1.88 0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 2f41a656-8996-44db-8982-51e75f7025ed))
    (fp_line (start 1.88 0.98) (end -1.88 0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 06ea021d-d8a6-404b-bf0a-f58963ddad95))
    (fp_line (start -1 -0.625) (end 1 -0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 379046b7-a5a3-4bca-93de-bc0b0cee2c07))
    (fp_line (start -1 0.625) (end -1 -0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b109222d-ea2a-42a9-9711-d8985b5bbdbe))
    (fp_line (start 1 -0.625) (end 1 0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1a1ff21e-8cc7-441f-a15e-fc6edc8d6ab5))
    (fp_line (start 1 0.625) (end -1 0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c21c5ea6-4b58-413e-948d-be0b81e0129c))
    (pad "1" smd roundrect (at -1.0375 0 270) (size 1.175 1.45) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.2127659574)
      (net 5 "+3V3") (pintype "passive") (tstamp fca4e3c2-ffd4-4d37-88bf-b481225dadc1))
    (pad "2" smd roundrect (at 1.0375 0 270) (size 1.175 1.45) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.2127659574)
      (net 1 "GND") (pintype "passive") (tstamp b5914d91-255b-4628-9ebd-362cdf4c6219))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0805_2012Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_0402_1005Metric" (layer "F.Cu")
    (tstamp a93a97c7-067d-45cc-becb-4bbe8da88b79)
    (at 152.7 23.49 180)
    (descr "Resistor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Resistor")
    (property "ki_keywords" "R res resistor")
    (path "/a6b6cee8-e1c9-43fd-b7a0-3a680dfe315b")
    (attr smd)
    (fp_text reference "R6" (at -1.79 -0.1) (layer "F.SilkS")
        (effects (font (size 0.5 0.5) (thickness 0.125)))
      (tstamp 35620e55-98bd-4c5b-86b2-4e889b3f8339)
    )
    (fp_text value "5.1k" (at 0 1.17) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 686d9df2-e281-4327-b1d4-19d0f69e6d92)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.26 0.26) (thickness 0.04)))
      (tstamp 4394fd8a-dbf3-4b3c-b79a-9bd2925f29dd)
    )
    (fp_line (start -0.153641 -0.38) (end 0.153641 -0.38)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp c949510d-e8c2-44eb-bec2-4cd88204a2eb))
    (fp_line (start -0.153641 0.38) (end 0.153641 0.38)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp b6b6ce32-92ad-46e5-95a9-15cb4f4bbe7e))
    (fp_line (start -0.93 -0.47) (end 0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 2025942b-d33b-4315-862f-8eeafdee7ade))
    (fp_line (start -0.93 0.47) (end -0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 1e841d38-bc1f-467e-9d22-a1935d969bea))
    (fp_line (start 0.93 -0.47) (end 0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 09b4fa0a-84ac-4ca9-bb94-dbf4fd861242))
    (fp_line (start 0.93 0.47) (end -0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 2c7d05cc-eb30-4b1e-88a6-2bc973837674))
    (fp_line (start -0.525 -0.27) (end 0.525 -0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 521b8cfb-1232-4cd4-b51e-712806e324b6))
    (fp_line (start -0.525 0.27) (end -0.525 -0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a132c27f-5e94-4b85-a9d8-3f089c8780a4))
    (fp_line (start 0.525 -0.27) (end 0.525 0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 276d851d-82ee-45de-b082-f06fec6af492))
    (fp_line (start 0.525 0.27) (end -0.525 0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 4c2fbdd3-8ba0-4a92-8ecf-4f6f4e89e85a))
    (pad "1" smd roundrect (at -0.51 0 180) (size 0.54 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 18 "Net-(J1-CC1)") (pintype "passive") (tstamp a1d2d0ee-2590-495b-a045-7feb7e1b3080))
    (pad "2" smd roundrect (at 0.51 0 180) (size 0.54 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp 48bb1edf-d185-4f68-994b-b21718b5974d))
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0805_2012Metric" (layer "F.Cu")
    (tstamp b3239033-d871-45c9-91b7-5424634177d2)
    (at 184.01 33.89)
    (descr "Capacitor SMD 0805 (2012 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf, https://docs.google.com/spreadsheets/d/1BsfQQcO9C6DZCsRaXUlFlo91Tg2WpOkGARC1WS5S8t0/edit?usp=sharing), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/fb71347e-d55a-4a74-8550-66b0379aaea5")
    (attr smd)
    (fp_text reference "C3" (at -2.47 -0.29) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp fac424e1-fc25-49ac-a6c7-585dc459a6e2)
    )
    (fp_text value "22u" (at 0 1.68) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 3a3df1de-937f-432c-b918-70056f0f32e0)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.5 0.5) (thickness 0.08)))
      (tstamp fec3b5b4-3d85-4639-8b8a-b24f55c073d8)
    )
    (fp_line (start -0.261252 -0.735) (end 0.261252 -0.735)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 9537cc07-d856-4192-a769-2480b22ef21f))
    (fp_line (start -0.261252 0.735) (end 0.261252 0.735)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp e826d87b-a08e-477b-81e0-bb605989efe1))
    (fp_line (start -1.7 -0.98) (end 1.7 -0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 493807f7-15ab-4abc-a8e2-61595ddb9073))
    (fp_line (start -1.7 0.98) (end -1.7 -0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 549ed75a-0ec0-4e59-9140-24c0472f3391))
    (fp_line (start 1.7 -0.98) (end 1.7 0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 4b250980-99a4-4c78-b0fe-558eedc46abd))
    (fp_line (start 1.7 0.98) (end -1.7 0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 5a207e52-e9a3-48c1-93be-6e4ea9041f99))
    (fp_line (start -1 -0.625) (end 1 -0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6694aa01-74a9-4b8c-a1fe-001aea76a29b))
    (fp_line (start -1 0.625) (end -1 -0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7f0e4b6b-cc16-4034-9268-fa8e741a358c))
    (fp_line (start 1 -0.625) (end 1 0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 4e908b83-4e13-4553-b8e3-0448f1a99d07))
    (fp_line (start 1 0.625) (end -1 0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3ae487eb-aff1-45e0-a6e9-38a0b827f299))
    (pad "1" smd roundrect (at -0.95 0) (size 1 1.45) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 2 "+12V") (pintype "passive") (tstamp 4ec3079c-0899-4858-a198-46c97343f430))
    (pad "2" smd roundrect (at 0.95 0) (size 1 1.45) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp 2f1cf059-158b-4bf3-bb79-7a93d08c4bd8))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0805_2012Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Diode_SMD:D_SOD-323" (layer "F.Cu")
    (tstamp b45fb335-1065-4275-ba47-3cc0366d2c48)
    (at 184.09 38.81 180)
    (descr "SOD-323")
    (tags "SOD-323")
    (property "LCSC" "")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "30V 1A Schottky Barrier Rectifier Diode, DO-41")
    (property "ki_keywords" "diode Schottky")
    (path "/2e11cec4-fbf4-4263-be57-ded14c1990c8")
    (attr smd)
    (fp_text reference "D3" (at -0.68 -1.91 180) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp ef158e3c-65a8-4c93-864b-734bb49c0780)
    )
    (fp_text value "1N5819" (at 0.1 1.9 180) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 85cf0109-64c4-440d-afc9-a7f0e0c0c64d)
    )
    (fp_text user "${REFERENCE}" (at 0 -1.85 180) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 1e72632c-10cd-40fd-9d88-0720320cdede)
    )
    (fp_line (start -1.61 -0.85) (end -1.61 0.85)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 6b8ec4d5-ecd3-4cf1-82b7-09eca2a4fc25))
    (fp_line (start -1.61 -0.85) (end 1.05 -0.85)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 331d6680-a376-4999-a385-5f652f3ee4e5))
    (fp_line (start -1.61 0.85) (end 1.05 0.85)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp b623d744-7aba-4181-9bed-cb0ab83794f2))
    (fp_line (start -1.6 -0.95) (end -1.6 0.95)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 100282da-62ec-4a09-b6aa-38aef38ac93d))
    (fp_line (start -1.6 -0.95) (end 1.6 -0.95)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp e6767c4d-ee66-48e9-8a6f-330729211fab))
    (fp_line (start -1.6 0.95) (end 1.6 0.95)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp b845829d-3d65-4ec1-8f13-a97126c7570b))
    (fp_line (start 1.6 -0.95) (end 1.6 0.95)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp ad6d25b0-ba61-4889-afae-6fe753251e2b))
    (fp_line (start -0.9 -0.7) (end 0.9 -0.7)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1f6f0a35-65ec-4b5c-bcc7-d2c6e9b63901))
    (fp_line (start -0.9 0.7) (end -0.9 -0.7)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 4396727c-094a-4445-a30a-c0d48e09cd7d))
    (fp_line (start -0.3 -0.35) (end -0.3 0.35)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 72c0cc1b-ffcb-464d-b91c-b71ae60b51e1))
    (fp_line (start -0.3 0) (end -0.5 0)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3746198d-f657-4823-951b-ea5f60132d74))
    (fp_line (start -0.3 0) (end 0.2 -0.35)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 4c713cec-b59d-4eee-99fd-2e84f8548f30))
    (fp_line (start 0.2 -0.35) (end 0.2 0.35)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f45d847c-23ab-40b5-9e61-91c6de4c959a))
    (fp_line (start 0.2 0) (end 0.45 0)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 34c85939-f648-4e8e-8bd6-365413e09966))
    (fp_line (start 0.2 0.35) (end -0.3 0)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 14ef8ad6-6cb6-439e-8313-e72e3b755070))
    (fp_line (start 0.9 -0.7) (end 0.9 0.7)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 35f20d77-8749-4812-8d4b-87811f71bf1b))
    (fp_line (start 0.9 0.7) (end -0.9 0.7)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e3b32c3d-5245-4dd4-abcb-ce461b26e975))
    (pad "1" smd roundrect (at -1.05 0 180) (size 0.6 0.45) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 13 "Net-(D3-K)") (pinfunction "K") (pintype "passive") (tstamp 558f5acf-c5a9-40ee-a472-f9c84d112119))
    (pad "2" smd roundrect (at 1.05 0 180) (size 0.6 0.45) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 7 "-12V") (pinfunction "A") (pintype "passive") (tstamp f3e527c9-9f15-45b9-b867-963682aec365))
    (model "${KICAD6_3DMODEL_DIR}/Diode_SMD.3dshapes/D_SOD-323.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "F.Cu")
    (tstamp c4f8fa0d-e716-43e8-b0da-907772f47f86)
    (at 168.125622 53.666425 -90)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/2e17c74c-5b41-463c-8ea3-8ac3445fcc7b")
    (attr smd)
    (fp_text reference "C28" (at 1.7 -0.25 -180) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 3936690c-a72f-4888-83de-886d732b200d)
    )
    (fp_text value "30p" (at 0 1.16 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 955ece0e-1f2c-40bb-bc8c-1998ff6e230d)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp 261904ec-0e99-43af-ab85-6db73d990d06)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 3d0850a3-a259-4a63-8cb6-39b782f9bd98))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 6a2b0470-6ff6-4976-9afb-9a36116ac4d6))
    (fp_line (start -0.91 -0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 9bc6fa14-5afb-461d-9d96-74f8e781bde6))
    (fp_line (start -0.91 0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 01270354-abcb-44e1-aa11-ba456958ee7f))
    (fp_line (start 0.91 -0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp c8a8dcdc-d4c4-484d-89ec-40cbbc143084))
    (fp_line (start 0.91 0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 6b2b9617-2def-430c-9d4e-4fc1d748cbd9))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 5704528a-a857-43cf-8818-14355083979a))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 643b1ad8-61be-421e-9fbb-1e1ce3502ddd))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8a5213d6-6f15-4051-8cb2-1a573360034e))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d49157d5-b082-46ef-9528-20a94ea8c25b))
    (pad "1" smd roundrect (at -0.48 0 270) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 12 "Net-(C28-Pad1)") (pintype "passive") (tstamp fc21d340-477f-45fe-a9a5-164e2b4b5bd1))
    (pad "2" smd roundrect (at 0.48 0 270) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp 5cca82ce-5957-44dd-b3c6-4cfa4a754b2f))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "F.Cu")
    (tstamp c75ee430-d26f-4ea9-af38-dc295d2b7a3e)
    (at 167.900622 37.171425 90)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/5dfeddf7-78fb-4037-a177-452a967ca0c3")
    (attr smd)
    (fp_text reference "C16" (at 2.555 0.925 90) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp f8fecde8-f029-428a-a3c1-e1b3667245a8)
    )
    (fp_text value "100n" (at 0 1.16 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 3c351146-159d-4429-996f-b54b17143156)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp 9aaca110-a63d-41f3-9418-789046cf6a77)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 9a8957fb-d16a-468a-ad88-98cbde17cf9d))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 946163f7-303e-4187-b3af-0723b53fe01e))
    (fp_line (start -0.91 -0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 7dddef9f-1f9f-41c9-b53f-bc089275a82a))
    (fp_line (start -0.91 0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 46793d24-fd30-49ec-8995-58498873a7f4))
    (fp_line (start 0.91 -0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 94580350-d3f8-4ea2-bb29-833c27cb82e9))
    (fp_line (start 0.91 0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 64528286-1764-445f-bc26-2351a62f4326))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 71982096-8de7-4142-a31e-1d182ac29300))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 5edf718a-8ea8-4ea5-be94-3121d38e3ce0))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0310e209-c942-422f-aeab-29cb29cc763a))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 11b90bc3-20f3-4895-b462-aaf08e1f3743))
    (pad "1" smd roundrect (at -0.48 0 90) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pintype "passive") (tstamp 5d15cc82-90a9-403d-b37b-f77c0dc3fc16))
    (pad "2" smd roundrect (at 0.48 0 90) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp b4b4a1d9-7935-453b-896e-bd5832cb6949))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Diode_SMD:D_SOD-323" (layer "F.Cu")
    (tstamp cd85e52f-1468-4529-830a-9949ca8e73e1)
    (at 184.89 26.09 90)
    (descr "SOD-323")
    (tags "SOD-323")
    (property "LCSC" "")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "30V 1A Schottky Barrier Rectifier Diode, DO-41")
    (property "ki_keywords" "diode Schottky")
    (path "/ca69ed9c-c315-4f2f-9dcb-c5dc0098a1d3")
    (attr smd)
    (fp_text reference "D4" (at -2.45 0.68 90) (layer "F.SilkS")
        (effects (font (size 0.5 0.5) (thickness 0.125)))
      (tstamp f0bcef44-4646-48c7-b941-05a850f2c9ce)
    )
    (fp_text value "1N5819" (at 0.1 1.9 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp c42529ab-aeb5-457b-9900-36172872e071)
    )
    (fp_text user "${REFERENCE}" (at 0 -1.85 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 7779b62a-5c1f-4100-a771-b2c3d634c32a)
    )
    (fp_line (start -1.61 -0.85) (end -1.61 0.85)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 22fc566f-bdd6-4b7d-bd91-3f1c79fc3c84))
    (fp_line (start -1.61 -0.85) (end 1.05 -0.85)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp ed351484-aff6-4f4c-909e-1d9e48834499))
    (fp_line (start -1.61 0.85) (end 1.05 0.85)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 9997413f-e138-4e28-9d46-261d51a7a60c))
    (fp_line (start -1.6 -0.95) (end -1.6 0.95)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 5f47be72-75a6-43fb-9350-070b8ebc30c7))
    (fp_line (start -1.6 -0.95) (end 1.6 -0.95)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 032d3f75-fac3-49c5-b914-f7164389cdb2))
    (fp_line (start -1.6 0.95) (end 1.6 0.95)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp fe0417ad-8efb-406f-a0a8-d671d04bc9f2))
    (fp_line (start 1.6 -0.95) (end 1.6 0.95)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 03038340-3eb6-47f9-bfb8-05629aff5f34))
    (fp_line (start -0.9 -0.7) (end 0.9 -0.7)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 29bf3efc-6708-4a23-b8c2-119b1f638d7b))
    (fp_line (start -0.9 0.7) (end -0.9 -0.7)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp dc69f815-bc6b-4a3b-99f1-b164293c1170))
    (fp_line (start -0.3 -0.35) (end -0.3 0.35)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0212c998-54e9-4c30-a358-97de6a31bf3f))
    (fp_line (start -0.3 0) (end -0.5 0)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3a4e918d-bc6b-4fd6-b596-4dc84d438539))
    (fp_line (start -0.3 0) (end 0.2 -0.35)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3215d400-e18f-4f97-aa6f-a240e557be9d))
    (fp_line (start 0.2 -0.35) (end 0.2 0.35)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e77c9184-36e9-4107-805f-b1e4442b37d0))
    (fp_line (start 0.2 0) (end 0.45 0)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d62c4be5-1f6f-45aa-9811-28bc0568d1e5))
    (fp_line (start 0.2 0.35) (end -0.3 0)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2bea0f2b-a055-4afc-8530-b25a086522e9))
    (fp_line (start 0.9 -0.7) (end 0.9 0.7)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 878b6d61-11e1-49bb-b51a-c85530e38fe7))
    (fp_line (start 0.9 0.7) (end -0.9 0.7)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ed899704-cff7-4898-b147-a2381b46f175))
    (pad "1" smd roundrect (at -1.05 0 90) (size 0.6 0.45) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 2 "+12V") (pinfunction "K") (pintype "passive") (tstamp 5c2500e9-b08d-462d-8afe-9ca341165124))
    (pad "2" smd roundrect (at 1.05 0 90) (size 0.6 0.45) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 14 "Net-(D4-A)") (pinfunction "A") (pintype "passive") (tstamp 6cb5b65f-f505-4cc2-8334-fb8dc0f9843f))
    (model "${KICAD6_3DMODEL_DIR}/Diode_SMD.3dshapes/D_SOD-323.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0603_1608Metric_Pad1.08x0.95mm_HandSolder" (layer "F.Cu")
    (tstamp ceb7e2bd-4c47-471b-aeee-24c65eb4ffdb)
    (at 182.93 41.58 -90)
    (descr "Capacitor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal with elongated pad for handsoldering. (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor handsolder")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/8793a6b5-460d-4f08-b604-c2f794863258")
    (attr smd)
    (fp_text reference "C5" (at -1.105 1.255 90) (layer "F.SilkS")
        (effects (font (size 0.5 0.5) (thickness 0.125)))
      (tstamp ed23fabd-c0ef-4744-864e-1bf38d6e2d67)
    )
    (fp_text value "1u" (at 0 1.43 -270) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp e598125d-dfde-48b8-9406-5943688fd6a0)
    )
    (fp_text user "${REFERENCE}" (at 0 0 -270) (layer "F.Fab")
        (effects (font (size 0.4 0.4) (thickness 0.06)))
      (tstamp f1f2dafc-f6d3-4172-ba6b-e3f0cc38f5f0)
    )
    (fp_line (start -0.146267 -0.51) (end 0.146267 -0.51)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 25e80816-e7e3-464f-9334-70d353368989))
    (fp_line (start -0.146267 0.51) (end 0.146267 0.51)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp aa2d2da0-5aa3-4ffa-889e-38ed2d927c9d))
    (fp_line (start -1.65 -0.73) (end 1.65 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp a5e74722-5982-4104-bc0f-8d8134fbe412))
    (fp_line (start -1.65 0.73) (end -1.65 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 18ba9bde-a361-4e3e-8005-82b922d4d489))
    (fp_line (start 1.65 -0.73) (end 1.65 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp d46b9a87-d4be-47ab-8286-6d37c170df7b))
    (fp_line (start 1.65 0.73) (end -1.65 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 1861a43d-6057-44dc-abb5-4422d7190996))
    (fp_line (start -0.8 -0.4) (end 0.8 -0.4)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f9627bd3-d6de-4ed2-9be8-666374a5c170))
    (fp_line (start -0.8 0.4) (end -0.8 -0.4)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b7b21610-3a74-47ac-8f6d-62c4fe9e3e51))
    (fp_line (start 0.8 -0.4) (end 0.8 0.4)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 35d98d50-6295-4e4e-9c77-17ac85b07535))
    (fp_line (start 0.8 0.4) (end -0.8 0.4)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp aa0284cf-0041-42af-8cc0-6402947003e7))
    (pad "1" smd roundrect (at -0.8625 0 270) (size 1.075 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 2 "+12V") (pintype "passive") (tstamp fe3a2d6a-d902-40d7-9f1b-6f6bacb450d6))
    (pad "2" smd roundrect (at 0.8625 0 270) (size 1.075 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (zone_connect 2) (tstamp 81d71b63-9177-4ecd-80e3-c0998e4f08b2))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "F.Cu")
    (tstamp d2e20b21-7ba3-4b71-b911-4a3fda68b7c7)
    (at 165.075622 49.716425 180)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/368f333d-218a-4bbd-9879-5d241ee7cf84")
    (attr smd)
    (fp_text reference "C27" (at -2.75 -0.95) (layer "F.SilkS")
        (effects (font (size 0.5 0.5) (thickness 0.125) bold))
      (tstamp dcab328b-4dfb-483e-bb87-fbdc977a78d7)
    )
    (fp_text value "30p" (at 0 1.16) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 2cff1e59-5bba-485b-be52-bf2cb386f97c)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp 0f654665-5bf2-4e4f-93a1-9f329ceb6d51)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 666b205f-040a-4ced-b75e-25d1166f5015))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp ed22742b-e6ee-422b-b904-36cce6ba2e32))
    (fp_line (start -0.91 -0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 91d43091-a092-4d84-b1a9-b90d2da3ec34))
    (fp_line (start -0.91 0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp c208fbe0-2ad7-4780-bf4b-f039646a780f))
    (fp_line (start 0.91 -0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 169a1de4-c404-4932-adcc-3152a8c65bc4))
    (fp_line (start 0.91 0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp cdb36511-3ae7-46bc-88b5-8374ff085b41))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7d885ab4-2645-4481-9a38-2cf25487fe0e))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 443ed079-dabb-4f75-9737-06681a2f2ead))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ad2da670-6b28-469c-85b4-38e4cd1f0081))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 10ce4aba-6def-4e38-a4f0-886812898830))
    (pad "1" smd roundrect (at -0.48 0 180) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 11 "/XIN") (pintype "passive") (tstamp fd9cc193-7532-42c0-90ef-9f4d10089da4))
    (pad "2" smd roundrect (at 0.48 0 180) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp 75b1625f-ae4a-45ba-8059-6487c2fc9f9e))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "F.Cu")
    (tstamp d49beab6-b1a8-4da5-970e-d26f4b73e13f)
    (at 173.775622 40.816425)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/e00b9879-d27a-48a1-a8e6-f96885666ff6")
    (attr smd)
    (fp_text reference "C37" (at 2.32 0.03) (layer "F.SilkS")
        (effects (font (size 0.5 0.5) (thickness 0.125)))
      (tstamp 6c427876-af0f-458b-bb08-472f11191dc7)
    )
    (fp_text value "100n" (at 0 1.16) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 47728b71-b836-47d7-8b32-26074a0d4681)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp 1853c5be-9719-4d1b-ad99-fc818c595885)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 49f23feb-7aaf-41a3-a705-3632f2386f0f))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 6a3107e1-d65a-4de4-8da9-855143698b12))
    (fp_line (start -0.91 -0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp b0119a6b-2b51-4c69-8f9c-b84b30568ccc))
    (fp_line (start -0.91 0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp b3518b11-a359-413d-b1ad-2b33aea45f87))
    (fp_line (start 0.91 -0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 69dd91e0-ee8d-491c-b1f6-269bfd9cfd89))
    (fp_line (start 0.91 0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp ba58cdf7-4386-4688-bddb-94497d79fbcb))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b535512d-7fb6-44b4-bac8-44aae0c6cba4))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 4d1cd882-6778-41c9-99a1-a63fdf8cd913))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3cae6909-9d5b-4e38-b791-0b781dcea8fc))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3cce49ef-8408-4262-a227-27d048104884))
    (pad "1" smd roundrect (at -0.48 0) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 9 "/+3V3_SIG") (pintype "passive") (tstamp aba1b930-763c-4a00-b07b-491ddd042a3a))
    (pad "2" smd roundrect (at 0.48 0) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp 88deadc4-2fd7-4e65-a809-c7ad80c912c6))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Connector_PinHeader_2.54mm:PinHeader_1x02_P2.54mm_Vertical" (layer "F.Cu")
    (tstamp d9c2ad60-7b34-46b6-bd89-0fdd208b25a2)
    (at 130.91 40.95 90)
    (descr "Through hole straight pin header, 1x02, 2.54mm pitch, single row")
    (tags "Through hole pin header THT 1x02 2.54mm single row")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Generic connector, double row, 02x01, this symbol is compatible with counter-clockwise, top-bottom and odd-even numbering schemes., script generated (kicad-library-utils/schlib/autogen/connector/)")
    (property "ki_keywords" "connector")
    (path "/aca0f23c-909a-4edb-a9b9-3e3db41bce51")
    (attr through_hole exclude_from_pos_files)
    (fp_text reference "J7" (at -0.825 4.975 180) (layer "F.SilkS")
        (effects (font (size 0.6 0.6) (thickness 0.15)))
      (tstamp 5f183e2d-efcb-46f8-a35a-cd13def2c1df)
    )
    (fp_text value "Conn_02x01" (at 0 4.87 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp b52fd53a-b2f2-4eaa-86b0-8bea701211bc)
    )
    (fp_text user "${REFERENCE}" (at 0 1.27) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp b47f3f72-0517-4536-91d2-1d2b39c0eea7)
    )
    (fp_line (start -1.33 -1.33) (end 0 -1.33)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 17890bfc-04e1-47a2-9e58-43c6b079db88))
    (fp_line (start -1.33 0) (end -1.33 -1.33)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp ced510bf-59ea-4289-87ee-8a80306dbbc9))
    (fp_line (start -1.33 1.27) (end -1.33 3.87)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 4951382a-f695-41fe-9d0c-6774382f7fd8))
    (fp_line (start -1.33 1.27) (end 1.33 1.27)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp c8c01df1-d26a-4d44-93ff-cb4f8527ce5b))
    (fp_line (start -1.33 3.87) (end 1.33 3.87)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 068f2dc2-58fd-42b9-92af-9da143ffff2e))
    (fp_line (start 1.33 1.27) (end 1.33 3.87)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 6dd8b331-f03c-4dd6-99ac-e62131a16cb2))
    (fp_line (start -1.8 -1.8) (end -1.8 4.35)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 7d68928d-628a-4585-937b-0e3d71216481))
    (fp_line (start -1.8 4.35) (end 1.8 4.35)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp be197ce1-e707-4e08-b632-b09f2f189952))
    (fp_line (start 1.8 -1.8) (end -1.8 -1.8)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 75193338-8237-4811-924c-7c599899cdb9))
    (fp_line (start 1.8 4.35) (end 1.8 -1.8)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 73641ead-1d82-4f5d-befa-f88209391b1f))
    (fp_line (start -1.27 -0.635) (end -0.635 -1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f130a358-5e74-4659-a232-980c0c81f98d))
    (fp_line (start -1.27 3.81) (end -1.27 -0.635)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e6d47cc1-f43e-4487-999f-c0827072d0b5))
    (fp_line (start -0.635 -1.27) (end 1.27 -1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7661730d-3e17-4c5d-8b07-920434a876fd))
    (fp_line (start 1.27 -1.27) (end 1.27 3.81)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9ab188b9-bfb3-4e1e-8f0f-d3d32f38002a))
    (fp_line (start 1.27 3.81) (end -1.27 3.81)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 79ad1bb2-fc86-4ff9-8183-5102f757aa25))
    (pad "1" thru_hole rect (at 0 0 90) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask")
      (net 25 "/GPIO1_Ext2") (pinfunction "Pin_1") (pintype "passive") (tstamp fccbef94-30d6-4a29-9fe1-b2388e594e66))
    (pad "2" thru_hole oval (at 0 2.54 90) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask")
      (net 26 "/GPIO0_Ext1") (pinfunction "Pin_2") (pintype "passive") (tstamp 41ed7bf1-a1ef-41c1-bea2-4db0e4551be2))
    (model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_1x02_P2.54mm_Vertical.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "F.Cu")
    (tstamp e6011ce5-a712-4a49-8e62-cf3185ad18bb)
    (at 169.800622 37.851425 -90)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Resistor")
    (property "ki_keywords" "R res resistor")
    (path "/b455bf6e-31d5-466f-b7c7-af817bd1b7cd")
    (attr smd)
    (fp_text reference "R21" (at -2.185 -0.025 90) (layer "F.SilkS")
        (effects (font (size 0.5 0.5) (thickness 0.125)))
      (tstamp 3d47e9d7-cb34-45ec-a3eb-5b745e37a36a)
    )
    (fp_text value "27" (at 0 1.16 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 9dbdd224-1c7c-4cac-b00e-6fc22de1d858)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp 17d5ea39-a3a0-4043-bc78-2422ffaafe46)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 7857dfe1-9d71-46c5-b63c-0d80a101c685))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 58b10f1f-b178-4124-b92e-0a2eff8a42fc))
    (fp_line (start -0.91 -0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 922d25fc-d42c-4d6d-b10d-a8a52547ea76))
    (fp_line (start -0.91 0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp a807db2d-7e65-4034-a7ab-9009b47e0779))
    (fp_line (start 0.91 -0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp dcb0a37c-30c0-40f9-93e4-491a5a574751))
    (fp_line (start 0.91 0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 70599193-4060-46b8-9c72-b7833ea5ecb6))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 5e92b804-b6bc-43d1-abc0-f750e397b4db))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 792929f6-9975-4fb4-bf42-6d5b7f245b40))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp fb3d6222-a695-4cde-b2db-cd280b3c2ae2))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d9182f9c-2f42-45bf-b151-5c85759f1547))
    (pad "1" smd roundrect (at -0.48 0 270) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 19 "/USB_D+") (pintype "passive") (tstamp 0b9665df-d5a0-42b6-bf0a-690dc54d8b9b))
    (pad "2" smd roundrect (at 0.48 0 270) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 31 "Net-(U7-USB_DP)") (pintype "passive") (tstamp e5414300-55f8-4ba3-9dac-0c9048b89938))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0805_2012Metric" (layer "F.Cu")
    (tstamp e67cd4c1-3cb5-4303-8fb6-e1d1f2186bae)
    (at 151 27.85)
    (descr "Capacitor SMD 0805 (2012 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf, https://docs.google.com/spreadsheets/d/1BsfQQcO9C6DZCsRaXUlFlo91Tg2WpOkGARC1WS5S8t0/edit?usp=sharing), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Resistor")
    (property "ki_keywords" "R res resistor")
    (path "/d0189a18-21de-47b9-be93-933835b98bcc")
    (attr smd exclude_from_pos_files)
    (fp_text reference "R30" (at -0.05 2.07) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 21c2ccc0-a85d-4704-a7e0-72410a8b42a0)
    )
    (fp_text value "DNF" (at 0 1.68) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp bdb70f8e-9a4d-4110-a904-7dc883dd9f80)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.5 0.5) (thickness 0.08)))
      (tstamp 6b5eb730-c0d4-418e-b651-c48de29d635c)
    )
    (fp_line (start -0.261252 -0.735) (end 0.261252 -0.735)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp b22dbfa8-e4a0-49df-81ca-ab4b2d05a081))
    (fp_line (start -0.261252 0.735) (end 0.261252 0.735)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp ab2e0489-8b31-4e4a-b9d8-7d1d9d21afdb))
    (fp_line (start -1.7 -0.98) (end 1.7 -0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 29a0f536-6d18-4be0-8540-9dc17c0385a1))
    (fp_line (start -1.7 0.98) (end -1.7 -0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 46c017f2-fb7a-4d0d-8ee2-feb5711fe431))
    (fp_line (start 1.7 -0.98) (end 1.7 0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 24312f7f-4b73-494b-a181-6340300b2365))
    (fp_line (start 1.7 0.98) (end -1.7 0.98)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 06d0ca46-2452-4127-87c3-b778bf046752))
    (fp_line (start -1 -0.625) (end 1 -0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp cb454027-28fb-4f2d-9c8a-c752526d3087))
    (fp_line (start -1 0.625) (end -1 -0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ae495db2-98ca-41ab-a78d-7cee5425543b))
    (fp_line (start 1 -0.625) (end 1 0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7b38debc-8cba-49b7-b17d-505f03951611))
    (fp_line (start 1 0.625) (end -1 0.625)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b6dbc8fe-3c70-42cc-94f9-2720de41bd1f))
    (pad "1" smd roundrect (at -0.95 0) (size 1 1.45) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pintype "passive") (tstamp c35a7b54-0973-42cb-87ff-e606ddab45c9))
    (pad "2" smd roundrect (at 0.95 0) (size 1 1.45) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 39 "/QSPI_SS") (pintype "passive") (tstamp 912b7d21-0b1c-4e74-8d2f-891b2d16bf66))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0805_2012Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Package_SON:WSON-6-1EP_2x2mm_P0.65mm_EP1x1.6mm_ThermalVias" (layer "F.Cu")
    (tstamp eb59b316-c46c-46f4-ac13-3b5f0bce105f)
    (at 178.4 35.2625)
    (descr "WSON, 6 Pin (http://www.ti.com/lit/ds/symlink/tps61040.pdf#page=35), generated with kicad-footprint-generator ipc_noLead_generator.py")
    (tags "WSON NoLead")
    (property "Link" "https://jlcpcb.com/partdetail/TexasInstruments-TLV76733DRVR/C2848334")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "1A Precision Linear Voltage Regulator, with enable pin, Fixed Output 3.3V, WSON6")
    (property "ki_keywords" "1A Precision Linear Voltage Regulator")
    (path "/6bd26ed4-f4b3-4494-8708-a1c0a903344f")
    (zone_connect 2)
    (attr smd)
    (fp_text reference "U1" (at 0.09 -2.1325) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp fef7b67c-f4d4-4030-8ace-4a79bb8ca9ae)
    )
    (fp_text value "TLV76733DRVR" (at 0 1.95) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp afa37cff-a149-405f-9a7f-76a65b5ba8f7)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.5 0.5) (thickness 0.08)))
      (tstamp 86b39808-87de-43ef-8d4a-54800a3dab65)
    )
    (fp_line (start -1 1.11) (end 1 1.11)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 95bf858b-f5f0-4f13-a570-612d1a2bf3ef))
    (fp_line (start 0 -1.11) (end 1 -1.11)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f5a2886e-30e9-4089-a025-52738a434d4b))
    (fp_line (start -1.32 -1.25) (end -1.32 1.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 753336a4-aa5b-44e0-8929-2af5ebc8ee5d))
    (fp_line (start -1.32 1.25) (end 1.32 1.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 8faba22e-fe0c-49be-a63c-2ceb942b7964))
    (fp_line (start 1.32 -1.25) (end -1.32 -1.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp aec15eef-c268-4268-982a-03e35d35af89))
    (fp_line (start 1.32 1.25) (end 1.32 -1.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 2a49ea58-6321-477d-a09b-9558fc79afb5))
    (fp_line (start -1 -0.5) (end -0.5 -1)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 74dbf2ab-c0a6-40d8-83e7-7c896f394bbd))
    (fp_line (start -1 1) (end -1 -0.5)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 17e1d638-1fbd-4499-889e-6135cb1bddc9))
    (fp_line (start -0.5 -1) (end 1 -1)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1f7d31ef-eb2d-4a1f-8cdf-13112a4fc07a))
    (fp_line (start 1 -1) (end 1 1)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d780b019-34fd-4a34-9ede-b18cdeeef4cf))
    (fp_line (start 1 1) (end -1 1)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7426c98a-d588-4334-92be-e065298eda8b))
    (pad "" smd roundrect (at 0 -0.4) (size 0.87 0.69) (layers "F.Paste") (roundrect_rratio 0.25) (tstamp 12991d55-87b5-43b1-acd4-299e3aadc346))
    (pad "" smd roundrect (at 0 0.4) (size 0.87 0.69) (layers "F.Paste") (roundrect_rratio 0.25) (tstamp 56d523d7-f0e0-4562-9f70-07b569272043))
    (pad "1" smd roundrect (at -0.8875 -0.65) (size 0.375 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pinfunction "OUT") (pintype "power_out") (tstamp a581cde4-2475-454e-9377-6cdc2fe28064))
    (pad "2" smd roundrect (at -0.8875 0) (size 0.375 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pinfunction "SNS") (pintype "input") (tstamp 71a19533-2421-44c0-a2f7-be37fbd6b7a8))
    (pad "3" smd roundrect (at -0.8875 0.65) (size 0.375 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pinfunction "GND") (pintype "power_in") (tstamp 6554f5e8-2fcd-4554-a7da-4c8833656903))
    (pad "4" smd roundrect (at 0.8875 0.65) (size 0.375 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 2 "+12V") (pinfunction "EN") (pintype "input") (tstamp ff1582b7-5608-4c9f-856e-e97384245385))
    (pad "5" smd roundrect (at 0.8875 0) (size 0.375 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pinfunction "GND") (pintype "passive") (tstamp 693b02e1-9d7c-4a6b-b159-b05d112b0f3e))
    (pad "6" smd roundrect (at 0.8875 -0.65) (size 0.375 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 2 "+12V") (pinfunction "IN") (pintype "power_in") (tstamp 0a32d017-a638-45b1-93f1-7b9f1fef82ff))
    (pad "7" thru_hole circle (at 0 -0.55) (size 0.5 0.5) (drill 0.2) (layers "*.Cu")
      (net 1 "GND") (pinfunction "PAD") (pintype "passive") (zone_connect 2) (tstamp 0de84c2a-8585-4b15-a391-aa293b929765))
    (pad "7" smd rect (at 0 0) (size 0.5 1.6) (layers "B.Cu")
      (net 1 "GND") (pinfunction "PAD") (pintype "passive") (tstamp f7935582-9c67-48eb-bc57-055daafa86f3))
    (pad "7" smd rect (at 0 0) (size 1 1.6) (layers "F.Cu" "F.Mask")
      (net 1 "GND") (pinfunction "PAD") (pintype "passive") (tstamp 91a1f723-dea0-4a48-8cb9-cee4fb42b2b0))
    (pad "7" thru_hole circle (at 0 0.55) (size 0.5 0.5) (drill 0.2) (layers "*.Cu")
      (net 1 "GND") (pinfunction "PAD") (pintype "passive") (zone_connect 2) (tstamp 0a6a9158-4c8b-4813-9304-73a6031a52da))
    (model "${KICAD6_3DMODEL_DIR}/Package_SON.3dshapes/WSON-6-1EP_2x2mm_P0.65mm_EP1x1.6mm.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "F.Cu")
    (tstamp ece95cba-c22c-4bc3-a124-3735c6b4181e)
    (at 168.825622 37.841425 90)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/9bae3afb-1d83-4760-b2ca-ab8a2786cb5e")
    (attr smd)
    (fp_text reference "C14" (at 3.225 -1.45 90) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 82ae33af-5a86-4eef-83a7-3c8691cf7ad0)
    )
    (fp_text value "100n" (at 0 1.16 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp ce11eb94-f2f0-402a-a609-540bacea81d8)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp 5df90b02-c979-422d-ac62-e27b30ec508a)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 9e475f3e-88cc-491b-88d6-79809a2f6e40))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 10c0834d-91e5-4a99-8a1e-0dcf01588bff))
    (fp_line (start -0.91 -0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp c3c24920-42f6-4251-9979-6d9cb7817f63))
    (fp_line (start -0.91 0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp be7cbfc2-25d3-4a41-a8b9-34d10f8ed74c))
    (fp_line (start 0.91 -0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp f89681f5-bc69-46dc-ad97-78f9d638b018))
    (fp_line (start 0.91 0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 593ef6ea-455a-42a3-ab8e-49712ee41147))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ba1ad716-97f0-4e00-bcc1-c25886422465))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 356a0714-614b-40e5-ad3c-732315ccfa5e))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7616d48d-7a7f-4bc7-9525-a2bb3eac4fdd))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 194cc03f-c6a9-4c80-af96-54c13468edf1))
    (pad "1" smd roundrect (at -0.48 0 90) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pintype "passive") (tstamp 6d39c28b-dae8-4b2f-ae97-0a50a4ed642e))
    (pad "2" smd roundrect (at 0.48 0 90) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp c08cb4a7-33ca-410c-8eec-9d41476bea3e))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_0603_1608Metric" (layer "F.Cu")
    (tstamp ed8c1d4f-611e-4804-b8a9-09d28c6917f0)
    (at 144.925 39.225 180)
    (descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Resistor")
    (property "ki_keywords" "R res resistor")
    (path "/d6429129-08bb-4b9e-b90b-885dca98eccb")
    (attr smd)
    (fp_text reference "R37" (at -2.225 -1.55) (layer "F.SilkS") hide
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 23585c41-8e50-4679-8474-a86860725274)
    )
    (fp_text value "1.5k" (at 0 1.43) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 1a4c44c2-0706-442c-bc94-107e0e84325f)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.4 0.4) (thickness 0.06)))
      (tstamp 786d0b74-40ca-47e4-a242-dd02dd939e30)
    )
    (fp_line (start -0.237258 -0.5225) (end 0.237258 -0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 32e5e69b-0d05-4b4e-a5c2-2e51dbdfadf4))
    (fp_line (start -0.237258 0.5225) (end 0.237258 0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 8131c40f-3a10-4879-b774-c970404a3f6b))
    (fp_line (start -1.48 -0.73) (end 1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp c13b6284-45a8-4a55-9800-c4a3b97c8058))
    (fp_line (start -1.48 0.73) (end -1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 3023b1d9-7bae-486a-8092-3dda24c26013))
    (fp_line (start 1.48 -0.73) (end 1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp b7cc13c9-491d-445a-b5b6-626e6285c8f1))
    (fp_line (start 1.48 0.73) (end -1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 946a719e-375e-482a-b797-4119f20e0f20))
    (fp_line (start -0.8 -0.4125) (end 0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7894ed01-3a9d-4571-ad5a-825db9b71d72))
    (fp_line (start -0.8 0.4125) (end -0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0bbcd0e4-9c9c-4a3a-9bc0-460d39fe0967))
    (fp_line (start 0.8 -0.4125) (end 0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a904c76d-4566-4bd2-9828-d97b2839c289))
    (fp_line (start 0.8 0.4125) (end -0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 44905f18-f86f-4845-bd93-59ba5f5af1ad))
    (pad "1" smd roundrect (at -0.825 0 180) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 25 "/GPIO1_Ext2") (pintype "passive") (tstamp 3faade95-c554-4d90-a845-2527584537a0))
    (pad "2" smd roundrect (at 0.825 0 180) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pintype "passive") (tstamp 1eec3aed-a0bb-499c-8b4b-827dd3dc34f8))
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Package_SO:SOIC-8_5.23x5.23mm_P1.27mm" (layer "F.Cu")
    (tstamp ef04529c-9c77-4a73-b17d-bf65a3f4c93f)
    (at 143.75 6.775)
    (descr "SOIC, 8 Pin (http://www.winbond.com/resource-files/w25q32jv%20revg%2003272018%20plus.pdf#page=68), generated with kicad-footprint-generator ipc_gullwing_generator.py")
    (tags "SOIC SO")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "128Mb Serial Flash Memory, Standard/Dual/Quad SPI, SOIC-8")
    (property "ki_keywords" "flash memory SPI QPI DTR")
    (path "/3e368736-42ee-49e8-b8e6-92f23d085d37")
    (attr smd)
    (fp_text reference "U11" (at -1.075 3.6) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp f82ac247-94a2-42ff-afce-9b6eb9796777)
    )
    (fp_text value "W25Q128JVS" (at 0 3.56) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp cf064496-5e29-4eff-98b7-7a98bb04168c)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 4c58af42-1929-4e3a-ba2a-4d74d2d4abb9)
    )
    (fp_line (start -2.725 -2.725) (end -2.725 -2.465)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 71d2d7ad-4979-47e3-966e-4de242baae23))
    (fp_line (start -2.725 -2.465) (end -4.4 -2.465)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp caeaa1da-f173-41c4-8a29-3126fdcd6731))
    (fp_line (start -2.725 2.725) (end -2.725 2.465)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 3ff7be23-fe16-45f9-8486-85eefbf51bcf))
    (fp_line (start 0 -2.725) (end -2.725 -2.725)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 8a6bbbc4-17ef-478e-96fa-fdc22cc0348f))
    (fp_line (start 0 -2.725) (end 2.725 -2.725)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 84fd5274-f46f-4470-a30b-1a1cfd13da80))
    (fp_line (start 0 2.725) (end -2.725 2.725)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp d7313e41-1cf0-44a6-882a-2f615052d7c4))
    (fp_line (start 0 2.725) (end 2.725 2.725)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 37a1df80-8a34-4df0-9266-55ecc7fdbc4e))
    (fp_line (start 2.725 -2.725) (end 2.725 -2.465)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 2e0d6433-a57a-451a-bc9d-507f87dc740b))
    (fp_line (start 2.725 2.725) (end 2.725 2.465)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp cdbf54de-0f20-4c73-a9dd-645e3ffcf2df))
    (fp_line (start -4.65 -2.86) (end -4.65 2.86)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 0ebc2e02-01fe-4eb2-9007-a0ae1f591059))
    (fp_line (start -4.65 2.86) (end 4.65 2.86)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 82de77ff-9bc0-4490-9791-5fce2bed5960))
    (fp_line (start 4.65 -2.86) (end -4.65 -2.86)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 6f49f992-d7cd-4947-909f-6ed75b103751))
    (fp_line (start 4.65 2.86) (end 4.65 -2.86)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 37e552f9-04d5-4065-815d-7719942c8482))
    (fp_line (start -2.615 -1.615) (end -1.615 -2.615)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 158582b1-6f92-4897-bc6b-735029835f6c))
    (fp_line (start -2.615 2.615) (end -2.615 -1.615)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 60b1f555-1f91-4c16-b386-e58bd14e60bd))
    (fp_line (start -1.615 -2.615) (end 2.615 -2.615)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 485a0439-cf31-4741-bac8-b3853d748ca2))
    (fp_line (start 2.615 -2.615) (end 2.615 2.615)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d9c19591-9b13-4cda-8480-180cf07db7dd))
    (fp_line (start 2.615 2.615) (end -2.615 2.615)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0a57d4d5-8743-475f-86eb-9b4749120763))
    (pad "1" smd roundrect (at -3.6 -1.905) (size 1.6 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 39 "/QSPI_SS") (pinfunction "~{CS}") (pintype "input") (tstamp 4d4efed7-63c1-4c76-bf98-999cb3c45227))
    (pad "2" smd roundrect (at -3.6 -0.635) (size 1.6 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 64 "/QSPI_SD1") (pinfunction "DO(IO1)") (pintype "bidirectional") (tstamp 6b6956a1-9e61-4552-8a3e-15651c401bf3))
    (pad "3" smd roundrect (at -3.6 0.635) (size 1.6 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 63 "/QSPI_SD2") (pinfunction "IO2") (pintype "bidirectional") (tstamp 96e8aded-d64c-48fa-af66-bd969cd1855b))
    (pad "4" smd roundrect (at -3.6 1.905) (size 1.6 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pinfunction "GND") (pintype "power_in") (tstamp 9231c9a0-ec67-4805-8696-f3410b51d0a7))
    (pad "5" smd roundrect (at 3.6 1.905) (size 1.6 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 62 "/QSPI_SD0") (pinfunction "DI(IO0)") (pintype "bidirectional") (tstamp 2a7010df-4f86-4767-8426-c21f75ccb078))
    (pad "6" smd roundrect (at 3.6 0.635) (size 1.6 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 61 "/QSPI_SCLK") (pinfunction "CLK") (pintype "input") (tstamp 676eb199-ff72-4bb7-a7db-139e265a52d0))
    (pad "7" smd roundrect (at 3.6 -0.635) (size 1.6 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 60 "/QSPI_SD3") (pinfunction "IO3") (pintype "bidirectional") (tstamp 73b12e34-d998-44e7-8c25-1be35ca5ea79))
    (pad "8" smd roundrect (at 3.6 -1.905) (size 1.6 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pinfunction "VCC") (pintype "power_in") (tstamp 6292b291-25de-40e2-a560-9e136c6f2ef5))
    (model "${KICAD6_3DMODEL_DIR}/Package_SO.3dshapes/SOIC-8_5.23x5.23mm_P1.27mm.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "F.Cu")
    (tstamp f2b23ca8-b6cb-433e-9fd3-21e58130fc49)
    (at 169.375622 49.786425 -90)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/48c9f4d6-2548-491f-86ad-2d208bb387c4")
    (attr smd)
    (fp_text reference "C21" (at -0.17 -2.55 90) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 8a2dee77-c479-4c6f-822c-7a2ed43845c5)
    )
    (fp_text value "100n" (at 0 1.16 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 506023ba-5851-4ea4-8c66-42687f999247)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp 7c34ad8d-27d7-4001-8edb-bb7dc0598301)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp cc8f2c63-62a7-47b6-8eeb-833bcd1b985a))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 72e607f9-e428-4d93-babd-7be69ca92848))
    (fp_line (start -0.91 -0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp bd8ee6a2-efe7-432b-9a21-812357cd4853))
    (fp_line (start -0.91 0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp a99aa3b7-8bcf-41d6-bd9b-3ce93b13126b))
    (fp_line (start 0.91 -0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 7cd4f270-6707-4f54-b5d6-5c7a93994953))
    (fp_line (start 0.91 0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 2327d8fc-6e54-4247-acb1-b62a286cb061))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a452b614-de6a-4439-a023-b1197665b951))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp deaec5dd-5521-4be3-b0c8-939574808085))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 86d90616-cbd8-4e1a-91ab-0a9f9ec77612))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 4a6d5ca8-1e9c-4eda-a9ce-4aa5eeed4f1a))
    (pad "1" smd roundrect (at -0.48 0 270) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pintype "passive") (tstamp 07b5f703-cff7-4022-905b-4a1a215bc696))
    (pad "2" smd roundrect (at 0.48 0 270) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp ec265faa-2746-4f61-a330-89b7393e65a8))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "F.Cu")
    (tstamp f92ea1e9-a131-4ae0-934e-7c3da5f1f7f5)
    (at 173.786211 45.477014 -45)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Unpolarized capacitor")
    (property "ki_keywords" "cap capacitor")
    (path "/d2aa342f-5a6e-490d-a061-74da53611820")
    (attr smd)
    (fp_text reference "C17" (at 0 -1.16 135) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp e4639b03-9694-47d7-9250-731ea4a87846)
    )
    (fp_text value "100n" (at 0 1.16 135) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp e93d9559-1ca1-401f-9af8-1d99c7043768)
    )
    (fp_text user "${REFERENCE}" (at 0 0 135) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp 53605888-4d7c-42ac-b692-feebba1f87db)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 002c765f-d327-4edb-bf01-67606ba278fa))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 51091ad7-fae5-44d5-9d30-dedc78e3d646))
    (fp_line (start -0.91 -0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 10b10cfd-236a-4e22-92d1-ba20ce15f470))
    (fp_line (start -0.91 0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 6e4ca1a5-1e96-4865-b93b-3fe5337bf779))
    (fp_line (start 0.91 -0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 0d515b3f-164f-4c1c-be78-ddf7b633a4e1))
    (fp_line (start 0.91 0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 52e4ca6f-b56d-479c-b419-9abf8838a3c6))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3c7387e1-f12f-4f9e-9762-59539a83d866))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 755b7401-c2aa-4d34-bc6d-a0f00f88d9f4))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6308ff52-3eb6-4261-9e88-a6be5b245802))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3151b058-5b14-4495-91ee-206db3a527f2))
    (pad "1" smd roundrect (at -0.48 0 315) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "+3V3") (pintype "passive") (tstamp 80e0798c-e7e1-42e6-864a-630b1f0ad00b))
    (pad "2" smd roundrect (at 0.48 0 315) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive") (tstamp a786de66-83c7-47e7-a001-adb9678e84a4))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "TestPoint:TestPoint_Pad_1.5x1.5mm" (layer "B.Cu")
    (tstamp 4a86172e-16c5-427b-bd2d-c2391f82a453)
    (at 153.6 41.65 180)
    (descr "SMD rectangular pad as test Point, square 1.5mm side length")
    (tags "test point SMD pad rectangle square")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "test point (alternative probe-style design)")
    (property "ki_keywords" "test point tp")
    (path "/061bc1bd-f30f-419f-aabf-f6e21b43f91a")
    (attr exclude_from_pos_files)
    (fp_text reference "TP8" (at 0 1.648) (layer "B.SilkS")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 45180e90-51af-492c-8bd6-0d99cfefcc43)
    )
    (fp_text value "1V1" (at 0 -1.75) (layer "B.Fab")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp db8c8426-1b83-4f4a-b5c1-b532baf11cdf)
    )
    (fp_text user "${REFERENCE}" (at 0 1.65) (layer "B.Fab")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 0f5a21e6-3c2c-4349-b45e-d6b9a895dc63)
    )
    (fp_line (start -0.95 -0.95) (end -0.95 0.95)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp f35c4136-0ed6-48e5-8819-7961672a04b1))
    (fp_line (start -0.95 0.95) (end 0.95 0.95)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 8512d3a7-72ec-4b2c-8b8f-0f094b4ab858))
    (fp_line (start 0.95 -0.95) (end -0.95 -0.95)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp a0737b0e-7499-4609-9f4d-1ec8a9d1ee74))
    (fp_line (start 0.95 0.95) (end 0.95 -0.95)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 354d1902-3b71-488d-85bc-a02b4363ec1f))
    (fp_line (start -1.25 1.25) (end -1.25 -1.25)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp c24c84c5-f692-461e-9a84-e14fb9ca83ea))
    (fp_line (start -1.25 1.25) (end 1.25 1.25)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 07f2f527-d4ad-4f78-92d7-1588f4f6fc40))
    (fp_line (start 1.25 -1.25) (end -1.25 -1.25)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp afbc16cd-fb26-4631-9f13-59d49b11c319))
    (fp_line (start 1.25 -1.25) (end 1.25 1.25)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 2263efe0-046c-4ef6-ad13-f5453ecebfb5))
    (pad "1" smd rect (at 0 0 180) (size 1.5 1.5) (layers "B.Cu" "B.Mask")
      (net 10 "+1V1") (pinfunction "1") (pintype "passive") (tstamp 00ad81ef-f973-45e9-96d1-de20d2c47f41))
  )

  (footprint "digikey-footprints:Switch_Tactile_SMD_B3U-1000P" (layer "B.Cu")
    (tstamp 4f0b456e-7397-4300-b76d-e6953275b33e)
    (at 135.375 28.275)
    (property "Category" "Switches")
    (property "DK_Datasheet_Link" "https://omronfs.omron.com/en_US/ecb/products/pdf/en-b3u.pdf")
    (property "DK_Detail_Page" "/product-detail/en/omron-electronics-inc-emc-div/B3U-1000P/SW1020CT-ND/1534357")
    (property "Description" "SWITCH TACTILE SPST-NO 0.05A 12V")
    (property "Digi-Key_PN" "SW1020CT-ND")
    (property "Family" "Tactile Switches")
    (property "LCSC" "")
    (property "MPN" "B3U-1000P")
    (property "Manufacturer" "Omron Electronics Inc-EMC Div")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "Status" "Active")
    (property "ki_description" "SWITCH TACTILE SPST-NO 0.05A 12V")
    (property "ki_keywords" "SW1020CT-ND B3U")
    (path "/78ad805b-36e5-49ba-95c8-721638c98be9")
    (attr smd)
    (fp_text reference "S2" (at 3.47 0.65) (layer "B.SilkS")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 744eb531-d817-489f-8962-7de676d50428)
    )
    (fp_text value "B3U-1000P" (at 0 -2.63) (layer "B.Fab")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 1275846e-3dd0-409e-a2b0-f763481516b3)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "B.Fab")
        (effects (font (size 0.5 0.5) (thickness 0.05)) (justify mirror))
      (tstamp 0ed20b48-88e8-4a7f-9c44-f883914240c4)
    )
    (fp_line (start -1.6 -1.6) (end -1.6 -1.1)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp 40f5cd7d-0333-42cb-b313-916aa736a2af))
    (fp_line (start -1.6 -1.6) (end -1.1 -1.6)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp cc817834-deff-4060-afba-6898f65f6846))
    (fp_line (start -1.6 1.1) (end -2 1.1)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp 48e1f52d-d570-4ec2-99e4-bd2b80dbb706))
    (fp_line (start -1.6 1.6) (end -1.6 1.1)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp f7d73d35-8ac8-4fa1-86f2-00a3ac002503))
    (fp_line (start -1.6 1.6) (end -1.1 1.6)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp e4441e10-3c12-4ca0-af6b-49e1d24ed1cf))
    (fp_line (start 1.6 -1.6) (end 1.2 -1.6)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp b164ee52-75dd-4231-b973-2a3135a52304))
    (fp_line (start 1.6 -1.6) (end 1.6 -1.1)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp 4055333c-fb61-4fa4-8e5b-73de3949ec0c))
    (fp_line (start 1.6 1.6) (end 1.1 1.6)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp 533a3901-d661-4186-97bd-3e883729250f))
    (fp_line (start 1.6 1.6) (end 1.6 1.1)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp e89db7e2-f03c-4068-8b4e-116dd28557fb))
    (fp_line (start -2.35 -1.75) (end -2.35 1.75)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 3da3187d-f5ce-47a2-9860-ca2aa06024ff))
    (fp_line (start -2.35 -1.75) (end 2.35 -1.75)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 4014a567-7d83-4df2-985f-58aa3d50b626))
    (fp_line (start -2.35 1.75) (end 2.35 1.75)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 2b4de5a8-8c88-48cb-a9f6-d432f47e0a9b))
    (fp_line (start 2.35 -1.75) (end 2.35 1.75)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 2b39dc31-ac67-4a2f-91e5-058a026f6c67))
    (fp_line (start -1.5 -1.49) (end 1.5 -1.49)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 9f6ea245-0ae6-44bf-946b-fcdb90379b31))
    (fp_line (start -1.5 1.49) (end -1.5 -1.49)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 8c1990cc-9889-40a8-b612-18d0586e52d7))
    (fp_line (start -1.5 1.49) (end 1.5 1.49)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp fd83f6b0-b390-4777-9f5b-6ea9c4d309d7))
    (fp_line (start 1.5 1.49) (end 1.5 -1.49)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 6a2cb1e7-57b6-4e4b-a359-f778c55aa8c3))
    (pad "1" smd rect (at -1.8 0.005) (size 1 1.69) (layers "B.Cu" "B.Paste" "B.Mask")
      (net 1 "GND") (pintype "passive") (tstamp 608806b7-fe4f-4234-bcea-afb9dfdc5b43))
    (pad "2" smd rect (at 1.8 0.005) (size 1 1.69) (layers "B.Cu" "B.Paste" "B.Mask")
      (net 24 "Net-(R29-Pad2)") (pintype "passive") (tstamp bbf22cc0-a1eb-46c8-8891-6d861d709760))
  )

  (footprint "LED_THT:LED_D3.0mm_FlatTop" (layer "B.Cu")
    (tstamp 6ca54506-dd78-412b-aae5-8663fbe57225)
    (at 140.475 79.2 90)
    (descr "LED, Round, FlatTop, diameter 3.0mm, 2 pins, http://www.kingbright.com/attachments/file/psearch/000/00/00/L-47XEC(Ver.9A).pdf")
    (tags "LED Round FlatTop diameter 3.0mm 2 pins")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Light emitting diode")
    (property "ki_keywords" "LED diode")
    (path "/87d10d4f-b17d-4582-a4a4-fe549b3608d0")
    (attr through_hole exclude_from_pos_files)
    (fp_text reference "D5" (at -2.275 -0.25 270) (layer "B.SilkS")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp f6387677-6791-4a1f-9a3c-2aaf53d69290)
    )
    (fp_text value "LED Gate In 1" (at 1.27 -2.96 270) (layer "B.Fab")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 4d84e16f-c424-4959-bbf5-5175039208f6)
    )
    (fp_line (start -0.29 -1.08) (end -0.29 -1.236)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 8387cbde-7c1f-4066-9009-4995fb34f07f))
    (fp_line (start -0.29 1.236) (end -0.29 1.08)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp edce9d24-735a-4276-8674-9e08993da005))
    (fp_arc (start -0.29 -1.235516) (mid 1.366487 -1.987659) (end 2.942335 -1.078608)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 0bc91463-7275-4512-bf5a-6d7f809162a3))
    (fp_arc (start 0.229039 -1.08) (mid 1.270117 -1.5) (end 2.31113 -1.079837)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp c2e53bff-f934-47ff-be61-35114e162c02))
    (fp_arc (start 2.31113 1.079837) (mid 1.270117 1.5) (end 0.229039 1.08)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 5f837a0e-8738-422a-a946-039200ef5b39))
    (fp_arc (start 2.942335 1.078608) (mid 1.366487 1.987659) (end -0.29 1.235516)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 6f443814-38d6-457a-a8fa-3d5277a38bfe))
    (fp_line (start -1.15 -2.25) (end 3.7 -2.25)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 7e8cc575-0769-41b0-9c52-576a7187e57e))
    (fp_line (start -1.15 2.25) (end -1.15 -2.25)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 58684166-3f2f-4e4f-81d5-d5d1d2f42f3f))
    (fp_line (start 3.7 -2.25) (end 3.7 2.25)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp e3912d0d-3b89-449c-86a2-9d643ac2bc9d))
    (fp_line (start 3.7 2.25) (end -1.15 2.25)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 586273e7-a834-4533-819b-e35c09ec61df))
    (fp_line (start -0.23 1.16619) (end -0.23 -1.16619)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp acd85a2a-561e-4dd6-9d7f-80cfbe653b76))
    (fp_arc (start -0.230555 -1.165476) (mid 3.17 -0.000452) (end -0.23 1.16619)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 0f35d383-2a9c-4463-9091-a2a61c362f57))
    (fp_circle (center 1.27 0) (end 2.77 0)
      (stroke (width 0.1) (type solid)) (fill none) (layer "B.Fab") (tstamp 7e26ddf0-7162-4cf8-a5e9-96772c1847ab))
    (pad "1" thru_hole rect (at 0 0 90) (size 1.8 1.8) (drill 0.9) (layers "*.Cu" "*.Mask")
      (net 1 "GND") (pinfunction "K") (pintype "passive") (thermal_bridge_angle 45) (thermal_gap 0.5) (tstamp 02824228-620a-408a-8ca2-0dfa2fda2227))
    (pad "2" thru_hole circle (at 2.54 0 90) (size 1.8 1.8) (drill 0.9) (layers "*.Cu" "*.Mask")
      (net 15 "Net-(D5-A)") (pinfunction "A") (pintype "passive") (tstamp ee6c0a33-03d4-4352-ad14-d1c8b787d922))
    (model "${KICAD6_3DMODEL_DIR}/LED_THT.3dshapes/LED_D3.0mm_FlatTop.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "digikey-footprints:Switch_Tactile_SMD_B3U-1000P" (layer "B.Cu")
    (tstamp 75100b72-0901-4c7e-9b15-8cc068c2e933)
    (at 135.365 24.275 180)
    (property "Category" "Switches")
    (property "DK_Datasheet_Link" "https://omronfs.omron.com/en_US/ecb/products/pdf/en-b3u.pdf")
    (property "DK_Detail_Page" "/product-detail/en/omron-electronics-inc-emc-div/B3U-1000P/SW1020CT-ND/1534357")
    (property "Description" "SWITCH TACTILE SPST-NO 0.05A 12V")
    (property "Digi-Key_PN" "SW1020CT-ND")
    (property "Family" "Tactile Switches")
    (property "LCSC" "")
    (property "MPN" "B3U-1000P")
    (property "Manufacturer" "Omron Electronics Inc-EMC Div")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "Status" "Active")
    (property "ki_description" "SWITCH TACTILE SPST-NO 0.05A 12V")
    (property "ki_keywords" "SW1020CT-ND B3U")
    (path "/6b21538d-1c1b-466e-aaec-e62d6533fa83")
    (attr smd exclude_from_pos_files)
    (fp_text reference "S1" (at -3.57 -0.22) (layer "B.SilkS")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp fe5bbbfb-0b7b-487f-820e-8c136406e589)
    )
    (fp_text value "B3U-1000P" (at 0 -2.63) (layer "B.Fab")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 4b714900-7c1b-4634-b1ed-6099eb59f30d)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "B.Fab")
        (effects (font (size 0.5 0.5) (thickness 0.05)) (justify mirror))
      (tstamp 69e4a8b4-7352-48b4-8f2e-6a9293ffc2ac)
    )
    (fp_line (start -1.6 -1.6) (end -1.6 -1.1)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp 91bb3255-bc77-49aa-8b50-eaea1a998144))
    (fp_line (start -1.6 -1.6) (end -1.1 -1.6)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp a8d9a877-1ec0-4e46-aa45-69093915a9ec))
    (fp_line (start -1.6 1.1) (end -2 1.1)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp ff7471fd-8d47-4811-886d-0eb16597f5a7))
    (fp_line (start -1.6 1.6) (end -1.6 1.1)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp 840c6098-b019-456a-b88b-4ac9b4cf374c))
    (fp_line (start -1.6 1.6) (end -1.1 1.6)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp ec58d7aa-dc7f-43ee-ba5d-a5a9ab2a7049))
    (fp_line (start 1.6 -1.6) (end 1.2 -1.6)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp 10f82443-6ba2-4ae6-ac88-79972b59dfb1))
    (fp_line (start 1.6 -1.6) (end 1.6 -1.1)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp c0090948-d7cd-44c6-b75a-f69554b8b037))
    (fp_line (start 1.6 1.6) (end 1.1 1.6)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp 9232436e-ea97-4541-a92b-994df3f72dc0))
    (fp_line (start 1.6 1.6) (end 1.6 1.1)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp 3de66361-d8e6-471f-9028-60bebf3e72be))
    (fp_line (start -2.35 -1.75) (end -2.35 1.75)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 915bb023-8db9-4616-b7a1-c67fc3b4c019))
    (fp_line (start -2.35 -1.75) (end 2.35 -1.75)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 97d74f43-d878-4b04-93fa-0a2e457303f3))
    (fp_line (start -2.35 1.75) (end 2.35 1.75)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 22d124e8-09d4-4fbf-91af-8bf4485d7a75))
    (fp_line (start 2.35 -1.75) (end 2.35 1.75)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 4bad7892-43c7-4d39-af40-efe612d1a09d))
    (fp_line (start -1.5 -1.49) (end 1.5 -1.49)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 57d6432e-2438-4f7f-8bad-330f4e430c40))
    (fp_line (start -1.5 1.49) (end -1.5 -1.49)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp ffcadd0a-6c75-441b-8420-fb814b383435))
    (fp_line (start -1.5 1.49) (end 1.5 1.49)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 66309a9b-f825-4c41-83f1-bf809822d525))
    (fp_line (start 1.5 1.49) (end 1.5 -1.49)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 546db71c-feca-4e64-8a82-24f01f64bcb9))
    (pad "1" smd rect (at -1.8 0.005 180) (size 1 1.69) (layers "B.Cu" "B.Paste" "B.Mask")
      (net 1 "GND") (pintype "passive") (tstamp d0a13baf-7b49-4a45-aba2-f4a176dcb52e))
    (pad "2" smd rect (at 1.8 0.005 180) (size 1 1.69) (layers "B.Cu" "B.Paste" "B.Mask")
      (net 48 "/RUN") (pintype "passive") (tstamp 0c8a8026-88f4-48e0-aa59-ed1ceb7e90fd))
  )

  (footprint "mouser:GPBS850N" (layer "B.Cu")
    (tstamp 849a5a72-d750-4389-9f1f-115cb64d2a81)
    (at 197.925 34.375 180)
    (descr "GPBS-850N-3")
    (tags "Switch")
    (property "Height" "14.2")
    (property "Manufacturer_Name" "CW Industries")
    (property "Manufacturer_Part_Number" "GPBS-850N")
    (property "Mouser Part Number" "629-GPBS-850N")
    (property "Mouser Price/Stock" "https://www.mouser.co.uk/ProductDetail/CW-Industries/GPBS-850N?qs=sajaCoHCXPRZB3tbc1cuTA%3D%3D")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Pushbutton Switches DPDT Non-Latching ON-(ON)")
    (path "/863b19fe-9090-46dc-b375-332ad9ba3324")
    (attr through_hole)
    (fp_text reference "SW1" (at 2.5 2.7) (layer "B.SilkS")
        (effects (font (size 1.27 1.27) (thickness 0.254)) (justify mirror))
      (tstamp c6e4858d-d7fe-428f-8bb2-1a20bf8f3031)
    )
    (fp_text value "SW_Push" (at 2.5 2.7) (layer "B.SilkS") hide
        (effects (font (size 1.27 1.27) (thickness 0.254)) (justify mirror))
      (tstamp 79d5bb8d-e5ee-47b0-9f16-b95ce9f42015)
    )
    (fp_text user "${REFERENCE}" (at 2.5 2.7) (layer "B.Fab")
        (effects (font (size 1.27 1.27) (thickness 0.254)) (justify mirror))
      (tstamp 9b2646ed-5a3a-44fc-8337-a972caf2639b)
    )
    (fp_line (start -1.75 -1.55) (end -1.75 6.95)
      (stroke (width 0.2) (type solid)) (layer "B.SilkS") (tstamp 4eef446b-ce83-4269-8675-7d8df2bc0bff))
    (fp_line (start -1.75 6.95) (end 6.75 6.95)
      (stroke (width 0.2) (type solid)) (layer "B.SilkS") (tstamp d41a5fa2-e8e5-4487-ac95-4dc693a01f58))
    (fp_line (start 0 -2.2) (end 0 -2.2)
      (stroke (width 0.2) (type solid)) (layer "B.SilkS") (tstamp eec1341a-155c-4975-84c5-7d26b4c0c24f))
    (fp_line (start 0 -2.1) (end 0 -2.1)
      (stroke (width 0.2) (type solid)) (layer "B.SilkS") (tstamp ba56a2c1-be37-4fcd-9ed6-4e4f392fcd1c))
    (fp_line (start 0 -2.1) (end 0 -2.1)
      (stroke (width 0.2) (type solid)) (layer "B.SilkS") (tstamp f99b2f4a-e6d2-4aee-8d93-cea94783d017))
    (fp_line (start 6.75 -1.55) (end -1.75 -1.55)
      (stroke (width 0.2) (type solid)) (layer "B.SilkS") (tstamp e81b393e-65b7-4e7b-ab53-63be943948a9))
    (fp_line (start 6.75 6.95) (end 6.75 -1.55)
      (stroke (width 0.2) (type solid)) (layer "B.SilkS") (tstamp ac71a051-db6d-4987-95a9-54e9262698a9))
    (fp_arc (start 0 -2.2) (mid 0.05 -2.15) (end 0 -2.1)
      (stroke (width 0.2) (type solid)) (layer "B.SilkS") (tstamp 30cffff4-c9e8-4ad9-b4ff-877cee20eab7))
    (fp_arc (start 0 -2.1) (mid -0.05 -2.15) (end 0 -2.2)
      (stroke (width 0.2) (type solid)) (layer "B.SilkS") (tstamp 3ca2f4e8-e654-4aa4-9477-979443922ea9))
    (fp_arc (start 0 -2.1) (mid -0.05 -2.15) (end 0 -2.2)
      (stroke (width 0.2) (type solid)) (layer "B.SilkS") (tstamp 9b3ef331-0df7-4d22-9100-ff2b7bc8d650))
    (fp_line (start -2.75 -2.55) (end -2.75 7.95)
      (stroke (width 0.1) (type solid)) (layer "B.CrtYd") (tstamp a88c7820-81f1-415a-a564-2b7307c8d0b7))
    (fp_line (start -2.75 7.95) (end 7.75 7.95)
      (stroke (width 0.1) (type solid)) (layer "B.CrtYd") (tstamp eedf0c10-df05-42ea-a77e-9facadbf6ec5))
    (fp_line (start 7.75 -2.55) (end -2.75 -2.55)
      (stroke (width 0.1) (type solid)) (layer "B.CrtYd") (tstamp 016edf3e-26c8-4fdb-932d-e1bb2a1b62ae))
    (fp_line (start 7.75 7.95) (end 7.75 -2.55)
      (stroke (width 0.1) (type solid)) (layer "B.CrtYd") (tstamp 724701d9-9efa-4f8f-b067-9d3f5fbcacdb))
    (fp_line (start -1.75 -1.55) (end 6.75 -1.55)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 9009cfa8-f4fe-4df8-869f-705f8df63801))
    (fp_line (start -1.75 6.95) (end -1.75 -1.55)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 2d8ed332-daf9-43a6-a5bc-fc06242d7c1b))
    (fp_line (start 6.75 -1.55) (end 6.75 6.95)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp e4d7c475-42df-40e7-a35d-2e283a318fa4))
    (fp_line (start 6.75 6.95) (end -1.75 6.95)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 104c0df3-0fab-4704-9f39-15d49acb74b6))
    (pad "1" thru_hole rect (at 0 0 180) (size 1.8 1.8) (drill 0.7) (layers "*.Cu" "*.Mask")
      (net 69 "unconnected-(SW1-NC_1-Pad1)") (pinfunction "NC_1") (pintype "passive+no_connect") (tstamp 6c5458f8-cde5-4407-9818-bb2b7bfa4a18))
    (pad "2" thru_hole circle (at 2.5 0 180) (size 1.8 1.8) (drill 0.7) (layers "*.Cu" "*.Mask")
      (net 49 "/GPIO10_SWM") (pinfunction "COM_1") (pintype "passive") (tstamp 589f0334-b905-429c-8c5d-ddfc3722f0e1))
    (pad "3" thru_hole circle (at 5 0 180) (size 1.8 1.8) (drill 0.7) (layers "*.Cu" "*.Mask")
      (net 1 "GND") (pinfunction "NO_1") (pintype "passive") (thermal_gap 0.5) (tstamp 310d5975-7585-4b75-8021-278579f32055))
    (pad "4" thru_hole circle (at 0 5.4 180) (size 1.8 1.8) (drill 0.7) (layers "*.Cu" "*.Mask")
      (net 68 "unconnected-(SW1-NC_2-Pad4)") (pinfunction "NC_2") (pintype "passive+no_connect") (tstamp 247ecd45-396f-4a88-9004-8025ac75c43e))
    (pad "5" thru_hole circle (at 2.5 5.4 180) (size 1.8 1.8) (drill 0.7) (layers "*.Cu" "*.Mask")
      (net 49 "/GPIO10_SWM") (pinfunction "COM_2") (pintype "passive") (tstamp 0da87597-8a55-474c-bec1-c0cce1e89c8c))
    (pad "6" thru_hole circle (at 5 5.4 180) (size 1.8 1.8) (drill 0.7) (layers "*.Cu" "*.Mask")
      (net 1 "GND") (pinfunction "NO_2") (pintype "passive") (thermal_gap 0.5) (tstamp ab55b656-2c15-4adc-ba1d-c01ae252128a))
    (model "GPBS-850N.stp"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "emutelab:Thonk DWB2" (layer "B.Cu")
    (tstamp 90748fef-1cee-4798-b7c1-6477f3478e20)
    (at 165.375 22.55 -90)
    (descr "http://spec_sheets.e-switch.com/specs/T111597.pdf")
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Switch, single pole double throw")
    (property "ki_keywords" "switch single-pole double-throw spdt ON-ON")
    (path "/4de20828-5bf8-4995-986d-c55dc4095ce1")
    (attr through_hole exclude_from_pos_files)
    (fp_text reference "SW3" (at 4.84 4.55 -270) (layer "B.SilkS")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 10d04668-181d-4db4-b0c6-273aaa43efaf)
    )
    (fp_text value "SW_SPDT" (at 4.65 -4.82 -270) (layer "B.Fab")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp d58bd83b-0b0b-44c3-93bb-9d7e2b069f41)
    )
    (fp_text user "${REFERENCE}" (at 4.7 0 -270) (layer "B.Fab")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp dae681bd-beeb-43b0-b125-74be4956efc8)
    )
    (fp_line (start -1.75 -3.525) (end -1.75 -3.1)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp beece0b5-ede6-4b5e-ac07-0089104fab12))
    (fp_line (start -1.75 -3.525) (end -1.3 -3.525)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp 07a55cfa-e87c-431f-89fb-b975350326fd))
    (fp_line (start -1.75 3.525) (end -1.75 3.15)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp 386f7de8-308c-445b-93b5-bf7be3646468))
    (fp_line (start -1.75 3.525) (end -1.35 3.525)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp f3115765-56c5-4e5d-ae0a-575551b63dbc))
    (fp_line (start 10.775 3.525) (end 11.15 3.525)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp 887ad57e-adbd-4d55-958e-b13189e2031f))
    (fp_line (start 11.125 -3.525) (end 10.675 -3.525)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp 2d49fed7-6fc7-484f-ac53-5313a3b84b48))
    (fp_line (start 11.15 -3.525) (end 11.15 -3.1)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp cc9db047-1806-43d9-9f16-6f780c120156))
    (fp_line (start 11.15 3.525) (end 11.15 3.175)
      (stroke (width 0.1) (type solid)) (layer "B.SilkS") (tstamp d5ace4fd-4215-4373-ab31-cdc9723aa5b8))
    (fp_line (start -1.9 -3.68) (end -1.9 3.68)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 5ff6f03d-9cdc-4545-9f4f-573dfc71d958))
    (fp_line (start -1.9 3.68) (end 11.3 3.68)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 36c4c251-ae2a-492c-afd6-f70aaec15bad))
    (fp_line (start 11.3 -3.68) (end -1.9 -3.68)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 95efdc7d-be5f-475a-92f3-e9ce9f70f45b))
    (fp_line (start 11.3 3.68) (end 11.3 -3.68)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 6eb76fdb-a695-4f03-8a46-5c2378b8e772))
    (fp_line (start -1.65 -3.43) (end 11.05 -3.43)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 12a81cf7-5095-4091-ad1d-3193e01d4570))
    (fp_line (start -1.65 3.43) (end -1.65 -3.43)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp e089c4f7-77b2-4298-b578-2a76fcfa6c82))
    (fp_line (start -1.65 3.43) (end 11.05 3.43)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp bafac1ba-9e80-412b-af02-7affaeade0e2))
    (fp_line (start 11.05 -3.43) (end 11.05 3.43)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 33d8b038-3e65-4f96-aa37-6c844bd6b242))
    (fp_circle (center 4.7 0) (end 4.7 -3.4)
      (stroke (width 0.1) (type solid)) (fill none) (layer "B.Fab") (tstamp 2c4c0ce9-b84c-4198-9b67-1c222383da89))
    (pad "1" thru_hole rect (at 0 0 270) (size 3 3) (drill 2) (layers "*.Cu" "*.Mask")
      (net 50 "/GPIO14_SWT") (pinfunction "A") (pintype "passive") (tstamp 8a6920fe-108d-480f-8407-f6197b67db1f))
    (pad "2" thru_hole circle (at 4.7 0 270) (size 3 3) (drill 2) (layers "*.Cu" "*.Mask")
      (net 1 "GND") (pinfunction "B") (pintype "passive") (tstamp 7daa72e1-8be1-4b05-991c-f9197d3b6d60))
    (pad "3" thru_hole circle (at 9.4 0 270) (size 3 3) (drill 2) (layers "*.Cu" "*.Mask")
      (net 56 "/GPIO23_SWTB") (pinfunction "C") (pintype "passive") (tstamp 285d5253-2455-4459-be1f-ee133ca48f61))
  )

  (footprint "Eurocad:Alpha9mmPot" (layer "B.Cu")
    (tstamp a88655ff-eeca-4eff-9831-8b2d1af7c8d6)
    (at 115.85 65.0875 180)
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Potentiometer")
    (property "ki_keywords" "resistor variable")
    (path "/28312ff2-3e30-48d4-aad8-1fa2264119ee")
    (attr through_hole exclude_from_pos_files)
    (fp_text reference "RV2" (at 0 -9.5) (layer "B.SilkS")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp ed84647e-56d0-423c-9dd0-374f123caf10)
    )
    (fp_text value "100k" (at 0 6) (layer "B.Fab")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 2616a3a4-df69-44b5-a63e-1183b8a7361a)
    )
    (fp_line (start -4.75 -6.5) (end 4.75 -6.5)
      (stroke (width 0.15) (type solid)) (layer "B.SilkS") (tstamp 91273f03-5791-4a61-a570-88a6ec7d4ac4))
    (fp_line (start -4.75 4.85) (end -4.75 -6.5)
      (stroke (width 0.15) (type solid)) (layer "B.SilkS") (tstamp ea8e7f1e-1af8-4aec-968d-97e50a7896a7))
    (fp_line (start -4.75 4.85) (end 4.75 4.85)
      (stroke (width 0.15) (type solid)) (layer "B.SilkS") (tstamp 0dd9995c-db8d-4021-bb39-90f95306aad1))
    (fp_line (start -1.27 0) (end 1.27 0)
      (stroke (width 0.15) (type solid)) (layer "B.SilkS") (tstamp b13f2693-eec2-4fee-8330-9f8f0d2a7fa7))
    (fp_line (start 0 1.27) (end 0 -1.27)
      (stroke (width 0.15) (type solid)) (layer "B.SilkS") (tstamp dd4caa03-cb99-453e-b83d-c18e29728b8e))
    (fp_line (start 4.75 4.85) (end 4.75 -6.5)
      (stroke (width 0.15) (type solid)) (layer "B.SilkS") (tstamp 8ae3bf75-fa69-49c2-b1ac-de56822aad3d))
    (fp_circle (center 0 0) (end 3.175 0)
      (stroke (width 0.15) (type solid)) (fill none) (layer "B.SilkS") (tstamp 10735441-0868-4405-9c25-c5dcbe245380))
    (pad "" thru_hole circle (at -4.8 0 180) (size 2.6 2.6) (drill 1.8) (layers "*.Cu" "*.Mask" "B.SilkS") (tstamp 5996a483-c00b-4842-8da2-d8fbeaede660))
    (pad "" thru_hole circle (at 4.8 0 180) (size 2.6 2.6) (drill 1.8) (layers "*.Cu" "*.Mask" "B.SilkS") (tstamp c36304e8-1df7-4609-9c94-42dc5941699f))
    (pad "1" thru_hole circle (at -2.5 -7.5 180) (size 1.8 1.8) (drill 1) (layers "*.Cu" "*.Mask" "B.SilkS")
      (net 3 "unconnected-(RV2-Pad1)") (pinfunction "1") (pintype "passive") (tstamp 3eedf769-34ea-4759-baa7-fe358a003ac7))
    (pad "2" thru_hole circle (at 0 -7.5 180) (size 1.8 1.8) (drill 1) (layers "*.Cu" "*.Mask" "B.SilkS")
      (net 4 "unconnected-(RV2-Pad2)") (pinfunction "2") (pintype "passive") (tstamp 457dcc72-18be-4fad-9725-7035184d7d06))
    (pad "3" thru_hole circle (at 2.5 -7.5 180) (size 1.8 1.8) (drill 1) (layers "*.Cu" "*.Mask" "B.SilkS")
      (net 6 "unconnected-(RV2-Pad3)") (pinfunction "3") (pintype "passive") (tstamp 97ea2d0f-6975-47be-a267-67bb8d611918))
  )

  (footprint "emutelab:USB-C-TH_MC-110LD-L137-A" (layer "B.Cu")
    (tstamp d0f52534-b02d-4d41-aff9-841a1f70bf7c)
    (at 164.525 12.975 180)
    (property "Sheetfile" "uSeqIns1.0.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "USB 2.0-only Type-C Receptacle connector")
    (property "ki_keywords" "usb universal serial bus type-C USB2.0")
    (path "/840160b8-be19-4336-9de1-29351f9b4208")
    (attr through_hole)
    (fp_text reference "J1" (at 4.45 -3.875) (layer "B.SilkS")
        (effects (font (size 1.143 1.143) (thickness 0.152)) (justify left mirror))
      (tstamp c1a4102e-8ed5-473e-b580-a02443a658d8)
    )
    (fp_text value "Hanbo_USB_C_Receptacle_USB2.0_MC-110LD-L137" (at -0.003 4.993) (layer "B.Fab") hide
        (effects (font (size 1.143 1.143) (thickness 0.152)) (justify left mirror))
      (tstamp fabbf936-1c91-48ca-a522-eb4115bf8460)
    )
    (fp_text user "gge415" (at 0 0) (layer "Cmts.User")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp b651c417-0c93-4899-bd7e-56fb71e52e95)
    )
    (fp_line (start -6.2 -2.477) (end -6.2 2.496)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp 22a7f2c2-7ef0-43f1-a681-c1ad8415ff62))
    (fp_line (start -6.2 -2.477) (end -4.798 -2.477)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp 9676b720-d9e3-4930-a024-33986003671f))
    (fp_line (start -6.2 2.496) (end -4.78 2.496)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp a237f8db-8b8d-4fd5-aaa1-96ade91c4346))
    (fp_line (start -4.798 -2.477) (end -4.798 -2.261)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp ec207f2c-ebd8-44cb-a20e-a1f077f1fb21))
    (fp_line (start -4.798 -2.261) (end -4.798 -2.215)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp b0b53705-041c-4627-a220-a6eb838593fd))
    (fp_line (start -4.798 -2.261) (end -4.797 -2.26)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp 32efe97a-3d9c-4d50-a9f0-d0bd5a433e20))
    (fp_line (start -4.78 0.639) (end -4.78 -0.639)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp 7f79fd9c-bcf3-4a1d-921d-90c1ab5c1eb5))
    (fp_line (start -4.78 2.261) (end -4.78 2.221)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp c486b29a-69a4-4bb4-ab4c-36e5b67204ff))
    (fp_line (start -4.78 2.261) (end -4.779 2.261)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp 1bf60ee4-3be3-45dc-afeb-2318150695c0))
    (fp_line (start -4.78 2.496) (end -4.78 2.261)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp d78a8766-9dee-4b7e-accd-869065edd548))
    (fp_line (start -2.995 -1.559) (end 2.995 -1.559)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp c9e88720-edb3-4f04-b4b8-5202a7b35dff))
    (fp_line (start -2.994 1.576) (end 2.994 1.576)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp 85a6ab67-7258-46ec-a986-e47158e5a5fd))
    (fp_line (start 4.758 -2.444) (end 4.758 -2.261)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp 5cf4ec89-e895-4a25-aadb-2d33df1d9452))
    (fp_line (start 4.758 -2.228) (end 4.758 -2.261)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp 2cf57d77-2984-4b15-8221-84f9284d1a26))
    (fp_line (start 4.758 0.632) (end 4.758 -0.632)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp 4667e487-a588-49f5-b09e-442674e91e6f))
    (fp_line (start 4.794 -2.48) (end 4.758 -2.444)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp 9be0a7a3-56fe-4c9e-a74b-30a993a28046))
    (fp_line (start 4.826 2.473) (end 4.826 2.205)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp ca7d7820-5284-45d2-9958-de0b90b8bed9))
    (fp_line (start 6.226 -2.48) (end 4.794 -2.48)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp 04fc58d8-5922-4264-94c6-3660159ab384))
    (fp_line (start 6.227 2.473) (end 4.826 2.473)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp 58f49edc-5374-4946-b4ee-06d96d54d99c))
    (fp_line (start 6.227 2.473) (end 6.245 2.455)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp a29a39a3-dddc-4dd0-a0ee-e9ad8486bc74))
    (fp_line (start 6.245 -2.461) (end 6.226 -2.48)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp 1212f399-2750-47b1-9892-f92703927b2d))
    (fp_line (start 6.245 2.455) (end 6.245 -2.461)
      (stroke (width 0.254) (type solid)) (layer "B.SilkS") (tstamp 3120f765-0f36-44c5-b068-184f35fa98b7))
    (fp_poly
      (pts
        (xy 4.831 2.232)
        (xy 4.682 2.245)
        (xy 4.682 2.443)
        (xy 4.819 2.547)
        (xy 6.246 2.534)
        (xy 6.326 2.391)
        (xy 6.338 -2.472)
        (xy 6.206 -2.561)
        (xy 4.772 -2.558)
        (xy 4.669 -2.47)
        (xy 4.669 -2.229)
        (xy 4.831 -2.238)
        (xy 4.844 -2.397)
        (xy 6.176 -2.399)
        (xy 6.161 2.371)
        (xy 4.845 2.371)
      )

      (stroke (width 0) (type solid)) (fill solid) (layer "Cmts.User") (tstamp cfc6610f-67f1-436c-96c2-684a8707c8e3))
    (fp_poly
      (pts
        (xy -4.856 -2.246)
        (xy -4.846 -2.256)
        (xy -4.704 -2.256)
        (xy -4.727 -2.279)
        (xy -4.727 -2.5)
        (xy -4.798 -2.571)
        (xy -4.819 -2.571)
        (xy -6.197 -2.578)
        (xy -6.282 -2.453)
        (xy -6.297 2.448)
        (xy -6.198 2.575)
        (xy -4.797 2.58)
        (xy -4.712 2.491)
        (xy -4.688 2.26)
        (xy -4.848 2.26)
        (xy -4.846 2.428)
        (xy -6.107 2.407)
        (xy -6.1 -2.425)
        (xy -4.867 -2.42)
      )

      (stroke (width 0) (type solid)) (fill solid) (layer "Cmts.User") (tstamp 793ac377-3a51-49ce-9bf2-05df6deed402))
    (pad "13" thru_hole oval (at -4.225 -1.43 90) (size 1.2 1.8) (drill oval 0.6 1.3) (layers "*.Cu" "*.Paste" "*.Mask")
      (net 1 "GND") (pinfunction "SHIELD") (pintype "passive") (tstamp e98b7c9e-e610-4089-bb1e-3ed9a064ba5b))
    (pad "13" thru_hole oval (at -4.225 1.43 90) (size 1.2 1.8) (drill oval 0.6 1.3) (layers "*.Cu" "*.Paste" "*.Mask")
      (net 1 "GND") (pinfunction "SHIELD") (pintype "passive") (tstamp 62aa7620-9c2b-4cc1-b9dc-cfa9d0f5d482))
    (pad "13" thru_hole oval (at 4.225 -1.43 270) (size 1.2 1.8) (drill oval 0.6 1.3) (layers "*.Cu" "*.Paste" "*.Mask")
      (net 1 "GND") (pinfunction "SHIELD") (pintype "passive") (tstamp 5a5d4e26-f889-4675-a707-8dbfe824df26))
    (pad "13" thru_hole oval (at 4.225 1.43 90) (size 1.2 1.8) (drill oval 0.6 1.3) (layers "*.Cu" "*.Paste" "*.Mask")
      (net 1 "GND") (pinfunction "SHIELD") (pintype "passive") (tstamp 4408ec86-0a63-458a-884e-eed35dd9b6a2))
    (pad "A1B12" thru_hole oval (at 3.5 0 180) (size 0.8 1) (drill oval 0.5 0.7) (layers "*.Cu" "*.Paste" "*.Mask")
      (net 1 "GND") (pinfunction "GND") (pintype "passive") (tstamp 6dcee035-8f74-4f7a-85e9-6a0ab2719b2d))
    (pad "A4B9" thru_hole oval (at 2.5 0 180) (size 0.8 1) (drill oval 0.5 0.7) (layers "*.Cu" "*.Paste" "*.Mask")
      (net 65 "unconnected-(J1-VBUS-PadA4B9)") (pinfunction "VBUS") (pintype "passive+no_connect") (tstamp 5bdb4407-8c0c-4c33-a170-29acec3134aa))
    (pad "A5" thru_hole circle (at 1.5 -0.825 90) (size 0.8 0.8) (drill 0.5) (layers "*.Cu" "*.Paste" "*.Mask")
      (net 18 "Net-(J1-CC1)") (pinfunction "CC1") (pintype "bidirectional") (tstamp b4ae7d93-2f1f-496f-8a19-7d5112fac838))
    (pad "A6" thru_hole circle (at 0.5 -0.825 90) (size 0.8 0.8) (drill 0.5) (layers "*.Cu" "*.Paste" "*.Mask")
      (net 19 "/USB_D+") (pinfunction "D+") (pintype "bidirectional") (tstamp b61c1445-b70c-4edc-a424-8bd9bf1beede))
    (pad "A7" thru_hole circle (at -0.5 -0.825 90) (size 0.8 0.8) (drill 0.5) (layers "*.Cu" "*.Paste" "*.Mask")
      (net 20 "/USB_D-") (pinfunction "D-") (pintype "bidirectional") (tstamp b44f0d51-3df5-40c4-9a73-6ec82d0e6283))
    (pad "A8" thru_hole circle (at -1.5 -0.825 90) (size 0.8 0.8) (drill 0.5) (layers "*.Cu" "*.Paste" "*.Mask")
      (net 21 "unconnected-(J1-SBU1-PadA8)") (pinfunction "SBU1") (pintype "bidirectional+no_connect") (tstamp fd2e24a7-f43b-4394-9b6f-3f4c76ad52b1))
    (pad "A9B4" thru_hole oval (at -2.5 0) (size 0.8 1) (drill oval 0.5 0.7) (layers "*.Cu" "*.Paste" "*.Mask")
      (net 66 "unconnected-(J1-VBUS-PadA9B4)") (pinfunction "VBUS") (pintype "passive+no_connect") (tstamp a9963687-d95c-4fad-bfef-0605aa660296))
    (pad "A12B1" thru_hole oval (at -3.5 0) (size 0.8 1) (drill oval 0.5 0.7) (layers "*.Cu" "*.Paste" "*.Mask")
      (net 1 "GND") (pinfunction "GND") (pintype "passive") (tstamp 30e906c0-5e42-4810-8a05-50f702e32706))
    (pad "B5" thru_hole circle (at -1.5 0.825 90) (size 0.8 0.8) (drill 0.5) (layers "*.Cu" "*.Paste" "*.Mask")
      (net 22 "Net-(J1-CC2)") (pinfunction "CC2") (pintype "bidirectional") (tstamp 971cbf40-b06f-4700-9ec6-91b9bdecd346))
    (pad "B6" thru_hole circle (at -0.5 0.825 90) (size 0.8 0.8) (drill 0.5) (layers "*.Cu" "*.Paste" "*.Mask")
      (net 19 "/USB_D+") (pinfunction "D+") (pintype "bidirectional") (tstamp 6e8b3dfe-4912-4de7-ad34-dfa54769a47c))
    (pad "B7" thru_hole circle (at 0.5 0.825 90) (size 0.8 0.8) (drill 0.5) (layers "*.Cu" "*.Paste" "*.Mask")
      (net 20 "/USB_D-") (pinfunction "D-") (pintype "bidirectional") (tstamp 0c326d21-7e2b-4849-aec5-0c267dac28d6))
    (pad "B8" thru_hole circle (at 1.5 0.825 90) (size 0.8 0.8) (drill 0.5) (layers "*.Cu" "*.Paste" "*.Mask")
      (net 23 "unconnected-(J1-SBU2-PadB8)") (pinfunction "SBU2") (pintype "bidirectional+no_connect") (tstamp d091a20b-5157-4fd9-92fb-395fde01b70b))
  )

  (gr_poly
    (pts
      (xy 173.103488 24.062439)
      (xy 173.079879 24.064192)
      (xy 173.056316 24.06708)
      (xy 173.03286 24.071076)
      (xy 173.009574 24.07615)
      (xy 172.986517 24.082275)
      (xy 172.963754 24.089423)
      (xy 172.941343 24.097567)
      (xy 172.919349 24.106678)
      (xy 172.897831 24.116729)
      (xy 172.876853 24.127691)
      (xy 172.856474 24.139536)
      (xy 172.836758 24.152238)
      (xy 172.817765 24.165767)
      (xy 172.799557 24.180097)
      (xy 172.782196 24.195198)
      (xy 172.765744 24.211044)
      (xy 172.750262 24.227605)
      (xy 172.735811 24.244856)
      (xy 172.722454 24.262766)
      (xy 172.710252 24.281309)
      (xy 172.699267 24.300457)
      (xy 172.689559 24.320181)
      (xy 172.681192 24.340454)
      (xy 172.674226 24.361248)
      (xy 172.668723 24.382535)
      (xy 172.664744 24.404288)
      (xy 172.662353 24.426477)
      (xy 172.661609 24.449076)
      (xy 172.662574 24.472056)
      (xy 172.665311 24.495389)
      (xy 172.669881 24.519048)
      (xy 172.89848 25.497478)
      (xy 172.901881 25.520803)
      (xy 172.904062 25.54383)
      (xy 172.905048 25.566529)
      (xy 172.904863 25.588871)
      (xy 172.903531 25.610828)
      (xy 172.901075 25.63237)
      (xy 172.897521 25.653469)
      (xy 172.892891 25.674095)
      (xy 172.88721 25.694219)
      (xy 172.880502 25.713813)
      (xy 172.872791 25.732848)
      (xy 172.864101 25.751294)
      (xy 172.854456 25.769123)
      (xy 172.84388 25.786305)
      (xy 172.832397 25.802812)
      (xy 172.820032 25.818615)
      (xy 172.806807 25.833685)
      (xy 172.792748 25.847992)
      (xy 172.777878 25.861508)
      (xy 172.762221 25.874204)
      (xy 172.745801 25.88605)
      (xy 172.728642 25.897019)
      (xy 172.710769 25.90708)
      (xy 172.692205 25.916205)
      (xy 172.672975 25.924365)
      (xy 172.653102 25.931532)
      (xy 172.63261 25.937675)
      (xy 172.611524 25.942766)
      (xy 172.589868 25.946776)
      (xy 172.567664 25.949676)
      (xy 172.544939 25.951438)
      (xy 172.521715 25.952031)
      (xy 167.938602 25.952031)
      (xy 167.782437 25.955994)
      (xy 167.628297 25.967755)
      (xy 167.476375 25.987121)
      (xy 167.326862 26.013898)
      (xy 167.17995 26.047896)
      (xy 167.035833 26.088921)
      (xy 166.894702 26.13678)
      (xy 166.75675 26.191281)
      (xy 166.622169 26.252231)
      (xy 166.491152 26.319438)
      (xy 166.36389 26.392709)
      (xy 166.240576 26.471851)
      (xy 166.121403 26.556672)
      (xy 166.006563 26.646979)
      (xy 165.896248 26.742579)
      (xy 165.79065 26.84328)
      (xy 165.689962 26.94889)
      (xy 165.594376 27.059215)
      (xy 165.504084 27.174063)
      (xy 165.41928 27.293241)
      (xy 165.340154 27.416557)
      (xy 165.2669 27.543819)
      (xy 165.199709 27.674832)
      (xy 165.138775 27.809406)
      (xy 165.084289 27.947347)
      (xy 165.036444 28.088462)
      (xy 164.995432 28.23256)
      (xy 164.961445 28.379447)
      (xy 164.934677 28.52893)
      (xy 164.915318 28.680818)
      (xy 164.903562 28.834917)
      (xy 164.8996 28.991035)
      (xy 164.903563 29.147152)
      (xy 164.915324 29.301247)
      (xy 164.934689 29.453127)
      (xy 164.961467 29.602601)
      (xy 164.995465 29.749476)
      (xy 165.03649 29.893559)
      (xy 165.084349 30.034659)
      (xy 165.13885 30.172582)
      (xy 165.1998 30.307136)
      (xy 165.267007 30.438129)
      (xy 165.340277 30.565369)
      (xy 165.41942 30.688662)
      (xy 165.50424 30.807817)
      (xy 165.594547 30.922641)
      (xy 165.690148 31.032941)
      (xy 165.790849 31.138526)
      (xy 165.896458 31.239202)
      (xy 166.006783 31.334778)
      (xy 166.121631 31.425061)
      (xy 166.24081 31.509858)
      (xy 166.364126 31.588978)
      (xy 166.491387 31.662227)
      (xy 166.622401 31.729413)
      (xy 166.756974 31.790343)
      (xy 166.894915 31.844827)
      (xy 167.03603 31.89267)
      (xy 167.180127 31.93368)
      (xy 167.327014 31.967666)
      (xy 167.476497 31.994434)
      (xy 167.628385 32.013793)
      (xy 167.782484 32.025549)
      (xy 167.938602 32.02951)
      (xy 173.665244 32.02951)
      (xy 173.688838 32.028921)
      (xy 173.712451 32.027172)
      (xy 173.736021 32.024291)
      (xy 173.759486 32.020305)
      (xy 173.782784 32.015241)
      (xy 173.805853 32.009129)
      (xy 173.828631 32.001994)
      (xy 173.851056 31.993866)
      (xy 173.873066 31.984771)
      (xy 173.8946 31.974736)
      (xy 173.915595 31.963791)
      (xy 173.93599 31.951961)
      (xy 173.955723 31.939276)
      (xy 173.974731 31.925762)
      (xy 173.992953 31.911447)
      (xy 174.010327 31.896358)
      (xy 174.026791 31.880524)
      (xy 174.042283 31.863972)
      (xy 174.056741 31.84673)
      (xy 174.070103 31.828824)
      (xy 174.082308 31.810284)
      (xy 174.093293 31.791135)
      (xy 174.102996 31.771407)
      (xy 174.111356 31.751127)
      (xy 174.118311 31.730321)
      (xy 174.123799 31.709019)
      (xy 174.127757 31.687247)
      (xy 174.130124 31.665033)
      (xy 174.130839 31.642404)
      (xy 174.129838 31.619389)
      (xy 174.12706 31.596015)
      (xy 174.122444 31.57231)
      (xy 173.894374 30.595996)
      (xy 173.890877 30.572571)
      (xy 173.888606 30.549445)
      (xy 173.887536 30.526648)
      (xy 173.887644 30.504207)
      (xy 173.888905 30.482153)
      (xy 173.891295 30.460515)
      (xy 173.894791 30.439321)
      (xy 173.899368 30.418601)
      (xy 173.905002 30.398384)
      (xy 173.91167 30.3787)
      (xy 173.919347 30.359577)
      (xy 173.928009 30.341044)
      (xy 173.937632 30.323131)
      (xy 173.948193 30.305867)
      (xy 173.959666 30.289281)
      (xy 173.972029 30.273403)
      (xy 173.985257 30.25826)
      (xy 173.999326 30.243884)
      (xy 174.014211 30.230302)
      (xy 174.02989 30.217543)
      (xy 174.046338 30.205638)
      (xy 174.063531 30.194615)
      (xy 174.081444 30.184504)
      (xy 174.100054 30.175333)
      (xy 174.119338 30.167132)
      (xy 174.13927 30.159929)
      (xy 174.159826 30.153755)
      (xy 174.180984 30.148638)
      (xy 174.202718 30.144607)
      (xy 174.225005 30.141692)
      (xy 174.247821 30.139921)
      (xy 174.271141 30.139325)
      (xy 179.178104 30.139325)
      (xy 179.178104 31.624167)
      (xy 174.741571 31.624167)
      (xy 174.717978 31.624757)
      (xy 174.694369 31.626511)
      (xy 174.670806 31.629399)
      (xy 174.64735 31.633394)
      (xy 174.624063 31.638468)
      (xy 174.601007 31.644593)
      (xy 174.578243 31.651742)
      (xy 174.555833 31.659886)
      (xy 174.533839 31.668997)
      (xy 174.512321 31.679047)
      (xy 174.491342 31.690009)
      (xy 174.470964 31.701855)
      (xy 174.451247 31.714557)
      (xy 174.432255 31.728086)
      (xy 174.414047 31.742416)
      (xy 174.396686 31.757517)
      (xy 174.380234 31.773363)
      (xy 174.364752 31.789925)
      (xy 174.350301 31.807175)
      (xy 174.336944 31.825085)
      (xy 174.324742 31.843628)
      (xy 174.313756 31.862776)
      (xy 174.304049 31.882501)
      (xy 174.295681 31.902774)
      (xy 174.288715 31.923568)
      (xy 174.283212 31.944855)
      (xy 174.279234 31.966607)
      (xy 174.276842 31.988796)
      (xy 174.276098 32.011395)
      (xy 174.277064 32.034375)
      (xy 174.279801 32.057708)
      (xy 174.28437 32.081367)
      (xy 174.51297 33.059796)
      (xy 174.516372 33.083121)
      (xy 174.518558 33.106148)
      (xy 174.519551 33.128847)
      (xy 174.519375 33.15119)
      (xy 174.518053 33.173147)
      (xy 174.51561 33.194689)
      (xy 174.51207 33.215788)
      (xy 174.507455 33.236414)
      (xy 174.50179 33.256538)
      (xy 174.495098 33.276132)
      (xy 174.487404 33.295167)
      (xy 174.47873 33.313613)
      (xy 174.469101 33.331442)
      (xy 174.45854 33.348624)
      (xy 174.447072 33.365132)
      (xy 174.434719 33.380934)
      (xy 174.421506 33.396004)
      (xy 174.407456 33.410311)
      (xy 174.392594 33.423827)
      (xy 174.376942 33.436523)
      (xy 174.360525 33.448369)
      (xy 174.343366 33.459338)
      (xy 174.325489 33.469399)
      (xy 174.306917 33.478524)
      (xy 174.287676 33.486685)
      (xy 174.267787 33.493851)
      (xy 174.247275 33.499994)
      (xy 174.226165 33.505085)
      (xy 174.204478 33.509095)
      (xy 174.18224 33.511996)
      (xy 174.159474 33.513757)
      (xy 174.136203 33.514351)
      (xy 166.243155 33.514351)
      (xy 166.21968 33.514947)
      (xy 166.196505 33.516718)
      (xy 166.173658 33.519633)
      (xy 166.151169 33.523665)
      (xy 166.129067 33.528783)
      (xy 166.107382 33.534959)
      (xy 166.086142 33.542163)
      (xy 166.065378 33.550367)
      (xy 166.045119 33.559541)
      (xy 166.025393 33.569657)
      (xy 166.006231 33.580685)
      (xy 165.987662 33.592597)
      (xy 165.969714 33.605363)
      (xy 165.952418 33.618953)
      (xy 165.935803 33.63334)
      (xy 165.919898 33.648494)
      (xy 165.904733 33.664386)
      (xy 165.890336 33.680987)
      (xy 165.876737 33.698267)
      (xy 165.863966 33.716198)
      (xy 165.852052 33.734751)
      (xy 165.841024 33.753897)
      (xy 165.830912 33.773606)
      (xy 165.821745 33.793849)
      (xy 165.813552 33.814598)
      (xy 165.806363 33.835823)
      (xy 165.800207 33.857496)
      (xy 165.795113 33.879587)
      (xy 165.791111 33.902066)
      (xy 165.788231 33.924906)
      (xy 165.7865 33.948077)
      (xy 165.78595 33.97155)
      (xy 165.78595 35.353735)
      (xy 165.786548 35.377161)
      (xy 165.788323 35.400292)
      (xy 165.791245 35.423097)
      (xy 165.795286 35.445547)
      (xy 165.800415 35.467613)
      (xy 165.806603 35.489266)
      (xy 165.813822 35.510475)
      (xy 165.822041 35.531213)
      (xy 165.831231 35.551449)
      (xy 165.841363 35.571155)
      (xy 165.852408 35.5903)
      (xy 165.864336 35.608855)
      (xy 165.877118 35.626791)
      (xy 165.890724 35.64408)
      (xy 165.905126 35.66069)
      (xy 165.920293 35.676594)
      (xy 165.936196 35.691761)
      (xy 165.952807 35.706162)
      (xy 165.970095 35.719768)
      (xy 165.988031 35.73255)
      (xy 166.006587 35.744478)
      (xy 166.025732 35.755522)
      (xy 166.045437 35.765655)
      (xy 166.065674 35.774845)
      (xy 166.086411 35.783064)
      (xy 166.107621 35.790282)
      (xy 166.129274 35.796471)
      (xy 166.15134 35.8016)
      (xy 166.17379 35.80564)
      (xy 166.196595 35.808563)
      (xy 166.219726 35.810338)
      (xy 166.243152 35.810936)
      (xy 174.47328 35.810936)
      (xy 174.473479 35.81093)
      (xy 174.473679 35.810913)
      (xy 174.47388 35.810887)
      (xy 174.474083 35.810853)
      (xy 174.4745 35.810768)
      (xy 174.474935 35.810671)
      (xy 174.475395 35.810574)
      (xy 174.475636 35.810529)
      (xy 174.475886 35.810489)
      (xy 174.476145 35.810455)
      (xy 174.476414 35.810429)
      (xy 174.476694 35.810412)
      (xy 174.476985 35.810406)
      (xy 174.500426 35.809629)
      (xy 174.523878 35.807714)
      (xy 174.547279 35.804686)
      (xy 174.570569 35.800573)
      (xy 174.593687 35.795403)
      (xy 174.616572 35.789201)
      (xy 174.639163 35.781995)
      (xy 174.6614 35.773811)
      (xy 174.683221 35.764678)
      (xy 174.704566 35.75462)
      (xy 174.725373 35.743667)
      (xy 174.745583 35.731844)
      (xy 174.765134 35.719178)
      (xy 174.783965 35.705697)
      (xy 174.802015 35.691427)
      (xy 174.819224 35.676395)
      (xy 174.835531 35.660628)
      (xy 174.850875 35.644154)
      (xy 174.865194 35.626998)
      (xy 174.878429 35.609189)
      (xy 174.890518 35.590752)
      (xy 174.901401 35.571715)
      (xy 174.911016 35.552105)
      (xy 174.919303 35.531949)
      (xy 174.926201 35.511273)
      (xy 174.931649 35.490105)
      (xy 174.935587 35.468472)
      (xy 174.937953 35.4464)
      (xy 174.938686 35.423916)
      (xy 174.937726 35.401048)
      (xy 174.935012 35.377822)
      (xy 174.930482 35.354265)
      (xy 174.930482 35.353735)
      (xy 174.701881 34.376365)
      (xy 174.698433 34.35299)
      (xy 174.696206 34.329914)
      (xy 174.695178 34.307165)
      (xy 174.695325 34.284774)
      (xy 174.696621 34.262769)
      (xy 174.699044 34.241178)
      (xy 174.70257 34.220032)
      (xy 174.707173 34.199359)
      (xy 174.712831 34.179188)
      (xy 174.719519 34.159549)
      (xy 174.727213 34.14047)
      (xy 174.735889 34.121981)
      (xy 174.745523 34.10411)
      (xy 174.756091 34.086887)
      (xy 174.767569 34.07034)
      (xy 174.779934 34.0545)
      (xy 174.79316 34.039394)
      (xy 174.807224 34.025052)
      (xy 174.822102 34.011503)
      (xy 174.83777 33.998776)
      (xy 174.854204 33.9869)
      (xy 174.871379 33.975905)
      (xy 174.889272 33.965818)
      (xy 174.90786 33.95667)
      (xy 174.927116 33.94849)
      (xy 174.947019 33.941305)
      (xy 174.967543 33.935147)
      (xy 174.988665 33.930043)
      (xy 175.010361 33.926022)
      (xy 175.032606 33.923114)
      (xy 175.055376 33.921348)
      (xy 175.078648 33.920753)
      (xy 181.017485 33.920753)
      (xy 181.040911 33.920155)
      (xy 181.064042 33.91838)
      (xy 181.086847 33.915458)
      (xy 181.109297 33.911417)
      (xy 181.131363 33.906288)
      (xy 181.153016 33.9001)
      (xy 181.174225 33.892881)
      (xy 181.194963 33.884662)
      (xy 181.215199 33.875472)
      (xy 181.234904 33.86534)
      (xy 181.254049 33.854295)
      (xy 181.272605 33.842367)
      (xy 181.290541 33.829585)
      (xy 181.30783 33.815979)
      (xy 181.32444 33.801578)
      (xy 181.340343 33.78641)
      (xy 181.355511 33.770507)
      (xy 181.369912 33.753897)
      (xy 181.383518 33.736608)
      (xy 181.3963 33.718672)
      (xy 181.408228 33.700116)
      (xy 181.419272 33.680971)
      (xy 181.429405 33.661266)
      (xy 181.438595 33.64103)
      (xy 181.446814 33.620292)
      (xy 181.454032 33.599083)
      (xy 181.460221 33.57743)
      (xy 181.46535 33.555364)
      (xy 181.46939 33.532914)
      (xy 181.472313 33.510109)
      (xy 181.474087 33.486979)
      (xy 181.474686 33.463553)
      (xy 181.474686 28.299945)
      (xy 181.474087 28.276519)
      (xy 181.472313 28.253388)
      (xy 181.46939 28.230584)
      (xy 181.46535 28.208134)
      (xy 181.460221 28.186068)
      (xy 181.454032 28.164415)
      (xy 181.446814 28.143205)
      (xy 181.438595 28.122468)
      (xy 181.429405 28.102231)
      (xy 181.419272 28.082526)
      (xy 181.408228 28.063381)
      (xy 181.3963 28.044826)
      (xy 181.383518 28.026889)
      (xy 181.369912 28.009601)
      (xy 181.355511 27.99299)
      (xy 181.340343 27.977087)
      (xy 181.32444 27.96192)
      (xy 181.30783 27.947519)
      (xy 181.290541 27.933912)
      (xy 181.272605 27.921131)
      (xy 181.254049 27.909203)
      (xy 181.234904 27.898158)
      (xy 181.215199 27.888026)
      (xy 181.194963 27.878835)
      (xy 181.174225 27.870616)
      (xy 181.153016 27.863398)
      (xy 181.131363 27.857209)
      (xy 181.109297 27.85208)
      (xy 181.086847 27.84804)
      (xy 181.064042 27.845117)
      (xy 181.040911 27.843342)
      (xy 181.017485 27.842744)
      (xy 173.93512 27.842744)
      (xy 173.911527 27.843335)
      (xy 173.887918 27.845088)
      (xy 173.864355 27.847977)
      (xy 173.840899 27.851972)
      (xy 173.817612 27.857046)
      (xy 173.794556 27.863171)
      (xy 173.771792 27.870319)
      (xy 173.749382 27.878463)
      (xy 173.727387 27.887574)
      (xy 173.70587 27.897625)
      (xy 173.684891 27.908587)
      (xy 173.664512 27.920433)
      (xy 173.644796 27.933134)
      (xy 173.625803 27.946664)
      (xy 173.607596 27.960993)
      (xy 173.590235 27.976095)
      (xy 173.573782 27.99194)
      (xy 173.5583 28.008502)
      (xy 173.54385 28.025752)
      (xy 173.530493 28.043663)
      (xy 173.51829 28.062206)
      (xy 173.507305 28.081354)
      (xy 173.497598 28.101078)
      (xy 173.48923 28.121351)
      (xy 173.482264 28.142145)
      (xy 173.476761 28.163432)
      (xy 173.472783 28.185185)
      (xy 173.470391 28.207374)
      (xy 173.469647 28.229973)
      (xy 173.470613 28.252952)
      (xy 173.473349 28.276286)
      (xy 173.477919 28.299945)
      (xy 173.706519 29.278373)
      (xy 173.709919 29.301699)
      (xy 173.712101 29.324726)
      (xy 173.713087 29.347425)
      (xy 173.712902 29.369767)
      (xy 173.71157 29.391724)
      (xy 173.709114 29.413266)
      (xy 173.70556 29.434365)
      (xy 173.70093 29.454991)
      (xy 173.695249 29.475116)
      (xy 173.688541 29.49471)
      (xy 173.68083 29.513744)
      (xy 173.67214 29.53219)
      (xy 173.662495 29.550019)
      (xy 173.651919 29.567201)
      (xy 173.640436 29.583708)
      (xy 173.628071 29.599511)
      (xy 173.614846 29.614581)
      (xy 173.600787 29.628888)
      (xy 173.585916 29.642404)
      (xy 173.570259 29.655099)
      (xy 173.55384 29.666946)
      (xy 173.536681 29.677914)
      (xy 173.518808 29.687975)
      (xy 173.500244 29.697101)
      (xy 173.481014 29.705261)
      (xy 173.46114 29.712427)
      (xy 173.440649 29.71857)
      (xy 173.419563 29.723661)
      (xy 173.397906 29.727671)
      (xy 173.375703 29.730572)
      (xy 173.352977 29.732333)
      (xy 173.329753 29.732927)
      (xy 167.939661 29.732927)
      (xy 167.901495 29.731958)
      (xy 167.863827 29.729083)
      (xy 167.826702 29.724349)
      (xy 167.790167 29.717803)
      (xy 167.754269 29.709493)
      (xy 167.719055 29.699465)
      (xy 167.684572 29.687767)
      (xy 167.650868 29.674446)
      (xy 167.617987 29.659549)
      (xy 167.585979 29.643123)
      (xy 167.554889 29.625216)
      (xy 167.524765 29.605874)
      (xy 167.495653 29.585146)
      (xy 167.4676 29.563077)
      (xy 167.440653 29.539716)
      (xy 167.41486 29.515109)
      (xy 167.390266 29.489304)
      (xy 167.366919 29.462347)
      (xy 167.344866 29.434287)
      (xy 167.324154 29.40517)
      (xy 167.304829 29.375043)
      (xy 167.286938 29.343954)
      (xy 167.270529 29.311949)
      (xy 167.255648 29.279076)
      (xy 167.242342 29.245383)
      (xy 167.230658 29.210916)
      (xy 167.220642 29.175722)
      (xy 167.212343 29.139849)
      (xy 167.205806 29.103344)
      (xy 167.201079 29.066254)
      (xy 167.198209 29.028626)
      (xy 167.197241 28.990507)
      (xy 167.19821 28.952342)
      (xy 167.201085 28.914674)
      (xy 167.205819 28.877549)
      (xy 167.212365 28.841014)
      (xy 167.220675 28.805116)
      (xy 167.230703 28.769902)
      (xy 167.242401 28.735419)
      (xy 167.255723 28.701715)
      (xy 167.27062 28.668835)
      (xy 167.287046 28.636826)
      (xy 167.304953 28.605736)
      (xy 167.324294 28.575612)
      (xy 167.345023 28.5465)
      (xy 167.367092 28.518447)
      (xy 167.390453 28.4915)
      (xy 167.41506 28.465706)
      (xy 167.440865 28.441112)
      (xy 167.467822 28.417765)
      (xy 167.495882 28.395712)
      (xy 167.525 28.375)
      (xy 167.555126 28.355674)
      (xy 167.586216 28.337784)
      (xy 167.61822 28.321374)
      (xy 167.651093 28.306493)
      (xy 167.684786 28.293187)
      (xy 167.719253 28.281503)
      (xy 167.754447 28.271488)
      (xy 167.79032 28.263188)
      (xy 167.826825 28.256652)
      (xy 167.863915 28.251924)
      (xy 167.901542 28.249054)
      (xy 167.939661 28.248086)
      (xy 172.859323 28.248086)
      (xy 172.882917 28.247496)
      (xy 172.906531 28.245742)
      (xy 172.9301 28.242854)
      (xy 172.953565 28.238859)
      (xy 172.976863 28.233785)
      (xy 172.999932 28.22766)
      (xy 173.02271 28.220511)
      (xy 173.045135 28.212367)
      (xy 173.067145 28.203256)
      (xy 173.088679 28.193206)
      (xy 173.109674 28.182244)
      (xy 173.130069 28.170398)
      (xy 173.149802 28.157696)
      (xy 173.16881 28.144167)
      (xy 173.187032 28.129837)
      (xy 173.204406 28.114736)
      (xy 173.22087 28.09889)
      (xy 173.236362 28.082328)
      (xy 173.25082 28.065078)
      (xy 173.264182 28.047168)
      (xy 173.276387 28.028625)
      (xy 173.287372 28.009477)
      (xy 173.297076 27.989752)
      (xy 173.305436 27.969479)
      (xy 173.312391 27.948685)
      (xy 173.317878 27.927398)
      (xy 173.321837 27.905646)
      (xy 173.324204 27.883457)
      (xy 173.324918 27.860858)
      (xy 173.323917 27.837878)
      (xy 173.32114 27.814545)
      (xy 173.316524 27.790886)
      (xy 173.088452 26.814573)
      (xy 173.084955 26.791148)
      (xy 173.082684 26.768022)
      (xy 173.081614 26.745224)
      (xy 173.081722 26.722784)
      (xy 173.082983 26.70073)
      (xy 173.085373 26.679091)
      (xy 173.088869 26.657898)
      (xy 173.093446 26.637178)
      (xy 173.099081 26.616961)
      (xy 173.105748 26.597276)
      (xy 173.113425 26.578153)
      (xy 173.122088 26.559621)
      (xy 173.131711 26.541708)
      (xy 173.142271 26.524444)
      (xy 173.153745 26.507858)
      (xy 173.166108 26.491979)
      (xy 173.179336 26.476837)
      (xy 173.193404 26.46246)
      (xy 173.20829 26.448878)
      (xy 173.223969 26.43612)
      (xy 173.240417 26.424215)
      (xy 173.257609 26.413192)
      (xy 173.275523 26.403081)
      (xy 173.294133 26.39391)
      (xy 173.313416 26.385708)
      (xy 173.333348 26.378506)
      (xy 173.353905 26.372332)
      (xy 173.375063 26.367215)
      (xy 173.396797 26.363184)
      (xy 173.419084 26.360269)
      (xy 173.441899 26.358498)
      (xy 173.465219 26.357902)
      (xy 181.017485 26.357902)
      (xy 181.040911 26.357303)
      (xy 181.064042 26.355528)
      (xy 181.086847 26.352606)
      (xy 181.109297 26.348566)
      (xy 181.131363 26.343437)
      (xy 181.153016 26.337248)
      (xy 181.174225 26.33003)
      (xy 181.194963 26.321811)
      (xy 181.215199 26.31262)
      (xy 181.234904 26.302488)
      (xy 181.254049 26.291444)
      (xy 181.272605 26.279516)
      (xy 181.290541 26.266734)
      (xy 181.30783 26.253128)
      (xy 181.32444 26.238726)
      (xy 181.340343 26.223559)
      (xy 181.355511 26.207656)
      (xy 181.369912 26.191046)
      (xy 181.383518 26.173757)
      (xy 181.3963 26.155821)
      (xy 181.408228 26.137266)
      (xy 181.419272 26.118121)
      (xy 181.429405 26.098415)
      (xy 181.438595 26.078179)
      (xy 181.446814 26.057442)
      (xy 181.454032 26.036232)
      (xy 181.460221 26.014579)
      (xy 181.46535 25.992513)
      (xy 181.46939 25.970063)
      (xy 181.472313 25.947258)
      (xy 181.474087 25.924128)
      (xy 181.474686 25.900702)
      (xy 181.474686 24.519048)
      (xy 181.474087 24.495622)
      (xy 181.472313 24.472492)
      (xy 181.46939 24.449687)
      (xy 181.46535 24.427237)
      (xy 181.460221 24.405171)
      (xy 181.454032 24.383518)
      (xy 181.446814 24.362308)
      (xy 181.438595 24.341571)
      (xy 181.429405 24.321334)
      (xy 181.419272 24.301629)
      (xy 181.408228 24.282484)
      (xy 181.3963 24.263929)
      (xy 181.383518 24.245992)
      (xy 181.369912 24.228704)
      (xy 181.355511 24.212094)
      (xy 181.340343 24.19619)
      (xy 181.32444 24.181023)
      (xy 181.30783 24.166622)
      (xy 181.290541 24.153016)
      (xy 181.272605 24.140234)
      (xy 181.254049 24.128306)
      (xy 181.234904 24.117261)
      (xy 181.215199 24.107129)
      (xy 181.194963 24.097939)
      (xy 181.174225 24.08972)
      (xy 181.153016 24.082502)
      (xy 181.131363 24.076313)
      (xy 181.109297 24.071184)
      (xy 181.086847 24.067144)
      (xy 181.064042 24.064221)
      (xy 181.040911 24.062446)
      (xy 181.017485 24.061848)
      (xy 173.127081 24.061848)
    )

    (stroke (width 0) (type solid)) (fill solid) (layer "B.SilkS") (tstamp 3e30d8cd-e664-46c5-8b79-8a4afa8b9abe))
  (gr_line (start 146.645 38.2) (end 146.65 35.29)
    (stroke (width 0.1) (type dot)) (layer "F.SilkS") (tstamp 55edf86f-b115-42c0-afa8-02abddc95aa7))
  (gr_rect (start 160.8 20.05) (end 186.2 128.05)
    (stroke (width 0.1) (type solid)) (fill none) (layer "Edge.Cuts") (tstamp d4696d13-3be9-4660-8bd6-d67f74943fb9))
  (gr_text "SDA" (at 143.4 32.7) (layer "F.SilkS" knockout) (tstamp 5fd2b4d0-59a2-4416-9514-fb4ce3506213)
    (effects (font (size 0.8 0.8) (thickness 0.15)) (justify left bottom))
  )
  (gr_text "RED\n" (at 185.8 32.88 90) (layer "F.SilkS" knockout) (tstamp 62fa0564-b92c-4384-846d-1eee07859369)
    (effects (font (size 1.5 1.5) (thickness 0.2)) (justify left bottom))
  )
  (gr_text "o" (at 163.325622 54.766425) (layer "F.SilkS" knockout) (tstamp 656b406d-a1b3-44a8-a6b3-52bb16e7cad4)
    (effects (font (size 0.2 0.2) (thickness 0.05)) (justify left bottom))
  )
  (gr_text "o" (at 179.48 41.38) (layer "F.SilkS" knockout) (tstamp 7e786ace-cb89-4670-adff-05b5b3ee72ba)
    (effects (font (size 0.2 0.2) (thickness 0.05)) (justify left bottom))
  )
  (gr_text "o" (at 153 26.45) (layer "F.SilkS" knockout) (tstamp 967ca676-587f-416f-888f-e17f98225850)
    (effects (font (size 0.2 0.2) (thickness 0.05)) (justify left bottom))
  )
  (gr_text "SCL\n" (at 137.225 31.85) (layer "F.SilkS" knockout) (tstamp a2414708-6120-40de-9c18-88ed3c9539c2)
    (effects (font (size 0.8 0.8) (thickness 0.15)) (justify left bottom))
  )
  (gr_text "o" (at 166.025622 41.816425) (layer "F.SilkS" knockout) (tstamp bb77631a-a7e5-4b6a-b35c-51d18bb4aeb7)
    (effects (font (size 0.2 0.2) (thickness 0.05)) (justify left bottom))
  )
  (gr_text "o" (at 177.4 34.2625) (layer "F.SilkS" knockout) (tstamp c40999ec-37dc-4df8-827e-d98ae0d18cfb)
    (effects (font (size 0.2 0.2) (thickness 0.05)) (justify left bottom))
  )

  (segment (start 174.777092 42.266425) (end 174.155622 42.266425) (width 0.15) (layer "F.Cu") (net 1) (tstamp 059a075b-f15c-4117-8265-1fe9e31abb9c))
  (segment (start 169.375622 50.266425) (end 169.563372 50.266425) (width 0.3) (layer "F.Cu") (net 1) (tstamp 11cb7513-27e0-42b7-8638-fe1c44746bae))
  (segment (start 179.396946 35.2625) (end 179.2875 35.2625) (width 0.3) (layer "F.Cu") (net 1) (tstamp 2e279339-5807-4b5b-a626-515ce0dbcf1b))
  (segment (start 171.915622 39.296425) (end 172.485622 39.296425) (width 0.3) (layer "F.Cu") (net 1) (tstamp 2fdad222-cb3f-4995-b028-bab36f594f40))
  (segment (start 172.485622 39.296425) (end 172.755622 39.026425) (width 0.3) (layer "F.Cu") (net 1) (tstamp 30ba2597-5181-47e6-ac6a-2f10dac95ad6))
  (segment (start 179.2875 35.2625) (end 178.4 35.2625) (width 0.3) (layer "F.Cu") (net 1) (tstamp 349e8911-2294-4889-ae7b-450ec903c62e))
  (segment (start 180.248966 34.184312) (end 179.975 34.458278) (width 0.3) (layer "F.Cu") (net 1) (tstamp 34eae709-5638-4c83-ac2a-11c5d7706c14))
  (segment (start 179.975 34.684446) (end 179.396946 35.2625) (width 0.3) (layer "F.Cu") (net 1) (tstamp 3d2d07fc-e0bc-4115-8ef5-e7a0f6f2e7f5))
  (segment (start 170.625622 50.346425) (end 171.395622 50.346425) (width 0.5) (layer "F.Cu") (net 1) (tstamp 41fddbee-9105-4269-a6b5-60727d721bf1))
  (segment (start 171.395622 50.346425) (end 171.625622 50.116425) (width 0.3) (layer "F.Cu") (net 1) (tstamp 4a2ad2a7-feae-4e97-8cc7-6fed08c0e836))
  (segment (start 166.5968 38.037603) (end 165.900622 37.341425) (width 0.5) (layer "F.Cu") (net 1) (tstamp 4c948283-fc1d-4161-b75d-ce717eb024be))
  (segment (start 167.400622 48.091425) (end 167.400622 47.528925) (width 0.15) (layer "F.Cu") (net 1) (tstamp 4d95a9ba-d7ea-44d3-8ea5-5a9b20344eff))
  (segment (start 169.563372 50.266425) (end 169.969272 49.860525) (width 0.5) (layer "F.Cu") (net 1) (tstamp 5cc8fe45-0170-4cc3-bcbd-f4a4bf558b20))
  (segment (start 166.700622 48.791425) (end 167.400622 48.091425) (width 0.15) (layer "F.Cu") (net 1) (tstamp 6567ba8f-0ed1-479f-88aa-af7328e273c4))
  (segment (start 181.3425 42.405) (end 182.8925 42.405) (width 0.3) (layer "F.Cu") (net 1) (tstamp 6b77fe87-6712-4482-93eb-23bdbf6ce03d))
  (segment (start 167.000622 36.691425) (end 166.975622 36.666425) (width 0.15) (layer "F.Cu") (net 1) (tstamp 6e446600-d6c5-47ff-b75f-d1aea5609bb9))
  (segment (start 167.900622 36.691425) (end 167.000622 36.691425) (width 0.5) (layer "F.Cu") (net 1) (tstamp 6f55d6fa-0e0f-443d-9733-e0f31d2d6d50))
  (segment (start 174.455622 40.616425) (end 175.025622 40.616425) (width 0.5) (layer "F.Cu") (net 1) (tstamp 725708c7-c77a-4d79-98f6-1283e484f4f7))
  (segment (start 173.89 29.635) (end 173.89 28.8) (width 1) (layer "F.Cu") (net 1) (tstamp 77fc103b-e1ae-4688-9ecb-aa6e7dd4964b))
  (segment (start 182.8925 42.405) (end 182.93 42.4425) (width 0.3) (layer "F.Cu") (net 1) (tstamp 7ac1bc6c-0594-4a8b-9a43-5b26ff882d19))
  (segment (start 179.29 38.78) (end 179.45 38.94) (width 0.3) (layer "F.Cu") (net 1) (tstamp 8d822adb-2096-42e5-b1ff-b5da140d0d26))
  (segment (start 179.975 34.458278) (end 179.975 34.684446) (width 0.3) (layer "F.Cu") (net 1) (tstamp a7c7ef2f-0756-47bc-987d-d216065975e6))
  (segment (start 174.235622 39.726425) (end 175.055622 39.726425) (width 0.3) (layer "F.Cu") (net 1) (tstamp b1c5867a-6fb8-4351-9d18-19703dc448ac))
  (segment (start 175.055622 39.726425) (end 175.065622 39.736425) (width 0.3) (layer "F.Cu") (net 1) (tstamp b4346450-9744-4af1-9670-846c2a5490e5))
  (segment (start 168.825622 36.566425) (end 168.825622 37.361425) (width 0.5) (layer "F.Cu") (net 1) (tstamp b8ff8e53-373d-4ee3-9971-f4618d46619f))
  (segment (start 180.248966 34.047481) (end 180.248966 34.184312) (width 0.3) (layer "F.Cu") (net 1) (tstamp cd8d0d89-2370-419f-8e4c-ea27e501a6fc))
  (segment (start 178.4 35.8125) (end 178.4 36.8625) (width 0.3) (layer "F.Cu") (net 1) (tstamp dcd8cd20-f546-4fa1-beed-2809fd8b2c82))
  (segment (start 174.255622 40.816425) (end 174.455622 40.616425) (width 0.5) (layer "F.Cu") (net 1) (tstamp defe1eb0-dfde-43cf-92bf-a6b933fca411))
  (segment (start 174.901357 42.39069) (end 174.777092 42.266425) (width 0.15) (layer "F.Cu") (net 1) (tstamp ffefaff6-d931-4f1f-82e5-1d30a5e89e94))
  (via (at 175.21 30.66) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (free) (net 1) (tstamp 024cf13b-9eb7-4111-800d-f8cb9ff275a1))
  (via (at 180.33 26.19) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (free) (net 1) (tstamp 086124cd-d9db-4e00-ab62-e9fa95254b9b))
  (via (at 161.375622 54.466425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (free) (net 1) (tstamp 13c8c63d-b563-424c-b938-3b38b8990f52))
  (via (at 162.025622 51.266425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (free) (net 1) (tstamp 146a6de5-fbcc-438e-82d3-47933252b1ee))
  (via (at 163.125622 50.466425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 1) (tstamp 1a425fee-d5a0-45fd-9805-65e6fe756508))
  (via (at 173.13 26.11) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (free) (net 1) (tstamp 299ca1a2-c686-4e03-8ef5-f2f01e03ce4e))
  (via (at 177.63 26.09) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (free) (net 1) (tstamp 33f0efc7-ea0c-44eb-a199-4d7eeb89b022))
  (via (at 171.675622 51.416425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (free) (net 1) (tstamp 3460c98a-c9cd-4408-a591-e8bca2bfb9b7))
  (via (at 162.725622 54.466425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (free) (net 1) (tstamp 34f3ef08-e2a6-41e1-8604-6a766ded8225))
  (via (at 169.525622 54.416425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (free) (net 1) (tstamp 36791829-fbc9-4d8f-a035-18e9dbfe92a5))
  (via (at 175.025622 40.616425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 1) (tstamp 43bedd69-e0c4-47f7-b640-6ec89d29f32b))
  (via (at 162.725622 52.166425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (free) (net 1) (tstamp 48409d8f-4985-4bdc-b70c-6016d9a8f1d9))
  (via (at 165.875622 37.416425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 1) (tstamp 5708f39e-8c1f-47e8-9aba-86a17acbc225))
  (via (at 169.969272 49.860525) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 1) (tstamp 5ee92087-5126-4817-88b9-ea2a0fbd0626))
  (via (at 175.34 23.82) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (free) (net 1) (tstamp 66334663-f5c7-47d9-b554-3cd597bf9e23))
  (via (at 174.901357 42.39069) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 1) (tstamp 67e94e44-3921-4f66-abef-325b1fa82f0f))
  (via (at 178.07 23.57) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (free) (net 1) (tstamp 7f61f7ee-adaa-405f-8297-3f1341c50812))
  (via (at 175.55 26.2) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (free) (net 1) (tstamp 89d4a818-fb15-42c5-ae72-e402105236e5))
  (via (at 180.248966 34.047481) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 1) (tstamp a562e007-30e9-4f9b-967c-9a0edc518ec1))
  (via (at 172.755622 39.026425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 1) (tstamp ad427a8b-a2c8-406b-8462-158f5325a2df))
  (via (at 163.625622 49.416425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (free) (net 1) (tstamp b4007128-ba16-4fb8-b2f1-5c443bb4c929))
  (via (at 171.125622 52.166425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (free) (net 1) (tstamp b8d16d5c-ba96-402e-9f04-f815089a5d87))
  (via (at 178.4 36.8625) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 1) (tstamp c25ccfc1-c6bb-465d-8079-0a8811864680))
  (via (at 180 30.64) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (free) (net 1) (tstamp c367fa70-b7bd-4551-8cc0-bb39ca3b6b1c))
  (via (at 183.26 26.2) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (free) (net 1) (tstamp cb807100-2ef7-4381-b91a-b625c4b013f5))
  (via (at 169.325622 53.366425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (free) (net 1) (tstamp ccf1d0a9-a5da-4408-be73-03d5c25a31a7))
  (via (at 171.625622 50.116425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 1) (tstamp cf1cdaba-f3fb-4756-97c0-4d252ada7573))
  (via (at 161.375622 52.266425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 1) (tstamp cf5fb76e-167f-43a1-a5b8-473e4a0bfc20))
  (via (at 170.225622 53.466425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 1) (tstamp d392e314-3412-47d2-ab8d-96a1ac123ea2))
  (via (at 175.065622 39.736425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 1) (tstamp d763bf75-bfcb-4972-8685-f90e8734b63a))
  (via (at 168.825622 36.566425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 1) (tstamp e35c9b6b-301a-4a72-98db-3d68e6c2fa12))
  (via (at 166.975622 36.666425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 1) (tstamp e85cda8c-4b7d-4134-a13a-7a4660e6c36c))
  (via (at 166.700622 48.791425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 1) (tstamp ea1db673-245a-4b20-833e-180b428cd8e0))
  (via (at 162.125622 53.316425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (free) (net 1) (tstamp faca8860-2d72-4d37-b777-260a0343819a))
  (segment (start 183.06 33.89) (end 184.89 32.06) (width 0.73) (layer "F.Cu") (net 2) (tstamp 0544517b-e3d1-453a-a7b0-67f67dd65504))
  (segment (start 181.8925 41.755) (end 182.93 40.7175) (width 0.3) (layer "F.Cu") (net 2) (tstamp 09baab7e-4cae-4523-87cc-932986ff28b2))
  (segment (start 179.2875 34.6125) (end 179.2875 34.160418) (width 0.3) (layer "F.Cu") (net 2) (tstamp 19d38f89-ff4e-47ce-814b-9c1e6c6d92d9))
  (segment (start 180.75 34.9875) (end 181.0125 35.25) (width 0.3) (layer "F.Cu") (net 2) (tstamp 1df7b156-28d9-4fd1-b691-032deaf0fd9e))
  (segment (start 181.0125 35.25) (end 181.7 35.25) (width 0.3) (layer "F.Cu") (net 2) (tstamp 2065f75e-f9e5-4b8b-b49a-a9bdf1b19b20))
  (segment (start 182.509546 43.40707) (end 182.40707 43.40707) (width 0.3) (layer "F.Cu") (net 2) (tstamp 2b3bce9b-5ff1-41a1-861f-0b2b1eee4553))
  (segment (start 181.7 35.25) (end 183.06 33.89) (width 0.3) (layer "F.Cu") (net 2) (tstamp 32c16d4f-aa29-4468-96a8-445b1b07ec83))
  (segment (start 183.54 35.29) (end 183.06 34.81) (width 0.3) (layer "F.Cu") (net 2) (tstamp 4f5679db-69a0-41fd-9fb8-840b275bbbb1))
  (segment (start 180.000437 33.447481) (end 180.497495 33.447481) (width 0.3) (layer "F.Cu") (net 2) (tstamp 6e54351a-4254-43f7-b2cb-e75096c53f59))
  (segment (start 180.848966 33.798952) (end 180.848966 34.888534) (width 0.3) (layer "F.Cu") (net 2) (tstamp 79f49511-3a4f-4b36-b79b-0430dbc1561e))
  (segment (start 179.2875 34.160418) (end 180.000437 33.447481) (width 0.3) (layer "F.Cu") (net 2) (tstamp 7ef88801-c37d-4a61-865d-51f0de4eba86))
  (segment (start 180.497495 33.447481) (end 180.848966 33.798952) (width 0.3) (layer "F.Cu") (net 2) (tstamp 818eca02-1d49-4284-8363-b40544075dc4))
  (segment (start 184.05 41.8375) (end 184.05 43.36) (width 0.3) (layer "F.Cu") (net 2) (tstamp a81d0907-61ba-4d68-a5e0-f65f13c9bb71))
  (segment (start 182.40707 43.40707) (end 182.055 43.055) (width 0.3) (layer "F.Cu") (net 2) (tstamp bafd8d00-fb91-422c-bd86-05a5dc8f53e9))
  (segment (start 184.89 32.06) (end 184.89 27.14) (width 0.73) (layer "F.Cu") (net 2) (tstamp bcb5fc1c-1cfe-49cd-adb2-92294ad569b8))
  (segment (start 181.3425 43.055) (end 182.055 43.055) (width 0.3) (layer "F.Cu") (net 2) (tstamp c2b4ee0b-d08f-4abb-aa50-f6212864989a))
  (segment (start 181.3425 41.755) (end 181.8925 41.755) (width 0.3) (layer "F.Cu") (net 2) (tstamp c66064f8-1b17-4db9-b74b-f82144b48dc2))
  (segment (start 179.825 35.9125) (end 180.75 34.9875) (width 0.3) (layer "F.Cu") (net 2) (tstamp c899d7d5-97dd-4068-8d67-32511c701061))
  (segment (start 179.2875 35.9125) (end 179.825 35.9125) (width 0.3) (layer "F.Cu") (net 2) (tstamp c9a126a3-0dd1-43dd-a476-3fd03aec2e44))
  (segment (start 183.06 34.81) (end 183.06 33.89) (width 0.3) (layer "F.Cu") (net 2) (tstamp efb0229e-2896-4338-832a-16758fdf2428))
  (segment (start 180.848966 34.888534) (end 180.75 34.9875) (width 0.3) (layer "F.Cu") (net 2) (tstamp f0ed5d76-d9a7-4246-a94a-42c7e784f346))
  (segment (start 182.93 40.7175) (end 184.05 41.8375) (width 0.3) (layer "F.Cu") (net 2) (tstamp f33d2821-44d8-4c44-ad98-173cf1b46fda))
  (via (at 184.05 43.36) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 2) (tstamp 32ed52bb-1f7e-46c4-83cd-6244214b9f63))
  (via (at 182.509546 43.40707) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 2) (tstamp 5f4ef170-4827-43f3-8dba-8eac4f0e9478))
  (via (at 183.54 35.29) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 2) (tstamp e14d2cb6-df57-45fb-859c-85b6899aaf0e))
  (segment (start 185.75 36.44) (end 185.75 40.05) (width 0.3) (layer "B.Cu") (net 2) (tstamp 1d9ca887-403b-4f9e-974a-f5686b76d8e8))
  (segment (start 184.05 43.36) (end 182.556616 43.36) (width 0.3) (layer "B.Cu") (net 2) (tstamp 3026afa5-b1df-41a6-a936-c27854c725a3))
  (segment (start 184.6 35.29) (end 185.75 36.44) (width 0.3) (layer "B.Cu") (net 2) (tstamp 30aee65a-ff01-4426-b4f1-f5af3c20a566))
  (segment (start 182.556616 43.36) (end 182.509546 43.40707) (width 0.3) (layer "B.Cu") (net 2) (tstamp 31118d0a-7d5f-4f36-9646-0dbf2d61cd9c))
  (segment (start 183.54 35.29) (end 184.6 35.29) (width 0.3) (layer "B.Cu") (net 2) (tstamp 674fce35-fe7c-4276-84f4-df5a7a301209))
  (segment (start 184.84 43.36) (end 184.85 43.35) (width 0.3) (layer "B.Cu") (net 2) (tstamp 68776b1e-2c89-4e79-84f2-e778da331481))
  (segment (start 184.05 43.36) (end 184.84 43.36) (width 0.3) (layer "B.Cu") (net 2) (tstamp 77846305-bae2-4006-ac71-239b36c6bb06))
  (segment (start 184.85 43.35) (end 185.75 43.35) (width 0.3) (layer "B.Cu") (net 2) (tstamp f10a9f48-dfbe-4bf4-a79f-bec3cb8efc11))
  (segment (start 168.600622 39.216425) (end 167.900622 38.516425) (width 0.15) (layer "F.Cu") (net 5) (tstamp 04164394-9633-483b-a1b0-0b093cc90a7b))
  (segment (start 169.000622 40.653925) (end 169.000622 38.496425) (width 0.15) (layer "F.Cu") (net 5) (tstamp 072248f3-5c9a-48fc-953e-b84f74c5e3da))
  (segment (start 170.850622 41.437785) (end 170.650622 41.237785) (width 0.3) (layer "F.Cu") (net 5) (tstamp 081c59e2-276e-4bd4-ae63-daa7460cfd8d))
  (segment (start 177.5125 35.2625) (end 177.5125 34.6125) (width 0.3) (layer "F.Cu") (net 5) (tstamp 0e9753b2-8571-4031-a91f-b75f5643dec7))
  (segment (start 168.600622 40.653925) (end 168.600622 39.216425) (width 0.15) (layer "F.Cu") (net 5) (tstamp 1ab8f97c-ea20-4ecd-8aee-b4953f8d3546))
  (segment (start 168.400622 41.391425) (end 168.600622 41.191425) (width 0.15) (layer "F.Cu") (net 5) (tstamp 1d112e67-dca9-4dad-8e4d-ea89ed2296ed))
  (segment (start 168.600622 47.528925) (end 168.600622 48.841424) (width 0.3) (layer "F.Cu") (net 5) (tstamp 1d6638a7-d067-428e-b461-bc64483def1a))
  (segment (start 172.396982 45.066425) (end 173.345622 45.066425) (width 0.3) (layer "F.Cu") (net 5) (tstamp 28bdd809-437e-48e9-bc5d-c08c523fc601))
  (segment (start 173.195622 42.266425) (end 173.195622 41.956426) (width 0.15) (layer "F.Cu") (net 5) (tstamp 2e83edb2-e798-4034-9d3c-3dd818f8fbe9))
  (segment (start 173.275622 39.726425) (end 173.010622 39.991425) (width 0.15) (layer "F.Cu") (net 5) (tstamp 306e59b9-8a81-4420-9c25-b083d8077566))
  (segment (start 168.271982 46.666425) (end 166.775622 46.666425) (width 0.3) (layer "F.Cu") (net 5) (tstamp 30989b8a-a21c-4c52-ad22-57b29c15d402))
  (segment (start 166.775622 46.666425) (end 165.700622 45.591425) (width 0.3) (layer "F.Cu") (net 5) (tstamp 52bcfb44-199f-4292-b316-987b4c572897))
  (segment (start 171.838122 41.491425) (end 172.730621 41.491425) (width 0.15) (layer "F.Cu") (net 5) (tstamp 576ed537-715a-4c19-a32c-e73e5394f921))
  (segment (start 167.900622 37.651425) (end 167.360622 37.651425) (width 0.3) (layer "F.Cu") (net 5) (tstamp 5d07f4ed-14f8-481e-a7d6-12768520db0d))
  (segment (start 168.600622 46.995065) (end 168.271982 46.666425) (width 0.3) (layer "F.Cu") (net 5) (tstamp 673f88c9-88af-4d77-a4af-de05eca3751f))
  (segment (start 170.850622 45.091425) (end 170.850622 41.437785) (width 0.3) (layer "F.Cu") (net 5) (tstamp 69473e61-9b67-4c63-910c-93b26527de29))
  (segment (start 165.700622 45.295065) (end 165.496982 45.091425) (width 0.3) (layer "F.Cu") (net 5) (tstamp 6f234421-91c2-4271-9d1c-0fb0300c2f2b))
  (segment (start 168.600622 41.191425) (end 168.600622 41.116425) (width 0.15) (layer "F.Cu") (net 5) (tstamp 71dc5101-85c4-4521-82b1-b5a3e9d27c59))
  (segment (start 168.825622 38.321425) (end 167.905622 38.321425) (width 0.3) (layer "F.Cu") (net 5) (tstamp 828fbebf-62a6-446b-8355-0d527efe40e8))
  (segment (start 168.600622 41.116425) (end 168.600622 41.053925) (width 0.15) (layer "F.Cu") (net 5) (tstamp 85502fb1-21b5-467b-93b5-7b174b59a99a))
  (segment (start 168.600622 47.528925) (end 168.600622 46.995065) (width 0.3) (layer "F.Cu") (net 5) (tstamp 8b23c53a-fdb1-480b-98ce-e2500fdc8be2))
  (segment (start 169.065623 49.306425) (end 169.375622 49.306425) (width 0.3) (layer "F.Cu") (net 5) (tstamp 8b366502-06d4-48f8-8147-ba528f356980))
  (segment (start 167.900622 38.516425) (end 167.900622 38.316425) (width 0.15) (layer "F.Cu") (net 5) (tstamp 8d9d7c03-5daf-4c7d-8df6-144c04091f85))
  (segment (start 165.596982 41.391425) (end 168.400622 41.391425) (width 0.15) (layer "F.Cu") (net 5) (tstamp 91626b3a-37bc-4d1b-bf52-18315adcd9ce))
  (segment (start 173.010622 39.991425) (end 170.800622 39.991425) (width 0.15) (layer "F.Cu") (net 5) (tstamp 94bb7abc-92a7-4903-bd1f-9537e4e88eba))
  (segment (start 167.905622 38.321425) (end 167.900622 38.316425) (width 0.3) (layer "F.Cu") (net 5) (tstamp 9622a67f-cbca-4e51-9719-cb5a317593d1))
  (segment (start 170.600622 40.653925) (end 170.600622 41.091425) (width 0.15) (layer "F.Cu") (net 5) (tstamp 99b8a1a8-1f88-4ab7-ab20-3352b1d335d2))
  (segment (start 162.910622 45.091425) (end 164.963122 45.091425) (width 0.3) (layer "F.Cu") (net 5) (tstamp 9ed03f11-3737-492c-bb7b-178df1c44312))
  (segment (start 173.195622 41.956426) (end 172.730621 41.491425) (width 0.15) (layer "F.Cu") (net 5) (tstamp abaaf117-ba95-4786-8b30-d05e0cbe0c9f))
  (segment (start 170.800622 39.991425) (end 170.600622 40.191425) (width 0.15) (layer "F.Cu") (net 5) (tstamp b0f21c2e-3a32-4bbc-80f1-5307c22d22a5))
  (segment (start 165.496982 41.491425) (end 165.596982 41.391425) (width 0.15) (layer "F.Cu") (net 5) (tstamp b3320965-0406-47c6-a5af-5b6fb8b6eb50))
  (segment (start 170.600622 40.191425) (end 170.600622 40.653925) (width 0.15) (layer "F.Cu") (net 5) (tstamp b5ad4c54-3df3-4794-a270-31d2c1efb8fe))
  (segment (start 168.600622 48.841424) (end 169.065623 49.306425) (width 0.3) (layer "F.Cu") (net 5) (tstamp bf2b0ef8-f527-4fff-bd75-8313232e7511))
  (segment (start 165.496982 41.491425) (end 164.963122 41.491425) (width 0.3) (layer "F.Cu") (net 5) (tstamp c0798fbe-b369-49bb-a3fd-9eb584d87cb5))
  (segment (start 165.700622 45.295065) (end 165.700622 41.695065) (width 0.3) (layer "F.Cu") (net 5) (tstamp c169d611-be4f-42c9-9f0a-6a8575b3cbde))
  (segment (start 163.230622 41.491425) (end 164.963122 41.491425) (width 0.3) (layer "F.Cu") (net 5) (tstamp c22de87e-1261-4644-874a-b20274c375b6))
  (segment (start 171.838122 45.091425) (end 172.371982 45.091425) (width 0.3) (layer "F.Cu") (net 5) (tstamp c4f0fa7d-bbe7-48d7-ab4d-c543ae4b65b0))
  (segment (start 167.900622 38.316425) (end 167.900622 37.651425) (width 0.15) (layer "F.Cu") (net 5) (tstamp c5e5a203-406e-40e3-9a9b-bdc278b1b2d1))
  (segment (start 165.700622 41.695065) (end 165.496982 41.491425) (width 0.3) (layer "F.Cu") (net 5) (tstamp c5f39be9-829d-413d-bd5a-9c7ffefb79f1))
  (segment (start 171.838122 45.091425) (end 170.850622 45.091425) (width 0.15) (layer "F.Cu") (net 5) (tstamp db4389b2-daec-4d19-a102-506da1dd4a63))
  (segment (start 165.496982 45.091425) (end 164.963122 45.091425) (width 0.3) (layer "F.Cu") (net 5) (tstamp db4677b9-5f0f-4f4b-8978-62ea90fb308f))
  (segment (start 168.600622 41.116425) (end 168.600622 40.653925) (width 0.15) (layer "F.Cu") (net 5) (tstamp e52fd5be-b316-435f-8664-ad3a305d49e8))
  (segment (start 165.700622 45.591425) (end 165.700622 45.295065) (width 0.3) (layer "F.Cu") (net 5) (tstamp f4f476c4-8fde-4022-aaa8-3d948ba064a5))
  (segment (start 181.22 38.81) (end 183.04 38.81) (width 1) (layer "F.Cu") (net 7) (tstamp 70b82114-b33b-4c82-a85d-554019b86163))
  (segment (start 181.19 38.78) (end 181.22 38.81) (width 1) (layer "F.Cu") (net 7) (tstamp faa2a28c-a972-4d47-966c-2246a0f58150))
  (segment (start 174.426358 43.116425) (end 171.863122 43.116425) (width 0.15) (layer "F.Cu") (net 8) (tstamp 738c208a-9588-4dce-8614-74dcca0683a0))
  (segment (start 171.000622 40.653925) (end 173.308122 40.653925) (width 0.3) (layer "F.Cu") (net 9) (tstamp aabd9048-275a-4e09-a61f-d3bde807a758))
  (segment (start 179.5675 41.755) (end 179.5675 42.405) (width 0.15) (layer "F.Cu") (net 9) (tstamp f2995003-543b-4906-a0cb-23ec38af4269))
  (segment (start 170.200622 40.051425) (end 170.955622 39.296425) (width 0.15) (layer "F.Cu") (net 10) (tstamp 048b93f2-b425-467e-b700-ee1981413b51))
  (segment (start 168.200622 39.916425) (end 168.200622 39.641425) (width 0.15) (layer "F.Cu") (net 10) (tstamp 0801dfda-ea69-4399-ba32-85e50403b607))
  (segment (start 169.500622 41.891425) (end 168.200622 41.891425) (width 0.3) (layer "F.Cu") (net 10) (tstamp 0a7a4691-5221-4567-b2a6-d80247d33e52))
  (segment (start 168.200622 39.641425) (end 167.275622 38.716425) (width 0.15) (layer "F.Cu") (net 10) (tstamp 14b34f88-e787-4f11-912d-025e078a72d6))
  (segment (start 169.425622 48.516425) (end 170.295622 49.386425) (width 0.15) (layer "F.Cu") (net 10) (tstamp 17425200-47c8-4f56-9904-f888755b3420))
  (segment (start 169.000622 48.241425) (end 169.000622 47.528925) (width 0.15) (layer "F.Cu") (net 10) (tstamp 23fc1a7c-bd3a-447c-9491-6e2f74df2f99))
  (segment (start 168.200622 40.653925) (end 168.200622 39.916425) (width 0.15) (layer "F.Cu") (net 10) (tstamp 348e3745-ea08-40ab-8a2a-8c519bb650b5))
  (segment (start 168.200622 41.891425) (end 168.175622 41.916425) (width 0.15) (layer "F.Cu") (net 10) (tstamp 55217dd3-59e9-4076-979a-0789d2efa011))
  (segment (start 168.200622 39.916425) (end 168.200622 39.786611) (width 0.15) (layer "F.Cu") (net 10) (tstamp 5b755d62-57b6-4638-8d1c-2474ff4e8841))
  (segment (start 169.425622 48.666425) (end 169.000622 48.241425) (width 0.15) (layer "F.Cu") (net 10) (tstamp 5ed886b5-b78a-472a-9042-d501be916467))
  (segment (start 166.325622 45.560327) (end 166.98172 46.216425) (width 0.3) (layer "F.Cu") (net 10) (tstamp 60b3e61a-3387-47d9-8739-9fc186311605))
  (segment (start 166.325622 42.622523) (end 166.325622 45.560327) (width 0.3) (layer "F.Cu") (net 10) (tstamp 61043f01-33d0-4082-a75c-1f37e7bd8d9c))
  (segment (start 170.200622 41.191425) (end 169.500622 41.891425) (width 0.3) (layer "F.Cu") (net 10) (tstamp 6c427c17-1e1f-47ef-a070-82a5d1a0f8cc))
  (segment (start 168.200622 39.786611) (end 168.003029 39.589018) (width 0.15) (layer "F.Cu") (net 10) (tstamp 9f6ba524-892c-4921-a194-6c6a092a6401))
  (segment (start 170.200622 40.653925) (end 170.200622 40.051425) (width 0.15) (layer "F.Cu") (net 10) (tstamp b21fa1c2-bdcc-4afb-8dc1-b6851960c1dc))
  (segment (start 168.125622 41.966425) (end 166.98172 41.966425) (width 0.3) (layer "F.Cu") (net 10) (tstamp baf8e99c-34dd-4b09-995d-c0d98c76ca45))
  (segment (start 170.200622 40.653925) (end 170.200622 41.191425) (width 0.3) (layer "F.Cu") (net 10) (tstamp c3c46ffe-6520-4a83-98af-1540ca1114b9))
  (segment (start 170.295622 49.386425) (end 170.625622 49.386425) (width 0.15) (layer "F.Cu") (net 10) (tstamp d1e6e7e1-db02-491a-812f-f420fd94d3f8))
  (segment (start 166.98172 41.966425) (end 166.325622 42.622523) (width 0.3) (layer "F.Cu") (net 10) (tstamp edbc54f8-8333-40cf-9f3a-5d18bed2864e))
  (segment (start 168.175622 41.916425) (end 168.125622 41.966425) (width 0.15) (layer "F.Cu") (net 10) (tstamp f3fec0fa-31dc-48c4-893f-5d7f33ef7fd6))
  (segment (start 168.975622 46.216425) (end 168.975622 47.503925) (width 0.15) (layer "F.Cu") (net 10) (tstamp f4500e5a-3e35-41c4-9818-3337cd85efc3))
  (segment (start 166.98172 46.216425) (end 168.975622 46.216425) (width 0.3) (layer "F.Cu") (net 10) (tstamp fc0dd5f6-ab32-4b37-a079-378f48bd1fa2))
  (via (at 168.975622 46.216425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 10) (tstamp 4749ec9a-19fd-4197-9be3-e71347dd935e))
  (via (at 169.425622 48.516425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 10) (tstamp 63221212-5b6d-4f49-9c90-29d15b7d212d))
  (via (at 168.003029 39.589018) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 10) (tstamp 82514560-75c7-4898-9517-a5ebc37a1dea))
  (via (at 168.175622 41.916425) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 10) (tstamp 91ef0fff-1c62-4e50-8a91-38c74f99893f))
  (segment (start 168.175622 39.816425) (end 168.175622 41.916425) (width 0.3) (layer "B.Cu") (net 10) (tstamp 0139b1b5-332a-49e0-aaa6-79f57e62bbc1))
  (segment (start 168.975622 46.216425) (end 168.975622 48.216425) (width 0.3) (layer "B.Cu") (net 10) (tstamp 03396cad-da84-4bd3-8f31-0a7b9ca2a17d))
  (segment (start 168.975622 48.216425) (end 169.425622 48.666425) (width 0.3) (layer "B.Cu") (net 10) (tstamp 15dcfb6b-dd78-4b53-963b-41412a72fa88))
  (segment (start 168.003029 39.643832) (end 168.175622 39.816425) (width 0.15) (layer "B.Cu") (net 10) (tstamp a7518625-c41c-4508-951f-4c93a995a5d8))
  (segment (start 168.003029 39.589018) (end 168.003029 39.643832) (width 0.15) (layer "B.Cu") (net 10) (tstamp c39cdf5b-3632-4b88-8f3f-9b06a2a96130))
  (segment (start 165.300622 50.941425) (end 166.050622 50.191425) (width 0.15) (layer "F.Cu") (net 11) (tstamp 151f6df1-8fef-44a8-a8a5-4b05323ede51))
  (segment (start 164.275622 53.616425) (end 165.300622 52.591425) (width 0.15) (layer "F.Cu") (net 11) (tstamp 4d82c776-6739-4e71-bba7-54a016b61a06))
  (segment (start 167.800622 48.441425) (end 167.800622 47.528925) (width 0.15) (layer "F.Cu") (net 11) (tstamp 5950bfad-d91b-4323-b426-dd63ec6c6f1b))
  (segment (start 166.050622 50.191425) (end 167.800622 48.441425) (width 0.15) (layer "F.Cu") (net 11) (tstamp 75e0b8f9-9ab8-469d-8c12-6c70eea96d31))
  (segment (start 165.555622 49.716425) (end 165.575622 49.716425) (width 0.15) (layer "F.Cu") (net 11) (tstamp b4b18f18-53d7-42cf-b8a7-c842f52761fc))
  (segment (start 165.300622 52.591425) (end 165.300622 50.941425) (width 0.15) (layer "F.Cu") (net 11) (tstamp bc7655e2-5f28-4f7d-8ca5-fd9f4045d6b5))
  (segment (start 165.575622 49.716425) (end 166.050622 50.191425) (width 0.15) (layer "F.Cu") (net 11) (tstamp d79b76dd-516a-4cf6-8711-b1bc2a346af6))
  (segment (start 168.125622 53.186425) (end 168.125622 52.061425) (width 0.15) (layer "F.Cu") (net 12) (tstamp 2fb7a100-6a71-41c4-b9d7-2f5b9740078f))
  (segment (start 168.125622 52.061425) (end 168.270622 51.916425) (width 0.15) (layer "F.Cu") (net 12) (tstamp 3a80b9c8-e405-4e27-b8f3-392db439a21d))
  (segment (start 166.525622 51.866425) (end 168.565622 51.866425) (width 0.25) (layer "F.Cu") (net 12) (tstamp 4d170a66-c363-4b54-8646-11fffd7f1159))
  (segment (start 166.475622 51.916425) (end 166.525622 51.866425) (width 0.25) (layer "F.Cu") (net 12) (tstamp 70d2234e-640c-453b-8f8a-b76e9584f002))
  (segment (start 185.0225 36.73) (end 185.0225 38.6925) (width 0.5) (layer "F.Cu") (net 13) (tstamp 6f26edf1-e497-4cb1-bc78-5f7c47eaee9a))
  (segment (start 185.0225 38.6925) (end 185.14 38.81) (width 0.5) (layer "F.Cu") (net 13) (tstamp 8d6f23f7-c4d2-4fb4-a876-24aefc229a96))
  (segment (start 184.89 25.04) (end 184.89 23.42) (width 1) (layer "F.Cu") (net 14) (tstamp 503ba91f-48d4-4a70-8bf1-17ad09cdbe27))
  (segment (start 184.89 23.42) (end 184.9 23.41) (width 1) (layer "F.Cu") (net 14) (tstamp 88702dac-0535-4741-9ccf-4ac9e26b41f6))
  (segment (start 182.492 36.73) (end 183.1975 36.73) (width 1) (layer "F.Cu") (net 16) (tstamp 1dcaf322-a839-4e4d-a3b0-d754b685ee9d))
  (segment (start 182.182 36.42) (end 182.492 36.73) (width 1) (layer "F.Cu") (net 16) (tstamp 4c4a846a-91d9-433b-893b-f16b39be9fe4))
  (segment (start 181.91 28.76) (end 181.91 29.85) (width 1) (layer "F.Cu") (net 16) (tstamp 7c80ca51-e7a9-4350-8c8c-78a2f1a48498))
  (segment (start 181.91 29.85) (end 183.46 31.4) (width 1) (layer "F.Cu") (net 16) (tstamp ab6451bf-fe82-48aa-a107-766bd6a52b45))
  (segment (start 181.91 28.76) (end 181.91 23.71) (width 1) (layer "F.Cu") (net 16) (tstamp aef4a9a0-67c3-4ce8-a5fd-8abd3a0c13f0))
  (via (at 182.182 36.42) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 16) (tstamp 4e99110d-aacb-4320-90c2-61094fd649d5))
  (via (at 183.46 31.4) (size 0.6) (drill 0.35) (layers "F.Cu" "B.Cu") (net 16) (tstamp 7b551604-0804-46d8-8196-301d9eeff75b))
  (segment (start 182.182 32.678) (end 183.46 31.4) (width 1) (layer "B.Cu") (net 16) (tstamp b79ce449-bfc9-4d7e-8f1a-5d66141faf53))
  (segment (start 182.182 36.42) (end 182.182 32.678) (width 1) (layer "B.Cu") (net 16) (tstamp c546635f-b1aa-47aa-849d-1de32b6c1ea5))
  (segment (start 184.585 21.27) (end 184.9 21.585) (width 1) (layer "F.Cu") (net 17) (tstamp 67977f4a-3f1c-4da1-8772-8957a841fbef))
  (segment (start 173.355 21.27) (end 184.585 21.27) (width 1) (layer "F.Cu") (net 17) (tstamp 9fdc342a-d262-44f3-af78-26c9d79dc34d))
  (segment (start 171.77 23.675) (end 171.77 28.725) (width 1) (layer "F.Cu") (net 17) (tstamp a2e8c2d8-77e3-47ee-8967-8749b86b1157))
  (segment (start 171.75 22.875) (end 173.355 21.27) (width 1) (layer "F.Cu") (net 17) (tstamp b5ed7c00-f707-441d-a2e0-0cf29f6cf4c1))
  (segment (start 171.75 23.71) (end 171.75 22.875) (width 1) (layer "F.Cu") (net 17) (tstamp b7afe91e-aba7-4f7f-8245-5acb11cc250c))
  (segment (start 164.963122 42.291425) (end 163.684886 42.291425) (width 0.15) (layer "F.Cu") (net 25) (tstamp 4ba1b840-8aa4-48ed-a79c-2276c7067a4f))
  (segment (start 164.963122 41.891425) (end 163.660622 41.891425) (width 0.15) (layer "F.Cu") (net 26) (tstamp 5512b3dd-5bf5-4edf-8ebc-b8cf2b82955e))
  (segment (start 170.6618 48.866425) (end 169.800622 48.005247) (width 0.15) (layer "F.Cu") (net 28) (tstamp 4b085691-ed6b-4f92-98e8-104485636855))
  (segment (start 169.800622 48.005247) (end 169.800622 47.528925) (width 0.15) (layer "F.Cu") (net 28) (tstamp 96599127-78ba-4226-8cf8-2c45cb71570b))
  (segment (start 169.400622 38.731425) (end 169.800622 38.331425) (width 0.15) (layer "F.Cu") (net 31) (tstamp 21ea842a-70a6-45ae-8944-6c933f25f9fe))
  (segment (start 169.400622 40.653925) (end 169.400622 38.731425) (width 0.15) (layer "F.Cu") (net 31) (tstamp fc479cb5-a622-4722-9afb-057c5b987362))
  (segment (start 169.800622 40.653925) (end 169.800622 39.331425) (width 0.15) (layer "F.Cu") (net 32) (tstamp 600d3fad-30a9-4306-a479-8f1ad648194b))
  (segment (start 169.800622 39.331425) (end 170.800622 38.331425) (width 0.15) (layer "F.Cu") (net 32) (tstamp 7f8dfce9-9529-4f57-91c8-164a473719a3))
  (segment (start 163.781942 43.891425) (end 164.963122 43.891425) (width 0.15) (layer "F.Cu") (net 33) (tstamp 0d8249d5-20d8-4bfa-82fa-e2f0ce61e6f1))
  (segment (start 163.757678 43.491425) (end 164.963122 43.491425) (width 0.15) (layer "F.Cu") (net 34) (tstamp e914e04a-48a3-460b-89bc-e0dc123ac836))
  (segment (start 163.733414 43.091425) (end 164.963122 43.091425) (width 0.15) (layer "F.Cu") (net 35) (tstamp 9795269b-0132-4308-9863-7c68546bf0f7))
  (segment (start 163.70915 42.691425) (end 164.963122 42.691425) (width 0.15) (layer "F.Cu") (net 36) (tstamp 6ffd47ad-50d0-43a5-901c-76cce7984930))
  (segment (start 174.35283 43.891425) (end 171.838122 43.891425) (width 0.15) (layer "F.Cu") (net 37) (tstamp 7b2c7c8d-4890-4155-80b5-f85dd873d227))
  (segment (start 171.838122 43.491425) (end 174.377094 43.491425) (width 0.15) (layer "F.Cu") (net 38) (tstamp 72d5e9f6-beb6-4fbe-8fc0-ad31e25b21e7))
  (segment (start 171.838122 44.691425) (end 173.1788 44.691425) (width 0.15) (layer "F.Cu") (net 43) (tstamp 3ee6df92-6523-457e-9133-1742c991419b))
  (segment (start 168.200622 50.541425) (end 168.200622 47.528925) (width 0.15) (layer "F.Cu") (net 44) (tstamp 02c3725e-6ce5-4bbf-99cd-07e959ebfa0b))
  (segment (start 169.870622 51.916425) (end 169.575622 51.916425) (width 0.15) (layer "F.Cu") (net 44) (tstamp 2eceecf7-0216-4623-89dd-9fd4646787a8))
  (segment (start 169.575622 51.916425) (end 168.200622 50.541425) (width 0.15) (layer "F.Cu") (net 44) (tstamp b43bf3dd-7d60-4d73-9256-e7d829c9c67d))
  (segment (start 171.838122 45.491425) (end 172.940554 45.491425) (width 0.15) (layer "F.Cu") (net 45) (tstamp d693a8bb-b172-475f-b7f9-175b174782e4))
  (segment (start 171.838122 45.891425) (end 172.91629 45.891425) (width 0.15) (layer "F.Cu") (net 46) (tstamp 57784c80-9812-4f17-893e-033dc8879fcc))
  (segment (start 172.892026 46.291425) (end 171.838122 46.291425) (width 0.15) (layer "F.Cu") (net 47) (tstamp b7f745c4-1569-4177-9630-c1f7b9a94738))
  (segment (start 166.600622 48.141425) (end 166.600622 47.528925) (width 0.15) (layer "F.Cu") (net 50) (tstamp 9e4ac6db-3ed5-4c49-b1b2-3a165203290a))
  (segment (start 171.838122 44.291425) (end 173.154536 44.291425) (width 0.15) (layer "F.Cu") (net 56) (tstamp 22c2b3da-7517-4d83-9724-db0a3d86fefb))
  (segment (start 171.838122 42.691425) (end 172.400622 42.691425) (width 0.15) (layer "F.Cu") (net 57) (tstamp 321b1609-5f2a-4293-8998-7cf61eb6d85e))
  (segment (start 172.400622 42.691425) (end 172.525622 42.816425) (width 0.15) (layer "F.Cu") (net 57) (tstamp a631ebf9-5605-4b96-9398-759f03410808))
  (segment (start 172.525622 42.816425) (end 174.550622 42.816425) (width 0.15) (layer "F.Cu") (net 57) (tstamp ca181a60-6ec4-47e9-ae15-274a0993e46f))
  (segment (start 161.764486 29.115) (end 161.7 29.115) (width 0.15) (layer "F.Cu") (net 60) (tstamp 2b7eaa78-557e-48b3-89d1-7999602d5350))
  (segment (start 167.800622 40.129075) (end 167.800622 40.653925) (width 0.15) (layer "F.Cu") (net 60) (tstamp 4b508794-249f-4f6e-9acb-4b27e016269c))
  (segment (start 167.400622 40.653925) (end 167.400622 40.153339) (width 0.15) (layer "F.Cu") (net 61) (tstamp b4b585d7-ae2e-48bd-ae91-d6fb302ee28f))
  (segment (start 167.000622 40.177603) (end 167.000622 40.653925) (width 0.15) (layer "F.Cu") (net 62) (tstamp 6e588fcb-f7cc-4326-9bc6-b666f956aeed))
  (segment (start 166.600622 40.201867) (end 166.600622 40.653925) (width 0.15) (layer "F.Cu") (net 63) (tstamp 36cb0b5d-75ab-4f2c-a0a7-1ae84c345c4f))
  (segment (start 164.114835 44.291425) (end 164.963122 44.291425) (width 0.15) (layer "F.Cu") (net 67) (tstamp 09aa6c47-d39a-401a-a438-17a029371167))
  (segment (start 163.788121 44.366425) (end 163.913978 44.492282) (width 0.15) (layer "B.Cu") (net 67) (tstamp 5e690c98-9481-4bd2-80b0-34317f4cc903))

  (zone (net 1) (net_name "GND") (layer "F.Cu") (tstamp 0cf7ba82-acef-4131-ad2f-8cffea46dcf6) (hatch edge 0.5)
    (connect_pads (clearance 0.25))
    (min_thickness 0.2) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.3) (thermal_bridge_width 0.3))
    (polygon
      (pts
        (xy 186 20.3)
        (xy 174.15 20.3)
        (xy 170.68 20.31)
        (xy 170.75 25.45)
        (xy 171.45 30.73)
        (xy 177.02 32.63)
        (xy 177.45 40.85)
        (xy 185.8 41)
        (xy 185.91 24.98)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 185.069191 33.758907)
        (xy 185.105155 33.808407)
        (xy 185.11 33.839)
        (xy 185.11 34.914999)
        (xy 185.110001 34.915)
        (xy 185.253058 34.915)
        (xy 185.253064 34.914999)
        (xy 185.341443 34.904386)
        (xy 185.482095 34.848919)
        (xy 185.602564 34.757564)
        (xy 185.602567 34.757561)
        (xy 185.665132 34.675058)
        (xy 185.715359 34.640115)
        (xy 185.776531 34.641369)
        (xy 185.825284 34.678339)
        (xy 185.843014 34.735557)
        (xy 185.835135 35.882958)
        (xy 185.815828 35.941017)
        (xy 185.766082 35.97664)
        (xy 185.704899 35.97622)
        (xy 185.656884 35.941607)
        (xy 185.642549 35.922458)
        (xy 185.642548 35.922457)
        (xy 185.642546 35.922454)
        (xy 185.633699 35.915831)
        (xy 185.527329 35.836202)
        (xy 185.392488 35.78591)
        (xy 185.392483 35.785909)
        (xy 185.392481 35.785908)
        (xy 185.392477 35.785908)
        (xy 185.361249 35.78255)
        (xy 185.332873 35.7795)
        (xy 185.33287 35.7795)
        (xy 184.712133 35.7795)
        (xy 184.712129 35.7795)
        (xy 184.712128 35.779501)
        (xy 184.706621 35.780093)
        (xy 184.652519 35.785908)
        (xy 184.652514 35.785909)
        (xy 184.51767 35.836202)
        (xy 184.402458 35.92245)
        (xy 184.40245 35.922458)
        (xy 184.316202 36.03767)
        (xy 184.26591 36.172511)
        (xy 184.265909 36.172517)
        (xy 184.260885 36.21925)
        (xy 184.2595 36.232129)
        (xy 184.2595 37.227866)
        (xy 184.259501 37.22787)
        (xy 184.265908 37.28748)
        (xy 184.265909 37.287485)
        (xy 184.316202 37.422329)
        (xy 184.398597 37.532394)
        (xy 184.402454 37.537546)
        (xy 184.482329 37.59734)
        (xy 184.517582 37.647348)
        (xy 184.522 37.676593)
        (xy 184.522 38.628042)
        (xy 184.519738 38.649085)
        (xy 184.518141 38.656426)
        (xy 184.521748 38.706855)
        (xy 184.522 38.713918)
        (xy 184.522 38.728299)
        (xy 184.524047 38.74254)
        (xy 184.524802 38.749562)
        (xy 184.528408 38.799983)
        (xy 184.531033 38.80702)
        (xy 184.536264 38.827514)
        (xy 184.537334 38.834954)
        (xy 184.537335 38.834957)
        (xy 184.558335 38.880941)
        (xy 184.561039 38.88747)
        (xy 184.578704 38.934831)
        (xy 184.580519 38.938155)
        (xy 184.591596 38.971331)
        (xy 184.599779 39.027505)
        (xy 184.599781 39.027514)
        (xy 184.652985 39.136343)
        (xy 184.652987 39.136345)
        (xy 184.652988 39.136347)
        (xy 184.738653 39.222012)
        (xy 184.847491 39.27522)
        (xy 184.918051 39.2855)
        (xy 184.964808 39.285499)
        (xy 184.999406 39.291741)
        (xy 185.032517 39.304091)
        (xy 185.176073 39.314359)
        (xy 185.298336 39.287761)
        (xy 185.319381 39.285499)
        (xy 185.361943 39.285499)
        (xy 185.361948 39.285499)
        (xy 185.409542 39.278565)
        (xy 185.432504 39.275221)
        (xy 185.432505 39.27522)
        (xy 185.432509 39.27522)
        (xy 185.432511 39.275218)
        (xy 185.432514 39.275218)
        (xy 185.541343 39.222014)
        (xy 185.541343 39.222013)
        (xy 185.541347 39.222012)
        (xy 185.627012 39.136347)
        (xy 185.627014 39.136341)
        (xy 185.631779 39.12967)
        (xy 185.634225 39.131417)
        (xy 185.66704 39.097482)
        (xy 185.727303 39.086898)
        (xy 185.782279 39.113756)
        (xy 185.810967 39.167799)
        (xy 185.812458 39.185605)
        (xy 185.800687 40.899897)
        (xy 185.78138 40.957956)
        (xy 185.731634 40.993579)
        (xy 185.699911 40.998201)
        (xy 183.781884 40.963745)
        (xy 183.724042 40.943796)
        (xy 183.713658 40.934765)
        (xy 183.684496 40.905603)
        (xy 183.656719 40.851086)
        (xy 183.6555 40.835599)
        (xy 183.6555 40.370829)
        (xy 183.653701 40.354095)
        (xy 183.649251 40.312701)
        (xy 183.643979 40.298566)
        (xy 183.600213 40.181223)
        (xy 183.516119 40.068888)
        (xy 183.516118 40.068887)
        (xy 183.516116 40.068884)
        (xy 183.477881 40.040261)
        (xy 183.403776 39.984786)
        (xy 183.272304 39.93575)
        (xy 183.272299 39.935749)
        (xy 183.272297 39.935748)
        (xy 183.272293 39.935748)
        (xy 183.246011 39.932922)
        (xy 183.214174 39.9295)
        (xy 182.645826 39.9295)
        (xy 182.617823 39.93251)
        (xy 182.587706 39.935748)
        (xy 182.587695 39.93575)
        (xy 182.456223 39.984786)
        (xy 182.343888 40.06888)
        (xy 182.34388 40.068888)
        (xy 182.259786 40.181223)
        (xy 182.21075 40.312695)
        (xy 182.210748 40.312706)
        (xy 182.2045 40.370829)
        (xy 182.2045 40.834615)
        (xy 182.185593 40.892806)
        (xy 182.136093 40.92877)
        (xy 182.103722 40.933599)
        (xy 180.052308 40.896747)
        (xy 179.994466 40.876798)
        (xy 179.959397 40.82666)
        (xy 179.957179 40.818012)
        (xy 179.949986 40.78357)
        (xy 179.941705 40.753332)
        (xy 179.931902 40.724589)
        (xy 179.919985 40.695601)
        (xy 179.862397 40.576819)
        (xy 179.847019 40.549507)
        (xy 179.830526 40.524011)
        (xy 179.830519 40.524001)
        (xy 179.811925 40.498795)
        (xy 179.727183 40.397562)
        (xy 179.705622 40.374802)
        (xy 179.705621 40.374801)
        (xy 179.683426 40.35408)
        (xy 179.683418 40.354073)
        (xy 179.683412 40.354068)
        (xy 179.659259 40.334146)
        (xy 179.659251 40.33414)
        (xy 179.659245 40.334135)
        (xy 179.552445 40.256542)
        (xy 179.552439 40.256538)
        (xy 179.552438 40.256537)
        (xy 179.526019 40.239716)
        (xy 179.526017 40.239715)
        (xy 179.499442 40.225004)
        (xy 179.471128 40.21153)
        (xy 179.34868 40.162227)
        (xy 179.318919 40.152318)
        (xy 179.289596 40.144518)
        (xy 179.258867 40.138333)
        (xy 179.258863 40.138332)
        (xy 179.12047 40.119217)
        (xy 179.104888 40.11755)
        (xy 179.089754 40.1164)
        (xy 179.089751 40.116399)
        (xy 179.08974 40.116399)
        (xy 179.081331 40.116019)
        (xy 179.074079 40.115691)
        (xy 178.452128 40.106677)
        (xy 177.502917 40.092921)
        (xy 177.445008 40.073173)
        (xy 177.409765 40.023157)
        (xy 177.405488 39.999104)
        (xy 177.392745 39.755498)
        (xy 177.368816 39.298064)
        (xy 178.49 39.298064)
        (xy 178.500613 39.386443)
        (xy 178.55608 39.527095)
        (xy 178.647435 39.647564)
        (xy 178.767904 39.738919)
        (xy 178.908556 39.794386)
        (xy 178.996935 39.804999)
        (xy 178.996942 39.805)
        (xy 179.139999 39.805)
        (xy 179.14 39.804999)
        (xy 179.44 39.804999)
        (xy 179.440001 39.805)
        (xy 179.583058 39.805)
        (xy 179.583064 39.804999)
        (xy 179.671443 39.794386)
        (xy 179.812095 39.738919)
        (xy 179.932564 39.647564)
        (xy 180.023919 39.527095)
        (xy 180.079386 39.386443)
        (xy 180.089999 39.298064)
        (xy 180.09 39.298058)
        (xy 180.09 38.930001)
        (xy 180.089999 38.93)
        (xy 179.440001 38.93)
        (xy 179.44 38.930001)
        (xy 179.44 39.804999)
        (xy 179.14 39.804999)
        (xy 179.14 38.930001)
        (xy 179.139999 38.93)
        (xy 178.490001 38.93)
        (xy 178.49 38.930001)
        (xy 178.49 39.298064)
        (xy 177.368816 39.298064)
        (xy 177.340565 38.758028)
        (xy 180.434711 38.758028)
        (xy 180.439123 38.808459)
        (xy 180.4395 38.817087)
        (xy 180.4395 39.302866)
        (xy 180.439501 39.30287)
        (xy 180.445908 39.36248)
        (xy 180.445909 39.362485)
        (xy 180.496202 39.497329)
        (xy 180.57319 39.600171)
        (xy 180.582454 39.612546)
        (xy 180.582457 39.612548)
        (xy 180.582458 39.612549)
        (xy 180.69767 39.698797)
        (xy 180.832511 39.749089)
        (xy 180.832512 39.749089)
        (xy 180.832517 39.749091)
        (xy 180.892127 39.7555)
        (xy 181.487872 39.755499)
        (xy 181.547483 39.749091)
        (xy 181.652989 39.70974)
        (xy 181.682329 39.698797)
        (xy 181.682329 39.698796)
        (xy 181.682331 39.698796)
        (xy 181.797546 39.612546)
        (xy 181.80681 39.60017)
        (xy 181.856819 39.564918)
        (xy 181.886063 39.5605)
        (xy 183.083704 39.5605)
        (xy 183.083709 39.5605)
        (xy 183.214255 39.545241)
        (xy 183.379117 39.485237)
        (xy 183.525696 39.38883)
        (xy 183.646092 39.261218)
        (xy 183.733812 39.109281)
        (xy 183.78413 38.94121)
        (xy 183.790752 38.827514)
        (xy 183.794331 38.766068)
        (xy 183.794331 38.766065)
        (xy 183.763866 38.593292)
        (xy 183.763865 38.593289)
        (xy 183.694377 38.432196)
        (xy 183.58961 38.29147)
        (xy 183.554412 38.261935)
        (xy 183.455216 38.178699)
        (xy 183.444975 38.173556)
        (xy 183.298433 38.09996)
        (xy 183.298429 38.099959)
        (xy 183.298423 38.099956)
        (xy 183.127723 38.0595)
        (xy 183.127721 38.0595)
        (xy 181.930979 38.0595)
        (xy 181.872788 38.040593)
        (xy 181.851726 38.019829)
        (xy 181.797549 37.947458)
        (xy 181.797548 37.947457)
        (xy 181.797546 37.947454)
        (xy 181.797541 37.94745)
        (xy 181.682329 37.861202)
        (xy 181.547488 37.81091)
        (xy 181.547483 37.810909)
        (xy 181.547481 37.810908)
        (xy 181.547477 37.810908)
        (xy 181.516249 37.80755)
        (xy 181.487873 37.8045)
        (xy 181.48787 37.8045)
        (xy 180.892133 37.8045)
        (xy 180.892129 37.8045)
        (xy 180.892128 37.804501)
        (xy 180.884949 37.805272)
        (xy 180.832519 37.810908)
        (xy 180.832514 37.810909)
        (xy 180.69767 37.861202)
        (xy 180.582458 37.94745)
        (xy 180.58245 37.947458)
        (xy 180.496202 38.06267)
        (xy 180.44591 38.197511)
        (xy 180.445908 38.197522)
        (xy 180.4395 38.257129)
        (xy 180.4395 38.718113)
        (xy 180.438455 38.732458)
        (xy 180.434711 38.758022)
        (xy 180.434711 38.758028)
        (xy 177.340565 38.758028)
        (xy 177.333868 38.629999)
        (xy 178.49 38.629999)
        (xy 178.490001 38.63)
        (xy 179.139999 38.63)
        (xy 179.14 38.629999)
        (xy 179.44 38.629999)
        (xy 179.440001 38.63)
        (xy 180.089999 38.63)
        (xy 180.09 38.629999)
        (xy 180.09 38.261941)
        (xy 180.089999 38.261935)
        (xy 180.079386 38.173556)
        (xy 180.023919 38.032904)
        (xy 179.932564 37.912435)
        (xy 179.812095 37.82108)
        (xy 179.671443 37.765613)
        (xy 179.583064 37.755)
        (xy 179.440001 37.755)
        (xy 179.44 37.755001)
        (xy 179.44 38.629999)
        (xy 179.14 38.629999)
        (xy 179.14 37.755001)
        (xy 179.139999 37.755)
        (xy 178.996935 37.755)
        (xy 178.908556 37.765613)
        (xy 178.767904 37.82108)
        (xy 178.647435 37.912435)
        (xy 178.55608 38.032904)
        (xy 178.500613 38.173556)
        (xy 178.49 38.261935)
        (xy 178.49 38.629999)
        (xy 177.333868 38.629999)
        (xy 177.262978 37.274844)
        (xy 177.586175 37.296391)
        (xy 179.086175 37.346391)
        (xy 179.06605 37.054582)
        (xy 179.975 37.054582)
        (xy 179.985373 37.140958)
        (xy 180.039576 37.278408)
        (xy 180.039579 37.278412)
        (xy 180.128854 37.396139)
        (xy 180.12886 37.396145)
        (xy 180.246587 37.48542)
        (xy 180.246591 37.485423)
        (xy 180.384041 37.539626)
        (xy 180.470417 37.549999)
        (xy 180.470424 37.55)
        (xy 180.599999 37.55)
        (xy 180.6 37.549999)
        (xy 180.6 36.862501)
        (xy 180.599999 36.8625)
        (xy 179.975001 36.8625)
        (xy 179.975 36.862501)
        (xy 179.975 37.054582)
        (xy 179.06605 37.054582)
        (xy 179.015062 36.315265)
        (xy 179.103277 36.3564)
        (xy 179.153409 36.363)
        (xy 179.42159 36.362999)
        (xy 179.471723 36.3564)
        (xy 179.544905 36.322275)
        (xy 179.586744 36.313)
        (xy 179.876 36.313)
        (xy 179.934191 36.331907)
        (xy 179.970155 36.381407)
        (xy 179.975 36.412)
        (xy 179.975 36.562499)
        (xy 179.975001 36.5625)
        (xy 180.801 36.5625)
        (xy 180.859191 36.581407)
        (xy 180.895155 36.630907)
        (xy 180.9 36.6615)
        (xy 180.9 37.549999)
        (xy 180.900001 37.55)
        (xy 181.029576 37.55)
        (xy 181.029582 37.549999)
        (xy 181.115958 37.539626)
        (xy 181.253408 37.485423)
        (xy 181.253412 37.48542)
        (xy 181.371139 37.396145)
        (xy 181.371145 37.396139)
        (xy 181.46042 37.278412)
        (xy 181.460423 37.278408)
        (xy 181.514626 37.140958)
        (xy 181.525353 37.05164)
        (xy 181.526983 37.051835)
        (xy 181.547059 37.000341)
        (xy 181.598546 36.967286)
        (xy 181.65963 36.970807)
        (xy 181.693673 36.993041)
        (xy 181.917556 37.216923)
        (xy 181.926963 37.227809)
        (xy 181.942385 37.248525)
        (xy 181.942389 37.248529)
        (xy 181.94239 37.24853)
        (xy 181.981177 37.281076)
        (xy 181.987523 37.28689)
        (xy 181.992223 37.29159)
        (xy 182.016819 37.311037)
        (xy 182.019034 37.312842)
        (xy 182.076786 37.361302)
        (xy 182.077617 37.361719)
        (xy 182.094594 37.372534)
        (xy 182.095323 37.373111)
        (xy 182.163649 37.404971)
        (xy 182.16622 37.406217)
        (xy 182.233567 37.44004)
        (xy 182.234465 37.440252)
        (xy 182.253483 37.446862)
        (xy 182.254327 37.447256)
        (xy 182.254332 37.447257)
        (xy 182.328132 37.462495)
        (xy 182.330937 37.463117)
        (xy 182.404279 37.4805)
        (xy 182.405214 37.4805)
        (xy 182.425232 37.482545)
        (xy 182.425444 37.482588)
        (xy 182.426144 37.482733)
        (xy 182.481573 37.48112)
        (xy 182.482722 37.481087)
        (xy 182.541438 37.498294)
        (xy 182.564853 37.520714)
        (xy 182.577454 37.537546)
        (xy 182.577457 37.537548)
        (xy 182.577458 37.537549)
        (xy 182.69267 37.623797)
        (xy 182.827511 37.674089)
        (xy 182.827512 37.674089)
        (xy 182.827517 37.674091)
        (xy 182.887127 37.6805)
        (xy 183.507872 37.680499)
        (xy 183.567483 37.674091)
        (xy 183.658189 37.64026)
        (xy 183.702329 37.623797)
        (xy 183.702329 37.623796)
        (xy 183.702331 37.623796)
        (xy 183.817546 37.537546)
        (xy 183.903796 37.422331)
        (xy 183.905559 37.417606)
        (xy 183.944974 37.311927)
        (xy 183.954091 37.287483)
        (xy 183.9605 37.227873)
        (xy 183.960499 36.232128)
        (xy 183.954091 36.172517)
        (xy 183.954089 36.172511)
        (xy 183.903797 36.03767)
        (xy 183.817549 35.922458)
        (xy 183.817548 35.922457)
        (xy 183.817546 35.922454)
        (xy 183.817541 35.92245)
        (xy 183.812539 35.917448)
        (xy 183.814681 35.915305)
        (xy 183.786899 35.875894)
        (xy 183.787773 35.814715)
        (xy 183.821214 35.768107)
        (xy 183.824077 35.76591)
        (xy 183.932621 35.682621)
        (xy 184.020861 35.567625)
        (xy 184.07633 35.433709)
        (xy 184.09525 35.29)
        (xy 184.088701 35.24026)
        (xy 184.07633 35.146291)
        (xy 184.020861 35.012375)
        (xy 183.932621 34.897379)
        (xy 183.817625 34.809139)
        (xy 183.801334 34.802391)
        (xy 183.771328 34.789962)
        (xy 183.724802 34.750225)
        (xy 183.710519 34.69073)
        (xy 183.72996 34.639171)
        (xy 183.753796 34.607331)
        (xy 183.804091 34.472483)
        (xy 183.8105 34.412873)
        (xy 183.8105 34.408064)
        (xy 184.16 34.408064)
        (xy 184.170613 34.496443)
        (xy 184.22608 34.637095)
        (xy 184.317435 34.757564)
        (xy 184.437904 34.848919)
        (xy 184.578556 34.904386)
        (xy 184.666935 34.914999)
        (xy 184.666942 34.915)
        (xy 184.809999 34.915)
        (xy 184.81 34.914999)
        (xy 184.81 34.040001)
        (xy 184.809999 34.04)
        (xy 184.160001 34.04)
        (xy 184.16 34.040001)
        (xy 184.16 34.408064)
        (xy 183.8105 34.408064)
        (xy 183.810499 34.050954)
        (xy 183.829406 33.992764)
        (xy 183.839496 33.980951)
        (xy 184.051452 33.768996)
        (xy 184.105968 33.741219)
        (xy 184.121455 33.74)
        (xy 185.011 33.74)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 180.348785 33.866888)
        (xy 180.360598 33.876977)
        (xy 180.41947 33.935849)
        (xy 180.447247 33.990366)
        (xy 180.448466 34.005853)
        (xy 180.448466 34.121807)
        (xy 180.429559 34.179998)
        (xy 180.384063 34.214565)
        (xy 180.276222 34.254787)
        (xy 180.163888 34.33888)
        (xy 180.16388 34.338888)
        (xy 180.079786 34.451223)
        (xy 180.03075 34.582695)
        (xy 180.030749 34.582701)
        (xy 180.024577 34.640115)
        (xy 180.0245 34.640829)
        (xy 180.0245 35.105599)
        (xy 180.005593 35.16379)
        (xy 179.995504 35.175603)
        (xy 179.688103 35.483004)
        (xy 179.633586 35.510781)
        (xy 179.618099 35.512)
        (xy 179.586744 35.512)
        (xy 179.544905 35.502724)
        (xy 179.47173 35.468602)
        (xy 179.471726 35.4686)
        (xy 179.441643 35.46464)
        (xy 179.421591 35.462)
        (xy 179.421589 35.462)
        (xy 179.153414 35.462)
        (xy 179.153402 35.462001)
        (xy 179.103279 35.468599)
        (xy 178.996981 35.518166)
        (xy 179.011398 35.013556)
        (xy 179.103277 35.0564)
        (xy 179.153409 35.063)
        (xy 179.42159 35.062999)
        (xy 179.471723 35.0564)
        (xy 179.58175 35.005094)
        (xy 179.667594 34.91925)
        (xy 179.7189 34.809223)
        (xy 179.7255 34.759091)
        (xy 179.725499 34.46591)
        (xy 179.7189 34.415777)
        (xy 179.709917 34.396514)
        (xy 179.702461 34.335786)
        (xy 179.729639 34.284671)
        (xy 180.137334 33.876977)
        (xy 180.19185 33.8492)
        (xy 180.207337 33.847981)
        (xy 180.290594 33.847981)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 181.118691 22.039407)
        (xy 181.154655 22.088907)
        (xy 181.1595 22.1195)
        (xy 181.1595 29.788115)
        (xy 181.158455 29.802461)
        (xy 181.154711 29.828023)
        (xy 181.154711 29.828027)
        (xy 181.159123 29.878454)
        (xy 181.1595 29.887083)
        (xy 181.1595 30.359672)
        (xy 181.159501 30.359684)
        (xy 181.174033 30.432736)
        (xy 181.174035 30.432742)
        (xy 181.229397 30.515599)
        (xy 181.229399 30.515601)
        (xy 181.31226 30.570966)
        (xy 181.367808 30.582015)
        (xy 181.385315 30.585498)
        (xy 181.38532 30.585498)
        (xy 181.385326 30.5855)
        (xy 181.543124 30.5855)
        (xy 181.601315 30.604407)
        (xy 181.613128 30.614496)
        (xy 182.960223 31.961591)
        (xy 183.063323 32.043111)
        (xy 183.222327 32.117257)
        (xy 183.394144 32.152734)
        (xy 183.569512 32.147631)
        (xy 183.707689 32.110606)
        (xy 183.768788 32.113808)
        (xy 183.816338 32.152313)
        (xy 183.832174 32.211414)
        (xy 183.810248 32.268535)
        (xy 183.803314 32.276237)
        (xy 183.194046 32.885504)
        (xy 183.139529 32.913281)
        (xy 183.124042 32.9145)
        (xy 182.762133 32.9145)
        (xy 182.762129 32.9145)
        (xy 182.762128 32.914501)
        (xy 182.754949 32.915272)
        (xy 182.702519 32.920908)
        (xy 182.702514 32.920909)
        (xy 182.56767 32.971202)
        (xy 182.452458 33.05745)
        (xy 182.45245 33.057458)
        (xy 182.366202 33.17267)
        (xy 182.31591 33.307511)
        (xy 182.315908 33.307522)
        (xy 182.3095 33.367129)
        (xy 182.3095 34.033098)
        (xy 182.290593 34.091289)
        (xy 182.280504 34.103102)
        (xy 181.644504 34.739102)
        (xy 181.589987 34.766879)
        (xy 181.529555 34.757308)
        (xy 181.48629 34.714043)
        (xy 181.4755 34.669098)
        (xy 181.4755 34.640829)
        (xy 181.4755 34.640826)
        (xy 181.469251 34.582701)
        (xy 181.425692 34.465914)
        (xy 181.420213 34.451223)
        (xy 181.336119 34.338888)
        (xy 181.336118 34.338887)
        (xy 181.336116 34.338884)
        (xy 181.289137 34.303715)
        (xy 181.253884 34.253706)
        (xy 181.249466 34.224462)
        (xy 181.249466 33.735521)
        (xy 181.249466 33.735519)
        (xy 181.242008 33.712565)
        (xy 181.238384 33.697466)
        (xy 181.234612 33.673651)
        (xy 181.234612 33.673648)
        (xy 181.23461 33.673645)
        (xy 181.23461 33.673643)
        (xy 181.22366 33.652153)
        (xy 181.217715 33.637803)
        (xy 181.210262 33.614862)
        (xy 181.196081 33.595343)
        (xy 181.187964 33.582097)
        (xy 181.177016 33.56061)
        (xy 181.177014 33.560608)
        (xy 181.154452 33.538045)
        (xy 181.154452 33.538046)
        (xy 181.15445 33.538043)
        (xy 180.758404 33.141997)
        (xy 180.7584 33.141994)
        (xy 180.755918 33.139512)
        (xy 180.755918 33.139511)
        (xy 180.735838 33.119432)
        (xy 180.735837 33.119431)
        (xy 180.714342 33.108478)
        (xy 180.701103 33.100365)
        (xy 180.695076 33.095986)
        (xy 180.681585 33.086185)
        (xy 180.681583 33.086184)
        (xy 180.658637 33.078728)
        (xy 180.644283 33.072782)
        (xy 180.622801 33.061835)
        (xy 180.598969 33.05806)
        (xy 180.583869 33.054434)
        (xy 180.560932 33.046981)
        (xy 180.560928 33.046981)
        (xy 180.529014 33.046981)
        (xy 180.06387 33.046981)
        (xy 179.937004 33.046981)
        (xy 179.937002 33.046981)
        (xy 179.936999 33.046982)
        (xy 179.914061 33.054434)
        (xy 179.898966 33.058058)
        (xy 179.875138 33.061833)
        (xy 179.875128 33.061836)
        (xy 179.853639 33.072786)
        (xy 179.83929 33.078729)
        (xy 179.832969 33.080783)
        (xy 179.816348 33.086184)
        (xy 179.79683 33.100365)
        (xy 179.783587 33.10848)
        (xy 179.762095 33.119431)
        (xy 179.762093 33.119432)
        (xy 179.739528 33.141996)
        (xy 179.739529 33.141997)
        (xy 179.739528 33.141998)
        (xy 179.036175 33.84535)
        (xy 179.036175 33.746391)
        (xy 177.84 33.846391)
        (xy 177.84 34.253156)
        (xy 177.80675 34.219906)
        (xy 177.696723 34.1686)
        (xy 177.646591 34.162)
        (xy 177.646589 34.162)
        (xy 177.378414 34.162)
        (xy 177.378402 34.162001)
        (xy 177.328279 34.168599)
        (xy 177.328277 34.1686)
        (xy 177.25801 34.201366)
        (xy 177.238945 34.210256)
        (xy 177.178216 34.217712)
        (xy 177.124702 34.188049)
        (xy 177.098844 34.132596)
        (xy 177.098244 34.125737)
        (xy 177.02 32.63)
        (xy 177.019999 32.629999)
        (xy 171.591299 30.778199)
        (xy 171.542329 30.741517)
        (xy 171.524272 30.683057)
        (xy 171.544025 30.625148)
        (xy 171.594044 30.589909)
        (xy 171.623261 30.5855)
        (xy 172.274673 30.5855)
        (xy 172.274674 30.5855)
        (xy 172.34774 30.570966)
        (xy 172.430601 30.515601)
        (xy 172.485966 30.43274)
        (xy 172.5005 30.359674)
        (xy 172.5005 28.908676)
        (xy 172.503169 28.885844)
        (xy 172.5205 28.812721)
        (xy 172.5205 23.631295)
        (xy 172.5205 23.631291)
        (xy 172.505241 23.500745)
        (xy 172.505239 23.50074)
        (xy 172.505239 23.500738)
        (xy 172.503911 23.495132)
        (xy 172.504844 23.49491)
        (xy 172.5005 23.470262)
        (xy 172.5005 23.226876)
        (xy 172.519407 23.168685)
        (xy 172.529496 23.156872)
        (xy 173.636872 22.049496)
        (xy 173.691389 22.021719)
        (xy 173.706876 22.0205)
        (xy 181.0605 22.0205)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 183.969799 22.039407)
        (xy 184.004367 22.084906)
        (xy 184.006204 22.089832)
        (xy 184.053015 22.152363)
        (xy 184.092454 22.205046)
        (xy 184.092457 22.205048)
        (xy 184.092458 22.205049)
        (xy 184.20767 22.291297)
        (xy 184.342511 22.341589)
        (xy 184.342512 22.341589)
        (xy 184.342517 22.341591)
        (xy 184.402127 22.348)
        (xy 185.397872 22.347999)
        (xy 185.457483 22.341591)
        (xy 185.524907 22.316443)
        (xy 185.592329 22.291297)
        (xy 185.592329 22.291296)
        (xy 185.592331 22.291296)
        (xy 185.707546 22.205046)
        (xy 185.721247 22.186742)
        (xy 185.771254 22.151491)
        (xy 185.832433 22.152363)
        (xy 185.881415 22.18903)
        (xy 185.8995 22.246072)
        (xy 185.8995 22.748927)
        (xy 185.880593 22.807118)
        (xy 185.831093 22.843082)
        (xy 185.769907 22.843082)
        (xy 185.721247 22.808256)
        (xy 185.707549 22.789958)
        (xy 185.707548 22.789957)
        (xy 185.707546 22.789954)
        (xy 185.705215 22.788209)
        (xy 185.592329 22.703702)
        (xy 185.457488 22.65341)
        (xy 185.457483 22.653409)
        (xy 185.457481 22.653408)
        (xy 185.457477 22.653408)
        (xy 185.426249 22.65005)
        (xy 185.397873 22.647)
        (xy 185.39787 22.647)
        (xy 184.402133 22.647)
        (xy 184.402129 22.647)
        (xy 184.402128 22.647001)
        (xy 184.394949 22.647772)
        (xy 184.342519 22.653408)
        (xy 184.342514 22.653409)
        (xy 184.20767 22.703702)
        (xy 184.092458 22.78995)
        (xy 184.09245 22.789958)
        (xy 184.006202 22.90517)
        (xy 183.95591 23.040011)
        (xy 183.955908 23.040022)
        (xy 183.9495 23.099629)
        (xy 183.9495 23.720366)
        (xy 183.949501 23.72037)
        (xy 183.955908 23.77998)
        (xy 183.955909 23.779985)
        (xy 184.006202 23.914829)
        (xy 184.052363 23.976491)
        (xy 184.092454 24.030046)
        (xy 184.092455 24.030047)
        (xy 184.092454 24.030047)
        (xy 184.099825 24.035564)
        (xy 184.135081 24.085571)
        (xy 184.1395 24.11482)
        (xy 184.1395 25.083709)
        (xy 184.154759 25.214255)
        (xy 184.154759 25.214257)
        (xy 184.15476 25.214258)
        (xy 184.214762 25.379116)
        (xy 184.311168 25.525693)
        (xy 184.31117 25.525696)
        (xy 184.43878 25.64609)
        (xy 184.43878 25.646091)
        (xy 184.438782 25.646092)
        (xy 184.590719 25.733812)
        (xy 184.731062 25.775828)
        (xy 184.758786 25.784129)
        (xy 184.758787 25.784129)
        (xy 184.75879 25.78413)
        (xy 184.833852 25.788501)
        (xy 184.933931 25.794331)
        (xy 184.933931 25.79433)
        (xy 184.933935 25.794331)
        (xy 185.055795 25.772843)
        (xy 185.106707 25.763866)
        (xy 185.106708 25.763865)
        (xy 185.106711 25.763865)
        (xy 185.267804 25.694377)
        (xy 185.40853 25.58961)
        (xy 185.521302 25.455214)
        (xy 185.60004 25.298433)
        (xy 185.600041 25.298425)
        (xy 185.600043 25.298423)
        (xy 185.6405 25.127722)
        (xy 185.6405 24.129791)
        (xy 185.659407 24.0716)
        (xy 185.680173 24.050537)
        (xy 185.707546 24.030046)
        (xy 185.721247 24.011742)
        (xy 185.771254 23.976491)
        (xy 185.832433 23.977363)
        (xy 185.881415 24.01403)
        (xy 185.8995 24.071072)
        (xy 185.8995 26.508792)
        (xy 185.899498 26.509472)
        (xy 185.854512 33.060965)
        (xy 185.835205 33.119024)
        (xy 185.785459 33.154647)
        (xy 185.724276 33.154227)
        (xy 185.67663 33.120105)
        (xy 185.602564 33.022435)
        (xy 185.482095 32.93108)
        (xy 185.341443 32.875613)
        (xy 185.253064 32.865)
        (xy 185.194455 32.865)
        (xy 185.136264 32.846093)
        (xy 185.1003 32.796593)
        (xy 185.1003 32.735407)
        (xy 185.124451 32.695997)
        (xy 185.269443 32.551004)
        (xy 185.280668 32.542012)
        (xy 185.280651 32.541992)
        (xy 185.285446 32.538023)
        (xy 185.285453 32.53802)
        (xy 185.331037 32.489476)
        (xy 185.33314 32.487306)
        (xy 185.352606 32.467842)
        (xy 185.355693 32.463861)
        (xy 185.361746 32.456775)
        (xy 185.391908 32.424657)
        (xy 185.402279 32.405789)
        (xy 185.41081 32.392804)
        (xy 185.423995 32.375807)
        (xy 185.423997 32.375805)
        (xy 185.441503 32.335349)
        (xy 185.445591 32.327006)
        (xy 185.466826 32.288382)
        (xy 185.472178 32.267537)
        (xy 185.477212 32.252833)
        (xy 185.485759 32.233084)
        (xy 185.492653 32.189548)
        (xy 185.494542 32.180434)
        (xy 185.505499 32.137762)
        (xy 185.5055 32.137754)
        (xy 185.5055 32.116232)
        (xy 185.506719 32.100743)
        (xy 185.510086 32.079486)
        (xy 185.505939 32.035614)
        (xy 185.5055 32.026298)
        (xy 185.5055 27.101275)
        (xy 185.490902 26.985719)
        (xy 185.4909 26.985713)
        (xy 185.461563 26.911616)
        (xy 185.433654 26.841124)
        (xy 185.385176 26.7744)
        (xy 185.342246 26.715311)
        (xy 185.222422 26.616185)
        (xy 185.081713 26.549972)
        (xy 185.081712 26.549971)
        (xy 184.92896 26.520832)
        (xy 184.928956 26.520832)
        (xy 184.928955 26.520832)
        (xy 184.917785 26.521534)
        (xy 184.773753 26.530596)
        (xy 184.625849 26.578653)
        (xy 184.494544 26.661981)
        (xy 184.388091 26.775342)
        (xy 184.313174 26.911616)
        (xy 184.2745 27.062241)
        (xy 184.2745 30.941834)
        (xy 184.255593 31.000025)
        (xy 184.206093 31.035989)
        (xy 184.144907 31.035989)
        (xy 184.095407 31.000025)
        (xy 184.091239 30.993806)
        (xy 184.052714 30.931347)
        (xy 184.052713 30.931346)
        (xy 184.052712 30.931344)
        (xy 182.689496 29.568128)
        (xy 182.661719 29.513611)
        (xy 182.6605 29.498124)
        (xy 182.6605 22.1195)
        (xy 182.679407 22.061309)
        (xy 182.728907 22.025345)
        (xy 182.7595 22.0205)
        (xy 183.911608 22.0205)
      )
    )
  )
  (zone (net 1) (net_name "GND") (layer "F.Cu") (tstamp 4fb54c0a-c599-4c01-a294-8e97552cccb3) (name "XTAL GND") (hatch edge 0.5)
    (priority 8)
    (connect_pads yes (clearance 0.15))
    (min_thickness 0.15) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.3) (thermal_bridge_width 0.3) (smoothing chamfer))
    (polygon
      (pts
        (xy 167.475622 48.616425)
        (xy 167.625622 48.516425)
        (xy 166.775622 48.466425)
        (xy 163.375622 48.716425)
        (xy 162.575622 49.316425)
        (xy 160.575622 51.566425)
        (xy 160.675622 54.816425)
        (xy 161.175622 55.166425)
        (xy 169.725622 55.166425)
        (xy 171.375622 52.516425)
        (xy 172.425622 51.266425)
        (xy 171.175622 51.166425)
        (xy 170.625622 51.566425)
        (xy 168.925622 51.366425)
        (xy 168.225622 50.966425)
        (xy 167.375622 48.616425)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 167.254377 48.494587)
        (xy 167.300843 48.514662)
        (xy 167.323535 48.55991)
        (xy 167.311833 48.609157)
        (xy 167.302356 48.620784)
        (xy 166.112447 49.810694)
        (xy 166.066571 49.832086)
        (xy 166.017676 49.818985)
        (xy 165.988642 49.777521)
        (xy 165.986121 49.758368)
        (xy 165.986121 49.503052)
        (xy 165.983304 49.478765)
        (xy 165.983304 49.478763)
        (xy 165.939435 49.379409)
        (xy 165.862638 49.302612)
        (xy 165.763284 49.258743)
        (xy 165.763285 49.258743)
        (xy 165.758426 49.258179)
        (xy 165.738995 49.255925)
        (xy 165.738991 49.255925)
        (xy 165.372249 49.255925)
        (xy 165.347962 49.258742)
        (xy 165.248605 49.302612)
        (xy 165.171809 49.379408)
        (xy 165.12794 49.478762)
        (xy 165.125122 49.503055)
        (xy 165.125122 49.929797)
        (xy 165.127939 49.954084)
        (xy 165.127939 49.954086)
        (xy 165.12794 49.954087)
        (xy 165.171809 50.053441)
        (xy 165.248606 50.130238)
        (xy 165.34796 50.174107)
        (xy 165.372249 50.176925)
        (xy 165.567565 50.176924)
        (xy 165.61513 50.194236)
        (xy 165.64044 50.238074)
        (xy 165.63165 50.287924)
        (xy 165.61989 50.30325)
        (xy 165.146722 50.776418)
        (xy 165.143914 50.779083)
        (xy 165.114114 50.805916)
        (xy 165.114111 50.80592)
        (xy 165.104202 50.828174)
        (xy 165.098665 50.838371)
        (xy 165.085396 50.858804)
        (xy 165.084065 50.867208)
        (xy 165.078581 50.885721)
        (xy 165.075123 50.893488)
        (xy 165.075122 50.893494)
        (xy 165.075122 50.91785)
        (xy 165.074211 50.929426)
        (xy 165.070399 50.953488)
        (xy 165.0726 50.961699)
        (xy 165.075122 50.980853)
        (xy 165.075122 52.467368)
        (xy 165.057809 52.514934)
        (xy 165.053448 52.519694)
        (xy 164.728891 52.844251)
        (xy 164.683015 52.865643)
        (xy 164.676565 52.865925)
        (xy 163.560802 52.865925)
        (xy 163.538851 52.870291)
        (xy 163.516899 52.874658)
        (xy 163.467118 52.90792)
        (xy 163.467117 52.907921)
        (xy 163.433855 52.957702)
        (xy 163.433855 52.957703)
        (xy 163.425122 53.001605)
        (xy 163.425122 54.231245)
        (xy 163.433855 54.275147)
        (xy 163.467118 54.324929)
        (xy 163.5169 54.358192)
        (xy 163.560802 54.366925)
        (xy 163.560803 54.366925)
        (xy 164.990441 54.366925)
        (xy 164.990442 54.366925)
        (xy 165.034344 54.358192)
        (xy 165.084126 54.324929)
        (xy 165.117389 54.275147)
        (xy 165.126122 54.231245)
        (xy 165.126122 53.115481)
        (xy 165.143435 53.067915)
        (xy 165.147785 53.063166)
        (xy 165.454537 52.756413)
        (xy 165.457309 52.753782)
        (xy 165.487131 52.726932)
        (xy 165.49704 52.704672)
        (xy 165.502577 52.694475)
        (xy 165.515848 52.674043)
        (xy 165.517179 52.665636)
        (xy 165.522664 52.647123)
        (xy 165.526122 52.639357)
        (xy 165.526122 52.639356)
        (xy 165.529288 52.632247)
        (xy 165.530458 52.632768)
        (xy 165.552826 52.596964)
        (xy 165.600966 52.581316)
        (xy 165.647901 52.600272)
        (xy 165.65992 52.614157)
        (xy 165.667117 52.624928)
        (xy 165.667118 52.624929)
        (xy 165.7169 52.658192)
        (xy 165.760802 52.666925)
        (xy 165.760803 52.666925)
        (xy 167.190441 52.666925)
        (xy 167.190442 52.666925)
        (xy 167.234344 52.658192)
        (xy 167.284126 52.624929)
        (xy 167.317389 52.575147)
        (xy 167.326122 52.531245)
        (xy 167.326122 52.215925)
        (xy 167.343435 52.168359)
        (xy 167.387272 52.143049)
        (xy 167.400122 52.141925)
        (xy 167.646122 52.141925)
        (xy 167.693688 52.159238)
        (xy 167.718998 52.203075)
        (xy 167.720122 52.215925)
        (xy 167.720122 52.224689)
        (xy 167.730049 52.292818)
        (xy 167.730049 52.292819)
        (xy 167.781423 52.397907)
        (xy 167.864141 52.480625)
        (xy 167.869119 52.484179)
        (xy 167.897773 52.525907)
        (xy 167.900122 52.544405)
        (xy 167.900122 52.705154)
        (xy 167.882809 52.75272)
        (xy 167.856013 52.772849)
        (xy 167.788604 52.802613)
        (xy 167.711809 52.879408)
        (xy 167.66794 52.978762)
        (xy 167.665122 53.003055)
        (xy 167.665122 53.369797)
        (xy 167.667939 53.394084)
        (xy 167.667939 53.394086)
        (xy 167.66794 53.394087)
        (xy 167.711809 53.493441)
        (xy 167.788606 53.570238)
        (xy 167.88796 53.614107)
        (xy 167.912249 53.616925)
        (xy 168.338994 53.616924)
        (xy 168.363284 53.614107)
        (xy 168.462638 53.570238)
        (xy 168.539435 53.493441)
        (xy 168.583304 53.394087)
        (xy 168.586122 53.369798)
        (xy 168.586121 53.003053)
        (xy 168.583304 52.978763)
        (xy 168.539435 52.879409)
        (xy 168.462638 52.802612)
        (xy 168.39523 52.772848)
        (xy 168.358711 52.737798)
        (xy 168.351122 52.705154)
        (xy 168.351122 52.615925)
        (xy 168.368435 52.568359)
        (xy 168.412272 52.543049)
        (xy 168.425122 52.541925)
        (xy 168.503876 52.541925)
        (xy 168.503882 52.541925)
        (xy 168.572015 52.531998)
        (xy 168.677105 52.480623)
        (xy 168.75982 52.397908)
        (xy 168.811195 52.292818)
        (xy 168.821122 52.224685)
        (xy 168.821122 51.998332)
        (xy 168.824525 51.98123)
        (xy 168.823715 51.981069)
        (xy 168.825137 51.97392)
        (xy 168.846519 51.866425)
        (xy 168.825137 51.75893)
        (xy 168.825136 51.758929)
        (xy 168.823715 51.751782)
        (xy 168.824523 51.751621)
        (xy 168.821122 51.734517)
        (xy 168.821122 51.659482)
        (xy 168.838435 51.611916)
        (xy 168.882272 51.586606)
        (xy 168.932122 51.595396)
        (xy 168.947448 51.607156)
        (xy 169.348448 52.008156)
        (xy 169.36984 52.054032)
        (xy 169.370122 52.060482)
        (xy 169.370122 52.224689)
        (xy 169.380049 52.292818)
        (xy 169.380049 52.292819)
        (xy 169.431423 52.397907)
        (xy 169.431424 52.397908)
        (xy 169.514139 52.480623)
        (xy 169.619229 52.531998)
        (xy 169.687362 52.541925)
        (xy 169.687368 52.541925)
        (xy 170.153876 52.541925)
        (xy 170.153882 52.541925)
        (xy 170.222015 52.531998)
        (xy 170.327105 52.480623)
        (xy 170.40982 52.397908)
        (xy 170.461195 52.292818)
        (xy 170.471122 52.224685)
        (xy 170.471122 51.631464)
        (xy 170.488435 51.583898)
        (xy 170.532272 51.558588)
        (xy 170.553764 51.557971)
        (xy 170.625622 51.566425)
        (xy 171.153485 51.182524)
        (xy 171.202135 51.168549)
        (xy 171.202719 51.168592)
        (xy 172.282536 51.254978)
        (xy 172.328568 51.276027)
        (xy 172.350301 51.321743)
        (xy 172.337564 51.370734)
        (xy 172.333295 51.376337)
        (xy 171.375622 52.516424)
        (xy 171.375615 52.516433)
        (xy 169.747344 55.131538)
        (xy 169.707506 55.162766)
        (xy 169.684526 55.166425)
        (xy 163.350122 55.166425)
        (xy 163.302556 55.149112)
        (xy 163.277246 55.105275)
        (xy 163.276122 55.092425)
        (xy 163.276122 48.82805)
        (xy 163.293435 48.780484)
        (xy 163.305721 48.768851)
        (xy 163.318859 48.758997)
        (xy 163.358274 48.729435)
        (xy 163.397243 48.714835)
        (xy 166.770754 48.466782)
        (xy 166.780502 48.466712)
      )
    )
  )
  (zone (net 1) (net_name "GND") (layer "F.Cu") (tstamp 95cf08cc-eb23-46dc-8d04-63eb56c3ef1c) (hatch edge 0.5)
    (priority 4)
    (connect_pads yes (clearance 0.15))
    (min_thickness 0.25) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.5) (thermal_bridge_width 0.5))
    (polygon
      (pts
        (xy 177.536175 35.596391)
        (xy 176.586175 35.546391)
        (xy 174.836175 35.696391)
        (xy 174.936175 37.146391)
        (xy 176.086175 37.196391)
        (xy 177.586175 37.296391)
        (xy 179.086175 37.346391)
        (xy 178.986175 35.896391)
        (xy 179.036175 34.146391)
        (xy 179.036175 33.746391)
        (xy 177.84 33.846391)
        (xy 177.84 34.96)
        (xy 177.736175 35.596391)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 179.036175 33.990059)
        (xy 179.027583 33.999484)
        (xy 179.027583 33.999485)
        (xy 179.025078 34.005951)
        (xy 179.011759 34.03122)
        (xy 179.007845 34.036933)
        (xy 179.007843 34.036938)
        (xy 179.001728 34.062935)
        (xy 178.996651 34.079331)
        (xy 178.987 34.104244)
        (xy 178.987 34.111169)
        (xy 178.983706 34.139559)
        (xy 178.982121 34.146295)
        (xy 178.982121 34.1463)
        (xy 178.985811 34.172751)
        (xy 178.987 34.189882)
        (xy 178.987 34.33842)
        (xy 178.967315 34.405459)
        (xy 178.966104 34.407308)
        (xy 178.963672 34.410946)
        (xy 178.96367 34.410952)
        (xy 178.9495 34.482191)
        (xy 178.9495 34.742801)
        (xy 178.963672 34.814052)
        (xy 179.014908 34.890732)
        (xy 178.992715 35.66748)
        (xy 178.963672 35.710948)
        (xy 178.96367 35.710952)
        (xy 178.9495 35.782191)
        (xy 178.9495 36.042801)
        (xy 178.963672 36.114052)
        (xy 179.005503 36.176658)
        (xy 179.086175 37.346391)
        (xy 177.586175 37.296391)
        (xy 177.262978 37.274844)
        (xy 177.1976 37.270485)
        (xy 176.086192 37.196391)
        (xy 175.046871 37.151203)
        (xy 174.98075 37.128625)
        (xy 174.937332 37.073883)
        (xy 174.928551 37.035854)
        (xy 174.844565 35.818052)
        (xy 174.85959 35.74982)
        (xy 174.909121 35.70054)
        (xy 174.95768 35.685976)
        (xy 176.577636 35.547122)
        (xy 176.594727 35.546841)
        (xy 177.299804 35.58395)
        (xy 177.305693 35.586964)
        (xy 177.323448 35.598828)
        (xy 177.32345 35.598828)
        (xy 177.323452 35.598829)
        (xy 177.359071 35.605914)
        (xy 177.394695 35.613)
        (xy 177.630304 35.612999)
        (xy 177.701552 35.598828)
        (xy 177.705199 35.596391)
        (xy 177.736175 35.596391)
        (xy 177.739963 35.573161)
        (xy 177.782344 35.544844)
        (xy 177.836328 35.464052)
        (xy 177.8505 35.392805)
        (xy 177.850499 35.132196)
        (xy 177.836328 35.060948)
        (xy 177.833896 35.057309)
        (xy 177.827472 35.036789)
        (xy 177.84 34.96)
        (xy 177.84 34.795593)
        (xy 177.850499 34.742808)
        (xy 177.8505 34.742805)
        (xy 177.850499 34.482196)
        (xy 177.84 34.429409)
        (xy 177.84 33.846391)
        (xy 179.036175 33.746391)
      )
    )
  )
  (zone (net 5) (net_name "+3V3") (layer "F.Cu") (tstamp 984dec00-9159-40c6-b6cc-4425a46b2919) (hatch edge 0.5)
    (connect_pads yes (clearance 0.25))
    (min_thickness 0.2) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.3) (thermal_bridge_width 0.3))
    (polygon
      (pts
        (xy 168.85 49.175)
        (xy 158.6 49.9)
        (xy 158.275 25.125)
        (xy 156.625117 25.059885)
        (xy 156.8 26.7)
        (xy 156.9 26.925)
        (xy 156.95 27.125)
        (xy 162.325 32.3)
        (xy 162.71 32.42)
        (xy 162.57 26.83)
        (xy 156.4 26.8)
        (xy 156.275 25.65)
        (xy 170.235 25.33)
        (xy 170.41 30.83)
        (xy 171.65 31.8)
        (xy 173.525 33.225)
        (xy 174.025 35.375)
        (xy 171.675 42.175)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 173.562803 33.47287)
        (xy 173.594773 33.525039)
        (xy 173.594936 33.525726)
        (xy 174.018536 35.347207)
        (xy 174.015679 35.401969)
        (xy 172.70592 39.19191)
        (xy 172.669043 39.240733)
        (xy 172.610511 39.258556)
        (xy 172.552681 39.238571)
        (xy 172.524141 39.204519)
        (xy 172.521477 39.199291)
        (xy 172.467062 39.124396)
        (xy 172.225605 38.88294)
        (xy 172.150706 38.828521)
        (xy 172.090762 38.809044)
        (xy 172.033062 38.790297)
        (xy 171.909364 38.790297)
        (xy 171.851663 38.809044)
        (xy 171.791721 38.828521)
        (xy 171.79172 38.828522)
        (xy 171.716821 38.882939)
        (xy 171.716819 38.88294)
        (xy 171.716816 38.882943)
        (xy 171.432938 39.166822)
        (xy 171.378523 39.241717)
        (xy 171.378522 39.241719)
        (xy 171.378522 39.24172)
        (xy 171.340297 39.359364)
        (xy 171.340297 39.483062)
        (xy 171.349332 39.510868)
        (xy 171.378522 39.600708)
        (xy 171.432937 39.675603)
        (xy 171.432938 39.675604)
        (xy 171.432939 39.675605)
        (xy 171.674395 39.91706)
        (xy 171.749293 39.971478)
        (xy 171.866938 40.009703)
        (xy 171.86694 40.009703)
        (xy 171.990634 40.009703)
        (xy 171.990636 40.009703)
        (xy 172.10828 39.971478)
        (xy 172.183179 39.917061)
        (xy 172.295031 39.805208)
        (xy 172.349547 39.777431)
        (xy 172.409979 39.787002)
        (xy 172.453243 39.830267)
        (xy 172.462815 39.890699)
        (xy 172.458604 39.907549)
        (xy 171.675822 42.172619)
        (xy 171.674058 42.177332)
        (xy 170.600537 44.837385)
        (xy 170.561226 44.884271)
        (xy 170.501864 44.899097)
        (xy 170.445125 44.876198)
        (xy 170.412746 44.824579)
        (xy 170.405432 44.795625)
        (xy 170.40543 44.795618)
        (xy 170.383699 44.749907)
        (xy 170.383698 44.749904)
        (xy 170.340029 44.68559)
        (xy 170.259197 44.627242)
        (xy 170.213165 44.606193)
        (xy 170.127289 44.583867)
        (xy 169.860259 44.562503)
        (xy 169.80789 44.542362)
        (xy 169.777625 44.519139)
        (xy 169.777621 44.519137)
        (xy 169.643709 44.46367)
        (xy 169.623059 44.460951)
        (xy 169.567835 44.434608)
        (xy 169.538641 44.380836)
        (xy 169.546629 44.320175)
        (xy 169.57115 44.287983)
        (xy 169.602143 44.261128)
        (xy 169.61016 44.248653)
        (xy 169.65556 44.210711)
        (xy 169.696606 44.193709)
        (xy 169.727625 44.180861)
        (xy 169.842621 44.092621)
        (xy 169.930861 43.977625)
        (xy 169.98633 43.843709)
        (xy 170.00525 43.7)
        (xy 169.98633 43.556291)
        (xy 169.930861 43.422375)
        (xy 169.842621 43.307379)
        (xy 169.727625 43.219139)
        (xy 169.727621 43.219137)
        (xy 169.593709 43.16367)
        (xy 169.593708 43.163669)
        (xy 169.45 43.14475)
        (xy 169.306291 43.163669)
        (xy 169.30629 43.16367)
        (xy 169.172378 43.219137)
        (xy 169.172369 43.219142)
        (xy 169.169758 43.221146)
        (xy 169.167537 43.221931)
        (xy 169.166753 43.222385)
        (xy 169.166668 43.222239)
        (xy 169.112079 43.241563)
        (xy 169.053416 43.224179)
        (xy 169.016174 43.175633)
        (xy 169.010499 43.142597)
        (xy 169.010499 43.140741)
        (xy 169.0105 43.140735)
        (xy 169.010499 42.799266)
        (xy 169.010499 42.799262)
        (xy 169.004882 42.763801)
        (xy 168.996017 42.707825)
        (xy 168.939859 42.597609)
        (xy 168.852391 42.510141)
        (xy 168.852388 42.510139)
        (xy 168.846881 42.504632)
        (xy 168.848643 42.502869)
        (xy 168.819682 42.463002)
        (xy 168.815215 42.423782)
        (xy 168.815442 42.421193)
        (xy 168.785732 42.310316)
        (xy 168.736476 42.239972)
        (xy 168.636167 42.139663)
        (xy 168.464923 41.968418)
        (xy 168.437146 41.913902)
        (xy 168.446717 41.85347)
        (xy 168.489982 41.810205)
        (xy 168.523522 41.800074)
        (xy 168.544991 41.797585)
        (xy 168.585011 41.779913)
        (xy 168.645879 41.773705)
        (xy 168.664989 41.779914)
        (xy 168.705009 41.797585)
        (xy 168.705008 41.797585)
        (xy 168.709478 41.798103)
        (xy 168.730135 41.8005)
        (xy 168.919864 41.800499)
        (xy 168.944991 41.797585)
        (xy 169.047765 41.752206)
        (xy 169.127206 41.672765)
        (xy 169.172585 41.569991)
        (xy 169.1755 41.544865)
        (xy 169.175499 40.724498)
        (xy 169.194406 40.666308)
        (xy 169.243906 40.630344)
        (xy 169.274493 40.625499)
        (xy 170.094864 40.625499)
        (xy 170.119991 40.622585)
        (xy 170.222765 40.577206)
        (xy 170.302206 40.497765)
        (xy 170.347585 40.394991)
        (xy 170.3505 40.369865)
        (xy 170.3505 40.2995)
        (xy 170.369407 40.241309)
        (xy 170.418907 40.205345)
        (xy 170.4495 40.2005)
        (xy 170.744878 40.2005)
        (xy 170.744879 40.2005)
        (xy 170.829449 40.185588)
        (xy 170.928859 40.128194)
        (xy 171.002644 40.040261)
        (xy 171.041904 39.932394)
        (xy 171.041904 39.817606)
        (xy 171.011896 39.735159)
        (xy 171.00976 39.674011)
        (xy 171.023451 39.648267)
        (xy 171.022577 39.647763)
        (xy 171.026905 39.640265)
        (xy 171.026905 39.640264)
        (xy 171.026908 39.640261)
        (xy 171.066168 39.532394)
        (xy 171.066168 39.417606)
        (xy 171.03616 39.335159)
        (xy 171.034024 39.274011)
        (xy 171.047715 39.248267)
        (xy 171.046841 39.247763)
        (xy 171.051169 39.240265)
        (xy 171.051169 39.240264)
        (xy 171.051172 39.240261)
        (xy 171.090432 39.132394)
        (xy 171.090432 39.017606)
        (xy 171.051172 38.90974)
        (xy 171.028683 38.882939)
        (xy 170.977387 38.821806)
        (xy 170.914047 38.785237)
        (xy 170.873105 38.739768)
        (xy 170.866709 38.678917)
        (xy 170.897302 38.625929)
        (xy 170.953197 38.601042)
        (xy 170.963546 38.6005)
        (xy 171.031652 38.6005)
        (xy 171.031653 38.6005)
        (xy 171.116223 38.585588)
        (xy 171.215633 38.528194)
        (xy 171.289418 38.440261)
        (xy 171.328678 38.332394)
        (xy 171.328678 38.217606)
        (xy 171.289418 38.10974)
        (xy 171.289415 38.109736)
        (xy 171.285088 38.102241)
        (xy 171.287057 38.101103)
        (xy 171.268676 38.055605)
        (xy 171.274406 38.014841)
        (xy 171.281631 37.99499)
        (xy 171.304414 37.932394)
        (xy 171.304414 37.8995)
        (xy 171.323321 37.841309)
        (xy 171.372821 37.805345)
        (xy 171.403414 37.8005)
        (xy 172.205682 37.8005)
        (xy 172.205683 37.8005)
        (xy 172.290253 37.785588)
        (xy 172.389663 37.728194)
        (xy 172.463448 37.640261)
        (xy 172.502708 37.532394)
        (xy 172.502708 37.417606)
        (xy 172.4727 37.335159)
        (xy 172.470564 37.274011)
        (xy 172.484255 37.248267)
        (xy 172.483381 37.247763)
        (xy 172.487709 37.240265)
        (xy 172.487709 37.240264)
        (xy 172.487712 37.240261)
        (xy 172.526972 37.132394)
        (xy 172.526972 37.017606)
        (xy 172.510872 36.973374)
        (xy 172.508737 36.91223)
        (xy 172.528065 36.875879)
        (xy 172.536976 36.865261)
        (xy 172.576236 36.757394)
        (xy 172.576236 36.702597)
        (xy 172.595143 36.644406)
        (xy 172.599398 36.638961)
        (xy 172.617317 36.617606)
        (xy 172.66124 36.565261)
        (xy 172.66124 36.56526)
        (xy 172.662203 36.564113)
        (xy 172.714091 36.53169)
        (xy 172.725119 36.529596)
        (xy 172.725734 36.529514)
        (xy 172.725735 36.529515)
        (xy 172.869444 36.510595)
        (xy 173.00336 36.455126)
        (xy 173.118356 36.366886)
        (xy 173.206596 36.25189)
        (xy 173.262065 36.117974)
        (xy 173.280985 35.974265)
        (xy 173.27729 35.946203)
        (xy 173.262065 35.830556)
        (xy 173.206596 35.69664)
        (xy 173.118356 35.581644)
        (xy 173.00336 35.493404)
        (xy 173.003356 35.493402)
        (xy 172.869444 35.437935)
        (xy 172.869443 35.437934)
        (xy 172.725735 35.419015)
        (xy 172.582026 35.437934)
        (xy 172.582026 35.437935)
        (xy 172.535769 35.457094)
        (xy 172.474772 35.461893)
        (xy 172.427884 35.435634)
        (xy 172.352391 35.360141)
        (xy 172.242175 35.303983)
        (xy 172.242176 35.303983)
        (xy 172.150736 35.2895)
        (xy 171.809262 35.2895)
        (xy 171.717825 35.303983)
        (xy 171.717821 35.303984)
        (xy 171.60761 35.36014)
        (xy 171.52014 35.44761)
        (xy 171.463983 35.557823)
        (xy 171.45099 35.639859)
        (xy 171.4495 35.649265)
        (xy 171.4495 35.839132)
        (xy 171.449501 35.9755)
        (xy 171.430594 36.03369)
        (xy 171.381094 36.069655)
        (xy 171.350501 36.0745)
        (xy 170.527835 36.0745)
        (xy 170.469644 36.055593)
        (xy 170.451997 36.039136)
        (xy 170.437456 36.021806)
        (xy 170.403795 36.002372)
        (xy 170.396516 35.997735)
        (xy 170.392709 35.995069)
        (xy 170.355891 35.946203)
        (xy 170.350499 35.913981)
        (xy 170.350499 35.780136)
        (xy 170.347585 35.755009)
        (xy 170.329914 35.714988)
        (xy 170.323705 35.654119)
        (xy 170.329911 35.635017)
        (xy 170.347585 35.594991)
        (xy 170.3505 35.569865)
        (xy 170.350499 35.380136)
        (xy 170.347585 35.355009)
        (xy 170.302206 35.252235)
        (xy 170.222765 35.172794)
        (xy 170.119991 35.127415)
        (xy 170.11999 35.127414)
        (xy 170.119988 35.127414)
        (xy 170.094868 35.1245)
        (xy 169.230139 35.1245)
        (xy 169.230136 35.124501)
        (xy 169.205009 35.127414)
        (xy 169.102235 35.172794)
        (xy 169.022794 35.252235)
        (xy 168.977414 35.355011)
        (xy 168.9745 35.38013)
        (xy 168.9745 35.569861)
        (xy 168.974501 35.569864)
        (xy 168.977415 35.594991)
        (xy 168.995086 35.635012)
        (xy 169.001294 35.695882)
        (xy 168.995086 35.714987)
        (xy 168.977415 35.755008)
        (xy 168.977414 35.755011)
        (xy 168.9745 35.78013)
        (xy 168.9745 35.969861)
        (xy 168.974501 35.969864)
        (xy 168.977415 35.994991)
        (xy 168.995086 36.035012)
        (xy 169.001294 36.095882)
        (xy 168.995086 36.114987)
        (xy 168.977415 36.155008)
        (xy 168.977414 36.155011)
        (xy 168.9745 36.18013)
        (xy 168.9745 36.369861)
        (xy 168.974501 36.369864)
        (xy 168.977415 36.394991)
        (xy 168.995086 36.435012)
        (xy 169.001294 36.495882)
        (xy 168.995086 36.514987)
        (xy 168.977415 36.555008)
        (xy 168.977414 36.555011)
        (xy 168.9745 36.58013)
        (xy 168.9745 36.769861)
        (xy 168.974501 36.769864)
        (xy 168.977415 36.794991)
        (xy 168.995086 36.835012)
        (xy 169.001294 36.895882)
        (xy 168.995086 36.914987)
        (xy 168.977415 36.955008)
        (xy 168.977414 36.955011)
        (xy 168.9745 36.98013)
        (xy 168.9745 37.169861)
        (xy 168.974501 37.169864)
        (xy 168.977415 37.194991)
        (xy 168.995086 37.235012)
        (xy 169.001294 37.295882)
        (xy 168.995086 37.314987)
        (xy 168.977415 37.355008)
        (xy 168.977414 37.355011)
        (xy 168.9745 37.38013)
        (xy 168.9745 37.569861)
        (xy 168.974501 37.569864)
        (xy 168.977415 37.594991)
        (xy 168.995086 37.635012)
        (xy 169.001294 37.695882)
        (xy 168.995086 37.714987)
        (xy 168.977415 37.755008)
        (xy 168.977414 37.755011)
        (xy 168.9745 37.78013)
        (xy 168.9745 37.969861)
        (xy 168.974501 37.969864)
        (xy 168.977415 37.994991)
        (xy 168.995086 38.035012)
        (xy 169.001294 38.095882)
        (xy 168.995086 38.114987)
        (xy 168.977415 38.155008)
        (xy 168.977414 38.155011)
        (xy 168.9745 38.18013)
        (xy 168.9745 38.369861)
        (xy 168.974501 38.369864)
        (xy 168.977414 38.39499)
        (xy 168.997404 38.440262)
        (xy 169.022794 38.497765)
        (xy 169.102235 38.577206)
        (xy 169.118609 38.584435)
        (xy 169.164202 38.625233)
        (xy 169.177109 38.685041)
        (xy 169.152397 38.741014)
        (xy 169.118612 38.765562)
        (xy 169.102236 38.772793)
        (xy 169.022794 38.852235)
        (xy 168.977414 38.955011)
        (xy 168.9745 38.98013)
        (xy 168.9745 39.169861)
        (xy 168.974501 39.169864)
        (xy 168.977415 39.194991)
        (xy 168.995086 39.235012)
        (xy 169.001294 39.295882)
        (xy 168.995086 39.314987)
        (xy 168.977415 39.355008)
        (xy 168.977414 39.355011)
        (xy 168.9745 39.38013)
        (xy 168.9745 39.569861)
        (xy 168.974501 39.569864)
        (xy 168.977415 39.594991)
        (xy 168.995086 39.635012)
        (xy 169.001294 39.695882)
        (xy 168.995086 39.714987)
        (xy 168.977415 39.755008)
        (xy 168.977414 39.755011)
        (xy 168.9745 39.78013)
        (xy 168.9745 39.969861)
        (xy 168.974501 39.969864)
        (xy 168.977415 39.994991)
        (xy 168.995086 40.035012)
        (xy 169.001294 40.095882)
        (xy 168.995086 40.114987)
        (xy 168.977415 40.155008)
        (xy 168.977414 40.155011)
        (xy 168.9745 40.18013)
        (xy 168.9745 40.3255)
        (xy 168.955593 40.383691)
        (xy 168.906093 40.419655)
        (xy 168.875501 40.4245)
        (xy 168.730138 40.4245)
        (xy 168.730135 40.424501)
        (xy 168.705007 40.427415)
        (xy 168.664986 40.445086)
        (xy 168.604116 40.451294)
        (xy 168.585016 40.445087)
        (xy 168.544991 40.427415)
        (xy 168.54499 40.427414)
        (xy 168.544988 40.427414)
        (xy 168.519868 40.4245)
        (xy 168.330138 40.4245)
        (xy 168.330135 40.424501)
        (xy 168.305007 40.427415)
        (xy 168.264986 40.445086)
        (xy 168.204116 40.451294)
        (xy 168.185016 40.445087)
        (xy 168.144991 40.427415)
        (xy 168.14499 40.427414)
        (xy 168.144988 40.427414)
        (xy 168.119868 40.4245)
        (xy 167.930138 40.4245)
        (xy 167.930135 40.424501)
        (xy 167.905007 40.427415)
        (xy 167.864986 40.445086)
        (xy 167.804116 40.451294)
        (xy 167.785016 40.445087)
        (xy 167.744991 40.427415)
        (xy 167.74499 40.427414)
        (xy 167.744988 40.427414)
        (xy 167.719868 40.4245)
        (xy 167.530138 40.4245)
        (xy 167.530135 40.424501)
        (xy 167.505007 40.427415)
        (xy 167.464986 40.445086)
        (xy 167.404116 40.451294)
        (xy 167.385016 40.445087)
        (xy 167.344991 40.427415)
        (xy 167.34499 40.427414)
        (xy 167.344988 40.427414)
        (xy 167.319869 40.4245)
        (xy 167.2245 40.4245)
        (xy 167.166309 40.405593)
        (xy 167.130345 40.356093)
        (xy 167.1255 40.3255)
        (xy 167.1255 40.292946)
        (xy 167.144407 40.234755)
        (xy 167.164233 40.214404)
        (xy 167.176039 40.205345)
        (xy 167.192621 40.192621)
        (xy 167.280861 40.077625)
        (xy 167.33633 39.943709)
        (xy 167.35525 39.8)
        (xy 167.33633 39.656291)
        (xy 167.336326 39.656281)
        (xy 167.334677 39.650126)
        (xy 167.337877 39.589024)
        (xy 167.376381 39.541473)
        (xy 167.430303 39.525499)
        (xy 167.712046 39.525499)
        (xy 167.731882 39.522357)
        (xy 167.804427 39.510868)
        (xy 167.915771 39.454135)
        (xy 168.004135 39.365771)
        (xy 168.060868 39.254427)
        (xy 168.0755 39.162045)
        (xy 168.075499 36.187956)
        (xy 168.07426 36.180135)
        (xy 168.068325 36.14266)
        (xy 168.060868 36.095573)
        (xy 168.004135 35.984229)
        (xy 167.915771 35.895865)
        (xy 167.804427 35.839132)
        (xy 167.804428 35.839132)
        (xy 167.757063 35.83163)
        (xy 167.702547 35.803852)
        (xy 167.67477 35.749335)
        (xy 167.684342 35.688903)
        (xy 167.702544 35.663848)
        (xy 168.330483 35.035909)
        (xy 168.35305 35.013342)
        (xy 168.364002 34.991843)
        (xy 168.372115 34.978605)
        (xy 168.386296 34.95909)
        (xy 168.393751 34.936144)
        (xy 168.399695 34.921795)
        (xy 168.410646 34.900304)
        (xy 168.410647 34.900295)
        (xy 168.413054 34.892892)
        (xy 168.416215 34.893919)
        (xy 168.437349 34.852394)
        (xy 168.491852 34.82459)
        (xy 168.552289 34.834133)
        (xy 168.577386 34.852357)
        (xy 168.602235 34.877206)
        (xy 168.705009 34.922585)
        (xy 168.730135 34.9255)
        (xy 168.919864 34.925499)
        (xy 168.944991 34.922585)
        (xy 169.047765 34.877206)
        (xy 169.127206 34.797765)
        (xy 169.171693 34.69701)
        (xy 169.212493 34.651416)
        (xy 169.262257 34.638)
        (xy 170.515712 34.638)
        (xy 170.573903 34.656907)
        (xy 170.603921 34.692054)
        (xy 170.626481 34.73633)
        (xy 170.660141 34.802391)
        (xy 170.747609 34.889859)
        (xy 170.857825 34.946017)
        (xy 170.949265 34.9605)
        (xy 171.290734 34.960499)
        (xy 171.290737 34.960499)
        (xy 171.317332 34.956286)
        (xy 171.382175 34.946017)
        (xy 171.492391 34.889859)
        (xy 171.529998 34.852251)
        (xy 171.584513 34.824476)
        (xy 171.644945 34.834047)
        (xy 171.67 34.85225)
        (xy 171.707609 34.889859)
        (xy 171.817825 34.946017)
        (xy 171.909265 34.9605)
        (xy 172.250734 34.960499)
        (xy 172.250737 34.960499)
        (xy 172.277332 34.956286)
        (xy 172.342175 34.946017)
        (xy 172.452391 34.889859)
        (xy 172.539859 34.802391)
        (xy 172.555111 34.772456)
        (xy 172.598373 34.729194)
        (xy 172.658804 34.719622)
        (xy 172.681199 34.725937)
        (xy 172.706291 34.73633)
        (xy 172.85 34.75525)
        (xy 172.993709 34.73633)
        (xy 173.127625 34.680861)
        (xy 173.242621 34.592621)
        (xy 173.330861 34.477625)
        (xy 173.38633 34.343709)
        (xy 173.40525 34.2)
        (xy 173.38633 34.056291)
        (xy 173.330861 33.922375)
        (xy 173.271698 33.845273)
        (xy 173.251275 33.787599)
        (xy 173.268652 33.728933)
        (xy 173.280247 33.714993)
        (xy 173.282612 33.712627)
        (xy 173.282621 33.712621)
        (xy 173.282798 33.712391)
        (xy 173.309774 33.677235)
        (xy 173.370861 33.597625)
        (xy 173.407046 33.510263)
        (xy 173.44678 33.46374)
        (xy 173.506275 33.449456)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 166.529593 41.970632)
        (xy 166.530596 41.972039)
        (xy 166.547732 41.996512)
        (xy 166.552372 42.003795)
        (xy 166.571806 42.037455)
        (xy 166.601569 42.062429)
        (xy 166.607938 42.068264)
        (xy 166.679337 42.139663)
        (xy 166.707114 42.19418)
        (xy 166.707486 42.196743)
        (xy 166.71367 42.24371)
        (xy 166.741259 42.310316)
        (xy 166.769139 42.377625)
        (xy 166.857379 42.492621)
        (xy 166.972375 42.580861)
        (xy 167.106291 42.63633)
        (xy 167.25 42.65525)
        (xy 167.285475 42.650579)
        (xy 167.345635 42.661728)
        (xy 167.368402 42.678728)
        (xy 167.513655 42.823981)
        (xy 167.541432 42.878498)
        (xy 167.531861 42.93893)
        (xy 167.503919 42.972526)
        (xy 167.401035 43.051473)
        (xy 167.401027 43.051481)
        (xy 167.312788 43.166475)
        (xy 167.284494 43.234783)
        (xy 167.263034 43.2669)
        (xy 167.23943 43.290504)
        (xy 167.184913 43.318281)
        (xy 167.169427 43.3195)
        (xy 166.999263 43.3195)
        (xy 166.907825 43.333983)
        (xy 166.907821 43.333984)
        (xy 166.79761 43.39014)
        (xy 166.71014 43.47761)
        (xy 166.653983 43.587823)
        (xy 166.6395 43.679263)
        (xy 166.6395 44.020734)
        (xy 166.640473 44.026873)
        (xy 166.630899 44.087305)
        (xy 166.587633 44.130568)
        (xy 166.527201 44.140138)
        (xy 166.472687 44.112361)
        (xy 166.379496 44.01917)
        (xy 166.351719 43.964653)
        (xy 166.3505 43.949166)
        (xy 166.3505 42.028823)
        (xy 166.369407 41.970632)
        (xy 166.418907 41.934668)
        (xy 166.480093 41.934668)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 161.279593 37.540234)
        (xy 161.292527 37.56456)
        (xy 161.32008 37.64026)
        (xy 161.320081 37.640261)
        (xy 161.320082 37.640263)
        (xy 161.320083 37.640264)
        (xy 161.393865 37.728194)
        (xy 161.493275 37.785588)
        (xy 161.531903 37.792399)
        (xy 161.585927 37.821123)
        (xy 161.612749 37.876116)
        (xy 161.613713 37.889895)
        (xy 161.613713 37.932394)
        (xy 161.652973 38.04026)
        (xy 161.652974 38.040261)
        (xy 161.652975 38.040263)
        (xy 161.711273 38.10974)
        (xy 161.726758 38.128194)
        (xy 161.826168 38.185588)
        (xy 161.910738 38.2005)
        (xy 162.000501 38.2005)
        (xy 162.058692 38.219407)
        (xy 162.094656 38.268907)
        (xy 162.099501 38.2995)
        (xy 162.099501 38.369864)
        (xy 162.102414 38.39499)
        (xy 162.122404 38.440262)
        (xy 162.147794 38.497765)
        (xy 162.227235 38.577206)
        (xy 162.243609 38.584435)
        (xy 162.289202 38.625233)
        (xy 162.302109 38.685041)
        (xy 162.277397 38.741014)
        (xy 162.243612 38.765562)
        (xy 162.227236 38.772793)
        (xy 162.147794 38.852235)
        (xy 162.102414 38.955011)
        (xy 162.0995 38.98013)
        (xy 162.0995 39.169861)
        (xy 162.099501 39.169864)
        (xy 162.102415 39.194991)
        (xy 162.120086 39.235012)
        (xy 162.126294 39.295882)
        (xy 162.120086 39.314987)
        (xy 162.102415 39.355008)
        (xy 162.102414 39.355011)
        (xy 162.0995 39.38013)
        (xy 162.0995 39.569861)
        (xy 162.099501 39.569864)
        (xy 162.102415 39.594991)
        (xy 162.120086 39.635012)
        (xy 162.126294 39.695882)
        (xy 162.120086 39.714987)
        (xy 162.102415 39.755008)
        (xy 162.102414 39.755011)
        (xy 162.0995 39.78013)
        (xy 162.0995 39.969861)
        (xy 162.099501 39.969864)
        (xy 162.102415 39.994991)
        (xy 162.120086 40.035012)
        (xy 162.126294 40.095882)
        (xy 162.120086 40.114987)
        (xy 162.102415 40.155008)
        (xy 162.102414 40.155011)
        (xy 162.0995 40.18013)
        (xy 162.0995 40.369861)
        (xy 162.099501 40.369864)
        (xy 162.102414 40.39499)
        (xy 162.124534 40.445086)
        (xy 162.147794 40.497765)
        (xy 162.227235 40.577206)
        (xy 162.330009 40.622585)
        (xy 162.355135 40.6255)
        (xy 163.1755 40.625499)
        (xy 163.233691 40.644406)
        (xy 163.269655 40.693906)
        (xy 163.2745 40.724499)
        (xy 163.2745 41.54486)
        (xy 163.274501 41.544863)
        (xy 163.277414 41.56999)
        (xy 163.2995 41.620008)
        (xy 163.322794 41.672765)
        (xy 163.370146 41.720117)
        (xy 163.397922 41.774632)
        (xy 163.388351 41.835064)
        (xy 163.345086 41.878329)
        (xy 163.307401 41.888852)
        (xy 161.20676 42.043312)
        (xy 161.147339 42.028723)
        (xy 161.107842 41.981994)
        (xy 161.1005 41.944579)
        (xy 161.1005 37.598425)
        (xy 161.119407 37.540234)
        (xy 161.168907 37.50427)
        (xy 161.230093 37.50427)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 170.19551 25.349817)
        (xy 170.232599 25.39848)
        (xy 170.23812 25.428074)
        (xy 170.409999 30.829999)
        (xy 170.41 30.83)
        (xy 171.125947 31.390055)
        (xy 171.650014 31.800011)
        (xy 171.777343 31.896781)
        (xy 172.737201 32.626273)
        (xy 172.772089 32.676534)
        (xy 172.77077 32.737706)
        (xy 172.733748 32.786419)
        (xy 172.715182 32.796555)
        (xy 172.612378 32.839137)
        (xy 172.612373 32.83914)
        (xy 172.590164 32.856182)
        (xy 172.532488 32.876606)
        (xy 172.473822 32.859228)
        (xy 172.459898 32.847648)
        (xy 172.432391 32.820141)
        (xy 172.322175 32.763983)
        (xy 172.322176 32.763983)
        (xy 172.230736 32.7495)
        (xy 171.889262 32.7495)
        (xy 171.797825 32.763983)
        (xy 171.797821 32.763984)
        (xy 171.68761 32.82014)
        (xy 171.60014 32.90761)
        (xy 171.543983 33.017823)
        (xy 171.5295 33.109263)
        (xy 171.5295 33.510737)
        (xy 171.543983 33.602174)
        (xy 171.543984 33.602178)
        (xy 171.582228 33.677235)
        (xy 171.600141 33.712391)
        (xy 171.682748 33.794998)
        (xy 171.710524 33.849513)
        (xy 171.700953 33.909945)
        (xy 171.682749 33.935)
        (xy 171.670001 33.947748)
        (xy 171.615487 33.975524)
        (xy 171.555055 33.965953)
        (xy 171.529999 33.947749)
        (xy 171.492391 33.910141)
        (xy 171.382175 33.853983)
        (xy 171.382176 33.853983)
        (xy 171.290737 33.8395)
        (xy 171.290735 33.8395)
        (xy 171.187594 33.8395)
        (xy 171.172111 33.838281)
        (xy 171.164019 33.837)
        (xy 171.164017 33.837)
        (xy 169.262257 33.837)
        (xy 169.204066 33.818093)
        (xy 169.171693 33.777989)
        (xy 169.127206 33.677235)
        (xy 169.047765 33.597794)
        (xy 169.047763 33.597793)
        (xy 169.047762 33.597792)
        (xy 169.040197 33.59261)
        (xy 169.04222 33.589655)
        (xy 169.009178 33.560075)
        (xy 168.996284 33.500264)
        (xy 169.021009 33.444297)
        (xy 169.049823 33.422119)
        (xy 169.152391 33.369859)
        (xy 169.189998 33.332251)
        (xy 169.244513 33.304476)
        (xy 169.304945 33.314047)
        (xy 169.33 33.33225)
        (xy 169.367609 33.369859)
        (xy 169.477825 33.426017)
        (xy 169.569265 33.4405)
        (xy 169.910734 33.440499)
        (xy 169.910737 33.440499)
        (xy 169.941174 33.435678)
        (xy 170.002175 33.426017)
        (xy 170.112391 33.369859)
        (xy 170.172755 33.309494)
        (xy 170.22727 33.281719)
        (xy 170.242757 33.2805)
        (xy 170.373432 33.2805)
        (xy 170.373433 33.2805)
        (xy 170.396382 33.273043)
        (xy 170.411465 33.269421)
        (xy 170.435304 33.265646)
        (xy 170.456802 33.25469)
        (xy 170.471151 33.248748)
        (xy 170.49409 33.241296)
        (xy 170.513612 33.227111)
        (xy 170.526849 33.219)
        (xy 170.548342 33.20805)
        (xy 170.565261 33.191129)
        (xy 170.565273 33.19112)
        (xy 170.569061 33.187332)
        (xy 170.571625 33.186024)
        (xy 170.582109 33.177615)
        (xy 170.58212 33.177608)
        (xy 170.583594 33.179924)
        (xy 170.623574 33.159547)
        (xy 170.626121 33.159177)
        (xy 170.723709 33.14633)
        (xy 170.857625 33.090861)
        (xy 170.972621 33.002621)
        (xy 171.060861 32.887625)
        (xy 171.11633 32.753709)
        (xy 171.13525 32.61)
        (xy 171.134091 32.6012)
        (xy 171.11633 32.466291)
        (xy 171.060861 32.332375)
        (xy 170.972621 32.217379)
        (xy 170.857625 32.129139)
        (xy 170.857621 32.129137)
        (xy 170.723709 32.07367)
        (xy 170.723708 32.073669)
        (xy 170.58 32.05475)
        (xy 170.436291 32.073669)
        (xy 170.43629 32.07367)
        (xy 170.302378 32.129137)
        (xy 170.302374 32.129139)
        (xy 170.187381 32.217377)
        (xy 170.187373 32.217385)
        (xy 170.12112 32.303727)
        (xy 170.070696 32.338382)
        (xy 170.009531 32.33678)
        (xy 170.005641 32.335109)
        (xy 170.002176 32.333983)
        (xy 169.910736 32.3195)
        (xy 169.569262 32.3195)
        (xy 169.477825 32.333983)
        (xy 169.477821 32.333984)
        (xy 169.36761 32.39014)
        (xy 169.367609 32.39014)
        (xy 169.367609 32.390141)
        (xy 169.330001 32.427748)
        (xy 169.275487 32.455524)
        (xy 169.215055 32.445953)
        (xy 169.189999 32.427749)
        (xy 169.152391 32.390141)
        (xy 169.152388 32.390139)
        (xy 169.146881 32.384632)
        (xy 169.148521 32.382991)
        (xy 169.119232 32.342682)
        (xy 169.11923 32.281496)
        (xy 169.125177 32.26714)
        (xy 169.171017 32.177175)
        (xy 169.1855 32.085735)
        (xy 169.185499 31.744266)
        (xy 169.185499 31.744263)
        (xy 169.185499 31.744262)
        (xy 169.179123 31.704006)
        (xy 169.171017 31.652825)
        (xy 169.114859 31.542609)
        (xy 169.077251 31.505001)
        (xy 169.049476 31.450487)
        (xy 169.059047 31.390055)
        (xy 169.07725 31.364999)
        (xy 169.114859 31.327391)
        (xy 169.171017 31.217175)
        (xy 169.1855 31.125735)
        (xy 169.185499 30.784266)
        (xy 169.185499 30.784262)
        (xy 169.175697 30.722375)
        (xy 169.171017 30.692825)
        (xy 169.114859 30.582609)
        (xy 169.027391 30.495141)
        (xy 168.917175 30.438983)
        (xy 168.917176 30.438983)
        (xy 168.825736 30.4245)
        (xy 168.424262 30.4245)
        (xy 168.332825 30.438983)
        (xy 168.332821 30.438984)
        (xy 168.22261 30.49514)
        (xy 168.222609 30.49514)
        (xy 168.222609 30.495141)
        (xy 168.195001 30.522748)
        (xy 168.140487 30.550524)
        (xy 168.080055 30.540953)
        (xy 168.054999 30.522749)
        (xy 168.027391 30.495141)
        (xy 167.917175 30.438983)
        (xy 167.917176 30.438983)
        (xy 167.825736 30.4245)
        (xy 167.424262 30.4245)
        (xy 167.332825 30.438983)
        (xy 167.332822 30.438984)
        (xy 167.309969 30.450628)
        (xy 167.249537 30.460198)
        (xy 167.195021 30.432419)
        (xy 167.167245 30.377902)
        (xy 167.173562 30.324532)
        (xy 167.18633 30.293709)
        (xy 167.20525 30.15)
        (xy 167.199495 30.106291)
        (xy 167.18633 30.006291)
        (xy 167.130861 29.872375)
        (xy 167.042621 29.757379)
        (xy 166.927625 29.669139)
        (xy 166.927621 29.669137)
        (xy 166.793709 29.61367)
        (xy 166.793708 29.613669)
        (xy 166.65 29.59475)
        (xy 166.506291 29.613669)
        (xy 166.50629 29.61367)
        (xy 166.372378 29.669137)
        (xy 166.372374 29.669139)
        (xy 166.257381 29.757377)
        (xy 166.257377 29.757381)
        (xy 166.2391 29.7812)
        (xy 166.188674 29.815855)
        (xy 166.12751 29.814252)
        (xy 166.115613 29.80914)
        (xy 166.032838 29.766964)
        (xy 166.017175 29.758983)
        (xy 166.017176 29.758983)
        (xy 165.925736 29.7445)
        (xy 165.524262 29.7445)
        (xy 165.432825 29.758983)
        (xy 165.423546 29.763711)
        (xy 165.378603 29.7745)
        (xy 165.110259 29.7745)
        (xy 165.072374 29.766964)
        (xy 164.943709 29.71367)
        (xy 164.943708 29.713669)
        (xy 164.8 29.69475)
        (xy 164.656291 29.713669)
        (xy 164.65629 29.71367)
        (xy 164.522378 29.769137)
        (xy 164.522374 29.769139)
        (xy 164.407381 29.857377)
        (xy 164.407377 29.857381)
        (xy 164.319139 29.972374)
        (xy 164.319137 29.972378)
        (xy 164.26367 30.10629)
        (xy 164.263669 30.106291)
        (xy 164.24475 30.249999)
        (xy 164.24475 30.25)
        (xy 164.263669 30.393708)
        (xy 164.26367 30.393709)
        (xy 164.315623 30.519138)
        (xy 164.319139 30.527625)
        (xy 164.335242 30.548611)
        (xy 164.355667 30.606286)
        (xy 164.33829 30.664952)
        (xy 164.289749 30.7022)
        (xy 164.228585 30.703803)
        (xy 164.186698 30.678883)
        (xy 164.053592 30.545778)
        (xy 163.967329 30.481202)
        (xy 163.832482 30.430908)
        (xy 163.70809 30.422011)
        (xy 163.688927 30.420641)
        (xy 163.688926 30.420641)
        (xy 163.688925 30.420641)
        (xy 163.688922 30.420641)
        (xy 163.548298 30.451232)
        (xy 163.548289 30.451236)
        (xy 163.4335 30.513916)
        (xy 163.423947 30.518487)
        (xy 163.42238 30.519135)
        (xy 163.422375 30.519138)
        (xy 163.307384 30.607374)
        (xy 163.307377 30.607381)
        (xy 163.219139 30.722374)
        (xy 163.219137 30.722378)
        (xy 163.16367 30.85629)
        (xy 163.163669 30.856291)
        (xy 163.14475 30.999999)
        (xy 163.14475 31)
        (xy 163.163669 31.143708)
        (xy 163.16367 31.143709)
        (xy 163.211693 31.25965)
        (xy 163.219139 31.277625)
        (xy 163.307379 31.392621)
        (xy 163.422375 31.480861)
        (xy 163.556291 31.53633)
        (xy 163.606145 31.542893)
        (xy 163.661371 31.569233)
        (xy 163.663228 31.571042)
        (xy 163.801912 31.709726)
        (xy 163.826061 31.749134)
        (xy 163.837046 31.78294)
        (xy 163.849701 31.821887)
        (xy 163.904115 31.896781)
        (xy 163.904116 31.896782)
        (xy 163.904117 31.896783)
        (xy 164.145573 32.138238)
        (xy 164.220471 32.192656)
        (xy 164.338116 32.230881)
        (xy 164.338118 32.230881)
        (xy 164.391297 32.230881)
        (xy 164.449488 32.249788)
        (xy 164.485452 32.299288)
        (xy 164.490297 32.329881)
        (xy 164.490297 32.383062)
        (xy 164.51199 32.449826)
        (xy 164.528522 32.500708)
        (xy 164.582937 32.575603)
        (xy 164.582938 32.575604)
        (xy 164.582939 32.575605)
        (xy 164.824395 32.81706)
        (xy 164.899293 32.871478)
        (xy 165.016938 32.909703)
        (xy 165.01694 32.909703)
        (xy 165.140635 32.909703)
        (xy 165.140636 32.909703)
        (xy 165.16501 32.901782)
        (xy 165.226194 32.901782)
        (xy 165.265607 32.925933)
        (xy 165.267033 32.927359)
        (xy 165.29481 32.981876)
        (xy 165.292659 33.022972)
        (xy 165.291078 33.028874)
        (xy 165.273346 33.16356)
        (xy 165.272157 33.172593)
        (xy 165.286612 33.282391)
        (xy 165.288864 33.299492)
        (xy 165.277714 33.359652)
        (xy 165.233332 33.40177)
        (xy 165.190711 33.411414)
        (xy 165.167603 33.411414)
        (xy 165.05974 33.450673)
        (xy 165.052234 33.455007)
        (xy 165.051097 33.453037)
        (xy 165.005592 33.471416)
        (xy 164.964841 33.465686)
        (xy 164.882394 33.435678)
        (xy 164.767606 33.435678)
        (xy 164.767603 33.435678)
        (xy 164.65974 33.474937)
        (xy 164.652234 33.479271)
        (xy 164.651097 33.477301)
        (xy 164.605592 33.49568)
        (xy 164.564841 33.48995)
        (xy 164.482394 33.459942)
        (xy 164.367606 33.459942)
        (xy 164.335782 33.471525)
        (xy 164.259739 33.499202)
        (xy 164.22276 33.530231)
        (xy 164.16603 33.55315)
        (xy 164.147716 33.552731)
        (xy 164.119868 33.5495)
        (xy 163.930138 33.5495)
        (xy 163.930135 33.549501)
        (xy 163.905007 33.552415)
        (xy 163.864986 33.570086)
        (xy 163.804116 33.576294)
        (xy 163.785016 33.570087)
        (xy 163.744991 33.552415)
        (xy 163.74499 33.552414)
        (xy 163.744988 33.552414)
        (xy 163.719868 33.5495)
        (xy 163.530138 33.5495)
        (xy 163.530135 33.549501)
        (xy 163.505009 33.552414)
        (xy 163.402235 33.597794)
        (xy 163.322794 33.677235)
        (xy 163.277414 33.780011)
        (xy 163.2745 33.80513)
        (xy 163.2745 34.66986)
        (xy 163.274501 34.669863)
        (xy 163.277414 34.69499)
        (xy 163.291079 34.725938)
        (xy 163.322794 34.797765)
        (xy 163.402235 34.877206)
        (xy 163.505009 34.922585)
        (xy 163.530135 34.9255)
        (xy 163.719864 34.925499)
        (xy 163.744991 34.922585)
        (xy 163.785011 34.904913)
        (xy 163.845879 34.898705)
        (xy 163.864989 34.904914)
        (xy 163.905009 34.922585)
        (xy 163.905008 34.922585)
        (xy 163.909478 34.923103)
        (xy 163.930135 34.9255)
        (xy 164.119864 34.925499)
        (xy 164.144991 34.922585)
        (xy 164.185011 34.904913)
        (xy 164.245879 34.898705)
        (xy 164.264989 34.904914)
        (xy 164.305009 34.922585)
        (xy 164.305008 34.922585)
        (xy 164.309478 34.923103)
        (xy 164.330135 34.9255)
        (xy 164.519864 34.925499)
        (xy 164.544991 34.922585)
        (xy 164.585011 34.904913)
        (xy 164.645879 34.898705)
        (xy 164.664989 34.904914)
        (xy 164.705009 34.922585)
        (xy 164.705008 34.922585)
        (xy 164.709478 34.923103)
        (xy 164.730135 34.9255)
        (xy 164.919864 34.925499)
        (xy 164.944991 34.922585)
        (xy 164.985011 34.904913)
        (xy 165.045879 34.898705)
        (xy 165.064989 34.904914)
        (xy 165.105009 34.922585)
        (xy 165.105008 34.922585)
        (xy 165.109478 34.923103)
        (xy 165.130135 34.9255)
        (xy 165.319864 34.925499)
        (xy 165.344991 34.922585)
        (xy 165.385011 34.904913)
        (xy 165.445879 34.898705)
        (xy 165.464989 34.904914)
        (xy 165.505009 34.922585)
        (xy 165.505008 34.922585)
        (xy 165.510446 34.923215)
        (xy 165.530135 34.9255)
        (xy 165.552761 34.925499)
        (xy 165.610951 34.944404)
        (xy 165.646916 34.993903)
        (xy 165.646919 35.055088)
        (xy 165.613038 35.103035)
        (xy 165.60738 35.107377)
        (xy 165.607377 35.107379)
        (xy 165.604777 35.110769)
        (xy 165.554351 35.145424)
        (xy 165.526236 35.1495)
        (xy 164.869531 35.1495)
        (xy 164.742665 35.1495)
        (xy 164.742664 35.1495)
        (xy 164.742658 35.149501)
        (xy 164.719722 35.156953)
        (xy 164.704623 35.160578)
        (xy 164.680799 35.164352)
        (xy 164.680793 35.164354)
        (xy 164.659294 35.175307)
        (xy 164.644955 35.181246)
        (xy 164.622012 35.188702)
        (xy 164.622009 35.188704)
        (xy 164.60249 35.202884)
        (xy 164.589254 35.210995)
        (xy 164.567756 35.22195)
        (xy 164.545192 35.244513)
        (xy 164.54519 35.244515)
        (xy 164.545188 35.244515)
        (xy 164.545189 35.244516)
        (xy 163.844515 35.94519)
        (xy 163.82195 35.967756)
        (xy 163.821948 35.967758)
        (xy 163.810997 35.989249)
        (xy 163.802887 36.002483)
        (xy 163.788706 36.022004)
        (xy 163.788701 36.022013)
        (xy 163.781249 36.04495)
        (xy 163.775305 36.0593)
        (xy 163.764355 36.080789)
        (xy 163.764352 36.080799)
        (xy 163.760577 36.104627)
        (xy 163.756953 36.119722)
        (xy 163.749501 36.14266)
        (xy 163.7495 36.142667)
        (xy 163.7495 39.207339)
        (xy 163.756953 39.230276)
        (xy 163.760579 39.245376)
        (xy 163.764354 39.269208)
        (xy 163.775301 39.29069)
        (xy 163.781247 39.305044)
        (xy 163.788703 39.32799)
        (xy 163.788702 39.32799)
        (xy 163.802884 39.34751)
        (xy 163.810997 39.360749)
        (xy 163.82195 39.382244)
        (xy 163.821951 39.382245)
        (xy 163.84203 39.402325)
        (xy 163.842031 39.402325)
        (xy 163.844513 39.404807)
        (xy 163.844516 39.404811)
        (xy 164.545189 40.105484)
        (xy 164.545192 40.105486)
        (xy 164.545191 40.105486)
        (xy 164.567754 40.128048)
        (xy 164.567756 40.12805)
        (xy 164.589243 40.138998)
        (xy 164.602484 40.147112)
        (xy 164.622006 40.161294)
        (xy 164.622007 40.161294)
        (xy 164.622009 40.161296)
        (xy 164.644959 40.168752)
        (xy 164.659299 40.174693)
        (xy 164.680794 40.185646)
        (xy 164.704632 40.189421)
        (xy 164.719709 40.193041)
        (xy 164.742665 40.2005)
        (xy 164.774579 40.2005)
        (xy 166.3755 40.2005)
        (xy 166.433691 40.219407)
        (xy 166.469655 40.268907)
        (xy 166.4745 40.2995)
        (xy 166.4745 40.460522)
        (xy 166.455593 40.518713)
        (xy 166.406093 40.554677)
        (xy 166.344907 40.554677)
        (xy 166.305498 40.530527)
        (xy 166.247765 40.472794)
        (xy 166.144991 40.427415)
        (xy 166.14499 40.427414)
        (xy 166.144988 40.427414)
        (xy 166.119868 40.4245)
        (xy 165.930138 40.4245)
        (xy 165.930135 40.424501)
        (xy 165.905007 40.427415)
        (xy 165.864986 40.445086)
        (xy 165.804116 40.451294)
        (xy 165.785016 40.445087)
        (xy 165.744991 40.427415)
        (xy 165.74499 40.427414)
        (xy 165.744988 40.427414)
        (xy 165.719868 40.4245)
        (xy 165.530138 40.4245)
        (xy 165.530135 40.424501)
        (xy 165.505007 40.427415)
        (xy 165.464986 40.445086)
        (xy 165.404116 40.451294)
        (xy 165.385016 40.445087)
        (xy 165.344991 40.427415)
        (xy 165.34499 40.427414)
        (xy 165.344988 40.427414)
        (xy 165.319868 40.4245)
        (xy 165.130138 40.4245)
        (xy 165.130135 40.424501)
        (xy 165.105007 40.427415)
        (xy 165.064986 40.445086)
        (xy 165.004116 40.451294)
        (xy 164.985016 40.445087)
        (xy 164.944991 40.427415)
        (xy 164.94499 40.427414)
        (xy 164.944988 40.427414)
        (xy 164.919868 40.4245)
        (xy 164.730138 40.4245)
        (xy 164.730135 40.424501)
        (xy 164.705007 40.427415)
        (xy 164.664986 40.445086)
        (xy 164.604116 40.451294)
        (xy 164.585016 40.445087)
        (xy 164.544991 40.427415)
        (xy 164.54499 40.427414)
        (xy 164.544988 40.427414)
        (xy 164.519868 40.4245)
        (xy 164.330138 40.4245)
        (xy 164.330135 40.424501)
        (xy 164.305007 40.427415)
        (xy 164.264986 40.445086)
        (xy 164.204116 40.451294)
        (xy 164.185016 40.445087)
        (xy 164.144991 40.427415)
        (xy 164.14499 40.427414)
        (xy 164.144988 40.427414)
        (xy 164.119868 40.4245)
        (xy 163.930138 40.4245)
        (xy 163.930135 40.424501)
        (xy 163.905007 40.427415)
        (xy 163.864986 40.445086)
        (xy 163.804116 40.451294)
        (xy 163.785016 40.445087)
        (xy 163.744991 40.427415)
        (xy 163.74499 40.427414)
        (xy 163.744988 40.427414)
        (xy 163.719869 40.4245)
        (xy 163.574499 40.4245)
        (xy 163.516308 40.405593)
        (xy 163.480344 40.356093)
        (xy 163.475499 40.325504)
        (xy 163.475499 40.180136)
        (xy 163.472585 40.155009)
        (xy 163.460681 40.12805)
        (xy 163.454914 40.114988)
        (xy 163.448705 40.054119)
        (xy 163.454911 40.035017)
        (xy 163.472585 39.994991)
        (xy 163.4755 39.969865)
        (xy 163.475499 39.780136)
        (xy 163.472585 39.755009)
        (xy 163.454914 39.714988)
        (xy 163.448705 39.654119)
        (xy 163.454911 39.635017)
        (xy 163.472585 39.594991)
        (xy 163.4755 39.569865)
        (xy 163.475499 39.380136)
        (xy 163.472585 39.355009)
        (xy 163.454914 39.314988)
        (xy 163.448705 39.254119)
        (xy 163.454911 39.235017)
        (xy 163.472585 39.194991)
        (xy 163.4755 39.169865)
        (xy 163.475499 38.980136)
        (xy 163.472585 38.955009)
        (xy 163.427206 38.852235)
        (xy 163.347765 38.772794)
        (xy 163.331391 38.765564)
        (xy 163.285796 38.724765)
        (xy 163.27289 38.664956)
        (xy 163.297603 38.608984)
        (xy 163.33139 38.584435)
        (xy 163.347765 38.577206)
        (xy 163.427206 38.497765)
        (xy 163.472585 38.394991)
        (xy 163.4755 38.369865)
        (xy 163.475499 38.180136)
        (xy 163.472585 38.155009)
        (xy 163.454914 38.114988)
        (xy 163.448705 38.054119)
        (xy 163.454911 38.035017)
        (xy 163.472585 37.994991)
        (xy 163.4755 37.969865)
        (xy 163.475499 37.780136)
        (xy 163.472585 37.755009)
        (xy 163.454914 37.714988)
        (xy 163.448705 37.654119)
        (xy 163.454911 37.635017)
        (xy 163.472585 37.594991)
        (xy 163.4755 37.569865)
        (xy 163.475499 37.380136)
        (xy 163.472585 37.355009)
        (xy 163.454914 37.314988)
        (xy 163.448705 37.254119)
        (xy 163.454911 37.235017)
        (xy 163.472585 37.194991)
        (xy 163.4755 37.169865)
        (xy 163.475499 36.980136)
        (xy 163.472585 36.955009)
        (xy 163.454914 36.914988)
        (xy 163.448705 36.854119)
        (xy 163.454911 36.835017)
        (xy 163.472585 36.794991)
        (xy 163.4755 36.769865)
        (xy 163.475499 36.580136)
        (xy 163.472585 36.555009)
        (xy 163.467451 36.543383)
        (xy 163.454914 36.514988)
        (xy 163.448705 36.454119)
        (xy 163.454911 36.435017)
        (xy 163.472585 36.394991)
        (xy 163.4755 36.369865)
        (xy 163.475499 36.180136)
        (xy 163.472585 36.155009)
        (xy 163.454914 36.114988)
        (xy 163.448705 36.054119)
        (xy 163.454911 36.035017)
        (xy 163.472585 35.994991)
        (xy 163.4755 35.969865)
        (xy 163.475499 35.780136)
        (xy 163.472585 35.755009)
        (xy 163.454914 35.714988)
        (xy 163.448705 35.654119)
        (xy 163.454911 35.635017)
        (xy 163.472585 35.594991)
        (xy 163.4755 35.569865)
        (xy 163.475499 35.380136)
        (xy 163.472585 35.355009)
        (xy 163.427206 35.252235)
        (xy 163.347765 35.172794)
        (xy 163.244991 35.127415)
        (xy 163.24499 35.127414)
        (xy 163.244988 35.127414)
        (xy 163.219868 35.1245)
        (xy 162.355139 35.1245)
        (xy 162.355136 35.124501)
        (xy 162.330009 35.127414)
        (xy 162.299095 35.141065)
        (xy 162.259107 35.1495)
        (xy 161.45652 35.1495)
        (xy 161.371956 35.164411)
        (xy 161.272541 35.221808)
        (xy 161.26591 35.227373)
        (xy 161.264257 35.225403)
        (xy 161.223437 35.250903)
        (xy 161.162401 35.246626)
        (xy 161.115536 35.20729)
        (xy 161.1005 35.15484)
        (xy 161.1005 31.353805)
        (xy 161.119407 31.295614)
        (xy 161.168907 31.25965)
        (xy 161.230093 31.25965)
        (xy 161.268161 31.282485)
        (xy 161.672096 31.67139)
        (xy 162.324998 32.299999)
        (xy 162.325 32.3)
        (xy 162.709998 32.42)
        (xy 162.709998 32.419999)
        (xy 162.71 32.42)
        (xy 162.57 26.83)
        (xy 162.57 26.829999)
        (xy 161.199019 26.823333)
        (xy 161.14092 26.804143)
        (xy 161.105198 26.754468)
        (xy 161.1005 26.724334)
        (xy 161.1005 25.636142)
        (xy 161.119407 25.577951)
        (xy 161.168907 25.541987)
        (xy 161.197221 25.537169)
        (xy 170.136903 25.332248)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 164.501388 30.714758)
        (xy 164.522369 30.730857)
        (xy 164.522371 30.730858)
        (xy 164.522375 30.730861)
        (xy 164.656291 30.78633)
        (xy 164.8 30.80525)
        (xy 164.943709 30.78633)
        (xy 164.950164 30.783656)
        (xy 164.951662 30.783036)
        (xy 164.989547 30.7755)
        (xy 165.378603 30.7755)
        (xy 165.423547 30.786289)
        (xy 165.432825 30.791017)
        (xy 165.524265 30.8055)
        (xy 165.925734 30.805499)
        (xy 165.925735 30.805499)
        (xy 165.936308 30.803824)
        (xy 165.975013 30.797694)
        (xy 166.035444 30.807265)
        (xy 166.078709 30.850529)
        (xy 166.0895 30.895475)
        (xy 166.0895 31.115736)
        (xy 166.103983 31.207174)
        (xy 166.103984 31.207178)
        (xy 166.13072 31.25965)
        (xy 166.160141 31.317391)
        (xy 166.247609 31.404859)
        (xy 166.357825 31.461017)
        (xy 166.449265 31.4755)
        (xy 166.850734 31.475499)
        (xy 166.850737 31.475499)
        (xy 166.877332 31.471286)
        (xy 166.942175 31.461017)
        (xy 166.987474 31.437935)
        (xy 167.047905 31.428364)
        (xy 167.102422 31.45614)
        (xy 167.1302 31.510657)
        (xy 167.120629 31.571089)
        (xy 167.120629 31.57109)
        (xy 167.078983 31.652824)
        (xy 167.0645 31.744263)
        (xy 167.0645 31.974165)
        (xy 167.045593 32.032356)
        (xy 167.035502 32.04417)
        (xy 167.007938 32.071733)
        (xy 167.001574 32.077564)
        (xy 166.971807 32.102542)
        (xy 166.971805 32.102544)
        (xy 166.952371 32.136205)
        (xy 166.94774 32.143475)
        (xy 166.925446 32.175316)
        (xy 166.925446 32.175317)
        (xy 166.925442 32.175324)
        (xy 166.925051 32.176786)
        (xy 166.91517 32.20064)
        (xy 166.914414 32.201948)
        (xy 166.91441 32.20196)
        (xy 166.907662 32.240223)
        (xy 166.905794 32.248649)
        (xy 166.895736 32.286187)
        (xy 166.895736 32.286194)
        (xy 166.899123 32.324904)
        (xy 166.8995 32.333533)
        (xy 166.8995 33.709107)
        (xy 166.891065 33.749094)
        (xy 166.877415 33.780008)
        (xy 166.877414 33.780011)
        (xy 166.8745 33.80513)
        (xy 166.8745 34.66986)
        (xy 166.874501 34.669863)
        (xy 166.877414 34.69499)
        (xy 166.891079 34.725938)
        (xy 166.922794 34.797765)
        (xy 167.002235 34.877206)
        (xy 167.019742 34.884936)
        (xy 167.065337 34.925736)
        (xy 167.078242 34.985545)
        (xy 167.053528 35.041517)
        (xy 167.000635 35.072273)
        (xy 166.979753 35.0745)
        (xy 166.383379 35.0745)
        (xy 166.325188 35.055593)
        (xy 166.323111 35.054042)
        (xy 166.272477 35.015189)
        (xy 166.273832 35.013422)
        (xy 166.239505 34.975298)
        (xy 166.233109 34.914448)
        (xy 166.259941 34.865029)
        (xy 166.327206 34.797765)
        (xy 166.372585 34.694991)
        (xy 166.3755 34.669865)
        (xy 166.375499 33.805136)
        (xy 166.372585 33.780009)
        (xy 166.370526 33.775346)
        (xy 166.358935 33.749093)
        (xy 166.3505 33.709106)
        (xy 166.3505 33.388719)
        (xy 166.350877 33.380089)
        (xy 166.353181 33.353758)
        (xy 166.360341 33.324499)
        (xy 166.363737 33.316302)
        (xy 166.382657 33.172593)
        (xy 166.380939 33.159547)
        (xy 166.363737 33.028884)
        (xy 166.308268 32.894968)
        (xy 166.220028 32.779972)
        (xy 166.105032 32.691732)
        (xy 166.105028 32.69173)
        (xy 165.971116 32.636263)
        (xy 165.971117 32.636263)
        (xy 165.918606 32.629349)
        (xy 165.863382 32.603007)
        (xy 165.861526 32.6012)
        (xy 165.725933 32.465607)
        (xy 165.698156 32.41109)
        (xy 165.701782 32.365011)
        (xy 165.709703 32.340636)
        (xy 165.709703 32.216938)
        (xy 165.671478 32.099293)
        (xy 165.661628 32.085736)
        (xy 165.617062 32.024396)
        (xy 165.375605 31.78294)
        (xy 165.300706 31.728521)
        (xy 165.225254 31.704006)
        (xy 165.183062 31.690297)
        (xy 165.129881 31.690297)
        (xy 165.07169 31.67139)
        (xy 165.035726 31.62189)
        (xy 165.030881 31.591297)
        (xy 165.030881 31.538118)
        (xy 165.030881 31.538116)
        (xy 164.992656 31.420471)
        (xy 164.981313 31.404859)
        (xy 164.93824 31.345574)
        (xy 164.696783 31.104118)
        (xy 164.621885 31.0497)
        (xy 164.549134 31.026061)
        (xy 164.509727 31.001912)
        (xy 164.371118 30.863304)
        (xy 164.34334 30.808787)
        (xy 164.352911 30.748355)
        (xy 164.396176 30.705091)
        (xy 164.456608 30.695519)
      )
    )
  )
  (zone (net 1) (net_name "GND") (layer "F.Cu") (tstamp fa433d75-9050-4dc7-9e30-d2552df84be0) (hatch edge 0.5)
    (priority 1)
    (connect_pads (clearance 0.15))
    (min_thickness 0.15) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.3) (thermal_bridge_width 0.3))
    (polygon
      (pts
        (xy 185.615 122.675)
        (xy 185.77 127.48)
        (xy 161.215 126.705)
        (xy 160.725 50.025)
        (xy 186.225 49.675)
        (xy 186.42 82.9)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 185.872284 49.697154)
        (xy 185.898193 49.74064)
        (xy 185.8995 49.754489)
        (xy 185.8995 108.617116)
        (xy 185.899485 108.618613)
        (xy 185.615 122.675005)
        (xy 185.767456 127.401162)
        (xy 185.751685 127.449262)
        (xy 185.708687 127.475972)
        (xy 185.69116 127.477511)
        (xy 161.286208 126.707247)
        (xy 161.239211 126.688443)
        (xy 161.215297 126.643829)
        (xy 161.214544 126.633767)
        (xy 161.100502 108.78723)
        (xy 161.1005 108.786757)
        (xy 161.1005 50.092836)
        (xy 161.117813 50.04527)
        (xy 161.16165 50.01996)
        (xy 161.173474 50.018843)
        (xy 185.824487 49.680497)
      )
    )
  )
  (zone (net 1) (net_name "GND") (layer "B.Cu") (tstamp d2709e5f-1c59-495c-8fec-fd26063ad4f8) (hatch edge 0.5)
    (priority 6)
    (connect_pads yes (clearance 0.05))
    (min_thickness 0.05) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.15) (thermal_bridge_width 0.3))
    (polygon
      (pts
        (xy 166.425622 42.166425)
        (xy 170.375622 42.116425)
        (xy 170.275622 46.166425)
        (xy 166.225622 46.216425)
        (xy 166.375622 42.216425)
      )
    )
    (filled_polygon
      (layer "B.Cu")
      (pts
        (xy 170.275622 46.166425)
        (xy 169.425075 46.176924)
        (xy 169.412319 46.088199)
        (xy 169.358504 45.970362)
        (xy 169.349564 45.960044)
        (xy 169.273673 45.872459)
        (xy 169.273671 45.872458)
        (xy 169.164695 45.802423)
        (xy 169.164692 45.802421)
        (xy 169.164691 45.802421)
        (xy 169.040394 45.765925)
        (xy 168.91085 45.765925)
        (xy 168.83786 45.787356)
        (xy 168.786555 45.80242)
        (xy 168.786548 45.802423)
        (xy 168.677572 45.872458)
        (xy 168.67757 45.872459)
        (xy 168.59274 45.970361)
        (xy 168.59274 45.970362)
        (xy 168.538925 46.088199)
        (xy 168.533104 46.128682)
        (xy 168.524569 46.188042)
        (xy 166.225622 46.216425)
        (xy 166.375622 42.216425)
        (xy 166.425622 42.166425)
        (xy 167.786669 42.149196)
        (xy 167.79274 42.162488)
        (xy 167.87757 42.26039)
        (xy 167.877572 42.260391)
        (xy 167.877573 42.260392)
        (xy 167.986553 42.330429)
        (xy 168.11085 42.366925)
        (xy 168.110852 42.366925)
        (xy 168.240392 42.366925)
        (xy 168.240394 42.366925)
        (xy 168.364691 42.330429)
        (xy 168.473671 42.260392)
        (xy 168.558504 42.162488)
        (xy 168.569097 42.139291)
        (xy 170.375622 42.116425)
      )
    )
  )
  (zone (net 1) (net_name "GND") (layer "B.Cu") (tstamp dba1ad8b-6343-434c-9365-20e6272c8374) (hatch edge 0.5)
    (priority 2)
    (connect_pads (clearance 0.25))
    (min_thickness 0.2) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.3) (thermal_bridge_width 0.3))
    (polygon
      (pts
        (xy 160.7 127.825)
        (xy 160.875 19.925)
        (xy 160.975 20.025)
        (xy 186.2 20.1)
        (xy 186.2 128.05)
        (xy 146.72 128.05)
      )
    )
    (filled_polygon
      (layer "B.Cu")
      (pts
        (xy 185.858691 20.369407)
        (xy 185.894655 20.418907)
        (xy 185.8995 20.4495)
        (xy 185.8995 35.784099)
        (xy 185.880593 35.84229)
        (xy 185.831093 35.878254)
        (xy 185.769907 35.878254)
        (xy 185.730496 35.854103)
        (xy 185.310102 35.433709)
        (xy 184.860909 34.984516)
        (xy 184.860905 34.984513)
        (xy 184.858423 34.982031)
        (xy 184.858423 34.98203)
        (xy 184.838343 34.961951)
        (xy 184.838342 34.96195)
        (xy 184.816847 34.950997)
        (xy 184.803608 34.942884)
        (xy 184.797581 34.938505)
        (xy 184.78409 34.928704)
        (xy 184.784088 34.928703)
        (xy 184.761142 34.921247)
        (xy 184.746788 34.915301)
        (xy 184.725306 34.904354)
        (xy 184.701474 34.900579)
        (xy 184.686374 34.896953)
        (xy 184.663437 34.8895)
        (xy 184.663433 34.8895)
        (xy 184.631519 34.8895)
        (xy 183.95596 34.8895)
        (xy 183.897769 34.870593)
        (xy 183.895693 34.869042)
        (xy 183.817629 34.809142)
        (xy 183.817628 34.809141)
        (xy 183.817625 34.809139)
        (xy 183.817622 34.809138)
        (xy 183.817621 34.809137)
        (xy 183.683709 34.75367)
        (xy 183.683708 34.753669)
        (xy 183.54 34.73475)
        (xy 183.396291 34.753669)
        (xy 183.39629 34.75367)
        (xy 183.262378 34.809137)
        (xy 183.262374 34.809139)
        (xy 183.147381 34.897377)
        (xy 183.147373 34.897385)
        (xy 183.110041 34.946037)
        (xy 183.059616 34.980692)
        (xy 182.998452 34.97909)
        (xy 182.949911 34.941842)
        (xy 182.9325 34.885769)
        (xy 182.9325 33.029876)
        (xy 182.951407 32.971685)
        (xy 182.961496 32.959872)
        (xy 183.491057 32.430311)
        (xy 184.021591 31.899777)
        (xy 184.103111 31.796677)
        (xy 184.177257 31.637673)
        (xy 184.212734 31.465856)
        (xy 184.207631 31.290487)
        (xy 184.162223 31.121024)
        (xy 184.078959 30.9666)
        (xy 183.962326 30.835541)
        (xy 183.962325 30.83554)
        (xy 183.818613 30.734912)
        (xy 183.65557 30.670139)
        (xy 183.655569 30.670138)
        (xy 183.655567 30.670138)
        (xy 183.481977 30.644711)
        (xy 183.481973 30.644711)
        (xy 183.481972 30.644711)
        (xy 183.481971 30.64471)
        (xy 183.307204 30.66)
        (xy 183.140665 30.715186)
        (xy 183.140661 30.715188)
        (xy 182.991347 30.807286)
        (xy 182.991344 30.807288)
        (xy 181.695068 32.103561)
        (xy 181.684188 32.112964)
        (xy 181.663471 32.128388)
        (xy 181.663469 32.12839)
        (xy 181.630922 32.167176)
        (xy 181.625105 32.173525)
        (xy 181.620415 32.178216)
        (xy 181.620405 32.178227)
        (xy 181.600958 32.202822)
        (xy 181.599141 32.205052)
        (xy 181.550701 32.262781)
        (xy 181.550697 32.262787)
        (xy 181.550269 32.26364)
        (xy 181.539481 32.280574)
        (xy 181.538888 32.281323)
        (xy 181.507031 32.34964)
        (xy 181.505777 32.35223)
        (xy 181.47196 32.419565)
        (xy 181.471959 32.419567)
        (xy 181.471744 32.420478)
        (xy 181.465143 32.439469)
        (xy 181.464744 32.440323)
        (xy 181.46474 32.440335)
        (xy 181.449496 32.514158)
        (xy 181.448874 32.516965)
        (xy 181.4315 32.590279)
        (xy 181.4315 32.591209)
        (xy 181.429455 32.611226)
        (xy 181.429265 32.612142)
        (xy 181.431458 32.687472)
        (xy 181.4315 32.690352)
        (xy 181.4315 36.463709)
        (xy 181.446759 36.594255)
        (xy 181.446759 36.594257)
        (xy 181.44676 36.594258)
        (xy 181.506762 36.759116)
        (xy 181.603168 36.905693)
        (xy 181.60317 36.905696)
        (xy 181.73078 37.02609)
        (xy 181.73078 37.026091)
        (xy 181.730782 37.026092)
        (xy 181.882719 37.113812)
        (xy 182.006603 37.150901)
        (xy 182.050786 37.164129)
        (xy 182.050787 37.164129)
        (xy 182.05079 37.16413)
        (xy 182.125852 37.168501)
        (xy 182.225931 37.174331)
        (xy 182.225931 37.17433)
        (xy 182.225935 37.174331)
        (xy 182.358815 37.1509)
        (xy 182.398707 37.143866)
        (xy 182.398708 37.143865)
        (xy 182.398711 37.143865)
        (xy 182.559804 37.074377)
        (xy 182.70053 36.96961)
        (xy 182.813302 36.835214)
        (xy 182.89204 36.678433)
        (xy 182.892041 36.678425)
        (xy 182.892043 36.678423)
        (xy 182.9325 36.507722)
        (xy 182.9325 35.69423)
        (xy 182.951407 35.636039)
        (xy 183.000907 35.600075)
        (xy 183.062093 35.600075)
        (xy 183.11004 35.633961)
        (xy 183.147379 35.682621)
        (xy 183.262375 35.770861)
        (xy 183.396291 35.82633)
        (xy 183.54 35.84525)
        (xy 183.683709 35.82633)
        (xy 183.817625 35.770861)
        (xy 183.855934 35.741464)
        (xy 183.895693 35.710958)
        (xy 183.953369 35.690534)
        (xy 183.95596 35.6905)
        (xy 184.393099 35.6905)
        (xy 184.45129 35.709407)
        (xy 184.463103 35.719496)
        (xy 185.320504 36.576897)
        (xy 185.348281 36.631414)
        (xy 185.3495 36.646901)
        (xy 185.3495 40.081519)
        (xy 185.364354 40.175304)
        (xy 185.42195 40.288342)
        (xy 185.511658 40.37805)
        (xy 185.624696 40.435646)
        (xy 185.75 40.455492)
        (xy 185.785014 40.449946)
        (xy 185.845444 40.459517)
        (xy 185.888709 40.502781)
        (xy 185.8995 40.547727)
        (xy 185.8995 42.852271)
        (xy 185.880593 42.910462)
        (xy 185.831093 42.946426)
        (xy 185.785025 42.950054)
        (xy 185.781524 42.9495)
        (xy 185.781519 42.9495)
        (xy 184.913433 42.9495)
        (xy 184.786567 42.9495)
        (xy 184.786563 42.9495)
        (xy 184.7707 42.954655)
        (xy 184.740109 42.9595)
        (xy 184.46596 42.9595)
        (xy 184.407769 42.940593)
        (xy 184.405693 42.939042)
        (xy 184.327629 42.879142)
        (xy 184.327628 42.879141)
        (xy 184.327625 42.879139)
        (xy 184.327622 42.879138)
        (xy 184.327621 42.879137)
        (xy 184.193709 42.82367)
        (xy 184.193708 42.823669)
        (xy 184.05 42.80475)
        (xy 183.906291 42.823669)
        (xy 183.90629 42.82367)
        (xy 183.772378 42.879137)
        (xy 183.77237 42.879142)
        (xy 183.694307 42.939042)
        (xy 183.636631 42.959466)
        (xy 183.63404 42.9595)
        (xy 182.864163 42.9595)
        (xy 182.805972 42.940593)
        (xy 182.803896 42.939042)
        (xy 182.787175 42.926212)
        (xy 182.787174 42.926211)
        (xy 182.787171 42.926209)
        (xy 182.787168 42.926208)
        (xy 182.787167 42.926207)
        (xy 182.653255 42.87074)
        (xy 182.653254 42.870739)
        (xy 182.509546 42.85182)
        (xy 182.365837 42.870739)
        (xy 182.365836 42.87074)
        (xy 182.231924 42.926207)
        (xy 182.23192 42.926209)
        (xy 182.116927 43.014447)
        (xy 182.116923 43.014451)
        (xy 182.028685 43.129444)
        (xy 182.028683 43.129448)
        (xy 181.973216 43.26336)
        (xy 181.973215 43.263361)
        (xy 181.954296 43.407069)
        (xy 181.954296 43.40707)
        (xy 181.973215 43.550778)
        (xy 181.973216 43.550779)
        (xy 182.028685 43.684695)
        (xy 182.116925 43.799691)
        (xy 182.231921 43.887931)
        (xy 182.365837 43.9434)
        (xy 182.509546 43.96232)
        (xy 182.653255 43.9434)
        (xy 182.787171 43.887931)
        (xy 182.902167 43.799691)
        (xy 182.902522 43.799228)
        (xy 182.902867 43.79899)
        (xy 182.906752 43.795106)
        (xy 182.907472 43.795826)
        (xy 182.952947 43.764576)
        (xy 182.981061 43.7605)
        (xy 183.63404 43.7605)
        (xy 183.692231 43.779407)
        (xy 183.694307 43.780958)
        (xy 183.761078 43.832193)
        (xy 183.772375 43.840861)
        (xy 183.906291 43.89633)
        (xy 184.05 43.91525)
        (xy 184.193709 43.89633)
        (xy 184.327625 43.840861)
        (xy 184.365934 43.811464)
        (xy 184.405693 43.780958)
        (xy 184.463369 43.760534)
        (xy 184.46596 43.7605)
        (xy 184.903432 43.7605)
        (xy 184.903433 43.7605)
        (xy 184.906455 43.759518)
        (xy 184.919296 43.755346)
        (xy 184.949889 43.7505)
        (xy 185.781521 43.7505)
        (xy 185.785012 43.749947)
        (xy 185.845444 43.759518)
        (xy 185.888709 43.802782)
        (xy 185.8995 43.847728)
        (xy 185.8995 127.6505)
        (xy 185.880593 127.708691)
        (xy 185.831093 127.744655)
        (xy 185.8005 127.7495)
        (xy 161.1995 127.7495)
        (xy 161.141309 127.730593)
        (xy 161.105345 127.681093)
        (xy 161.1005 127.6505)
        (xy 161.1005 39.8)
        (xy 164.05 39.8)
        (xy 166.248322 39.77286)
        (xy 166.24475 39.799999)
        (xy 166.24475 39.8)
        (xy 166.263669 39.943708)
        (xy 166.26367 39.943709)
        (xy 166.319137 40.077621)
        (xy 166.319142 40.07763)
        (xy 166.379041 40.15569)
        (xy 166.399466 40.213365)
        (xy 166.3995 40.215958)
        (xy 166.3995 41.863437)
        (xy 166.406953 41.886374)
        (xy 166.410579 41.901474)
        (xy 166.414354 41.925306)
        (xy 166.425301 41.946788)
        (xy 166.431247 41.961142)
        (xy 166.438703 41.984088)
        (xy 166.438702 41.984088)
        (xy 166.452884 42.003608)
        (xy 166.460997 42.016847)
        (xy 166.47195 42.038342)
        (xy 166.471951 42.038343)
        (xy 166.49203 42.058423)
        (xy 166.492031 42.058423)
        (xy 166.492032 42.058424)
        (xy 166.492031 42.058424)
        (xy 166.72545 42.291842)
        (xy 166.746911 42.323961)
        (xy 166.769139 42.377625)
        (xy 166.857379 42.492621)
        (xy 166.972375 42.580861)
        (xy 167.106291 42.63633)
        (xy 167.158913 42.643257)
        (xy 167.176578 42.647254)
        (xy 167.186567 42.6505)
        (xy 167.207432 42.6505)
        (xy 167.220354 42.651347)
        (xy 167.25 42.65525)
        (xy 167.279646 42.651347)
        (xy 167.292568 42.6505)
        (xy 167.313432 42.6505)
        (xy 167.313433 42.6505)
        (xy 167.323415 42.647256)
        (xy 167.341082 42.643258)
        (xy 167.393709 42.63633)
        (xy 167.527625 42.580861)
        (xy 167.642621 42.492621)
        (xy 167.730861 42.377625)
        (xy 167.78633 42.243709)
        (xy 167.80525 42.1)
        (xy 167.800103 42.060909)
        (xy 167.78633 41.956291)
        (xy 167.730861 41.822375)
        (xy 167.642621 41.707379)
        (xy 167.527625 41.619139)
        (xy 167.527621 41.619137)
        (xy 167.466944 41.594004)
        (xy 167.393709 41.56367)
        (xy 167.372795 41.560916)
        (xy 167.286577 41.549565)
        (xy 167.231352 41.523223)
        (xy 167.202158 41.469452)
        (xy 167.2005 41.451412)
        (xy 167.2005 40.215958)
        (xy 167.219407 40.157767)
        (xy 167.220959 40.15569)
        (xy 167.280857 40.07763)
        (xy 167.280856 40.07763)
        (xy 167.280861 40.077625)
        (xy 167.33633 39.943709)
        (xy 167.35525 39.8)
        (xy 167.349886 39.75926)
        (xy 168.1 39.75)
        (xy 168.2 35.7)
        (xy 166.504121 35.721466)
        (xy 166.53633 35.643709)
        (xy 166.55525 35.5)
        (xy 166.546522 35.433709)
        (xy 166.53633 35.356291)
        (xy 166.480861 35.222375)
        (xy 166.472193 35.211078)
        (xy 166.420958 35.144307)
        (xy 166.400534 35.086631)
        (xy 166.4005 35.08404)
        (xy 166.4005 33.368478)
        (xy 166.395139 33.334636)
        (xy 166.385646 33.274696)
        (xy 166.385644 33.274692)
        (xy 166.383237 33.267281)
        (xy 166.384877 33.266747)
        (xy 166.3766 33.218591)
        (xy 166.382657 33.172593)
        (xy 166.363737 33.028884)
        (xy 166.308268 32.894968)
        (xy 166.220028 32.779972)
        (xy 166.105032 32.691732)
        (xy 166.105028 32.69173)
        (xy 165.971116 32.636263)
        (xy 165.971115 32.636262)
        (xy 165.827407 32.617343)
        (xy 165.683698 32.636262)
        (xy 165.683697 32.636263)
        (xy 165.549785 32.69173)
        (xy 165.549781 32.691732)
        (xy 165.434788 32.77997)
        (xy 165.434784 32.779974)
        (xy 165.346546 32.894967)
        (xy 165.346544 32.894971)
        (xy 165.291077 33.028883)
        (xy 165.291076 33.028884)
        (xy 165.272157 33.172592)
        (xy 165.272157 33.172593)
        (xy 165.291076 33.316301)
        (xy 165.291077 33.316302)
        (xy 165.346546 33.450218)
        (xy 165.434786 33.565214)
        (xy 165.43479 33.565217)
        (xy 165.434791 33.565218)
        (xy 165.549783 33.653455)
        (xy 165.55 33.65358)
        (xy 165.550119 33.653712)
        (xy 165.55493 33.657404)
        (xy 165.554245 33.658295)
        (xy 165.590941 33.699049)
        (xy 165.5995 33.739316)
        (xy 165.5995 35.08404)
        (xy 165.580593 35.142231)
        (xy 165.579042 35.144307)
        (xy 165.519142 35.22237)
        (xy 165.519137 35.222378)
        (xy 165.46367 35.35629)
        (xy 165.463669 35.356291)
        (xy 165.44475 35.499999)
        (xy 165.44475 35.5)
        (xy 165.463669 35.643708)
        (xy 165.46367 35.643709)
        (xy 165.501136 35.734162)
        (xy 164.25 35.75)
        (xy 164.2 35.8)
        (xy 164.05 39.8)
        (xy 161.1005 39.8)
        (xy 161.1005 38.048809)
        (xy 161.119407 37.990618)
        (xy 161.168907 37.954654)
        (xy 161.230093 37.954654)
        (xy 161.279593 37.990618)
        (xy 161.295125 38.023183)
        (xy 161.312945 38.089684)
        (xy 161.362201 38.160028)
        (xy 161.528328 38.326155)
        (xy 161.598672 38.375411)
        (xy 161.709549 38.405121)
        (xy 161.823901 38.395117)
        (xy 161.927936 38.346604)
        (xy 162.009103 38.265437)
        (xy 162.057615 38.161403)
        (xy 162.06762 38.04705)
        (xy 162.03791 37.936173)
        (xy 161.988654 37.865829)
        (xy 161.822527 37.699702)
        (xy 161.752183 37.650446)
        (xy 161.641306 37.620736)
        (xy 161.641304 37.620736)
        (xy 161.565071 37.627406)
        (xy 161.526954 37.630741)
        (xy 161.432042 37.674999)
        (xy 161.422919 37.679253)
        (xy 161.34175 37.760422)
        (xy 161.289579 37.872304)
        (xy 161.286416 37.870829)
        (xy 161.26179 37.908737)
        (xy 161.204665 37.930654)
        (xy 161.145567 37.914808)
        (xy 161.10707 37.867252)
        (xy 161.1005 37.831789)
        (xy 161.1005 20.4495)
        (xy 161.119407 20.391309)
        (xy 161.168907 20.355345)
        (xy 161.1995 20.3505)
        (xy 185.8005 20.3505)
      )
    )
  )
  (group "" (id 13c41adc-3373-4cfe-9edd-51cd92465380)
    (members
      3e30d8cd-e664-46c5-8b79-8a4afa8b9abe
    )
  )
)
