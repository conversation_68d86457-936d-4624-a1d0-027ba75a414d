class Cursor:
    def __init__(self, row=0, col=0, col_hint=None):
        self.row = row
        self._col = col
        self._col_hint = col if col_hint is None else col_hint

    @classmethod
    def createFromCursor(cls,cursor):
        return cls(row=cursor.row, col=cursor.col)

    @property
    def col(self):
        return self._col


    @col.setter
    def col(self, col):
        self._col = col
        self._col_hint = col

    def _clamp_col(self, buffer):
        self._col = min(self._col_hint, len(buffer[self.row]))

    def _clamp_row(self, buffer):
        self.row = min(self.row, len(buffer)-1)


    def clamp(self, buffer):
        self._clamp_row(buffer)
        self._col = min(self._col, len(buffer[self.row]))

    def move(self, newx, newy, buffer):
        self.row = newx
        self.col = newy
        self._clamp_col(buffer)

    def up(self, buffer):
        if self.row > 0:
            self.row -= 1
            self._clamp_col(buffer)

    def down(self, buffer):
        if self.row < len(buffer) - 1:
            self.row += 1
            self._clamp_col(buffer)

    def left(self, buffer):
        ok = True
        if self.col > 0:
            self.col -= 1
        elif self.row > 0:
            self.row -= 1
            self.col = len(buffer[self.row])
        else:
            ok = False
        return ok

    def right(self, buffer):
        ok = True
        if self.col < len(buffer[self.row]):
            self.col += 1
        elif self.row < len(buffer) - 1:
            self.row += 1
            self.col = 0
        else:
            ok = False
        return ok

