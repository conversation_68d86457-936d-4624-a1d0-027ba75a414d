#ifndef DTOSTRF_H_
#define DTOSTRF_H_

/*
  dtostrf - Emulation for dtostrf function from avr-libc
  Copyright (c) 2015 Arduino LLC.  All rights reserved.

  This library is free software; you can redistribute it and/or
  modify it under the terms of the GNU Lesser General Public
  License as published by the Free Software Foundation; either
  version 2.1 of the License, or (at your option) any later version.

  This library is distributed in the hope that it will be useful,
  but WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
  Lesser General Public License for more details.

  You should have received a copy of the GNU Lesser General Public
  License along with this library; if not, write to the Free Software
  Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
*/

#pragma once

#if !defined(ARDUINO_ARCH_AVR)
#ifdef __cplusplus
extern "C"
{
#endif

// char *dtostrf(double val, signed char width, unsigned char prec, char *sout);
#include "stdio.h"
#include <cstdio>

    char* dtostrf(double val, signed char width, unsigned char prec, char* sout)
    {
        asm(".global _printf_float");

#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
        char fmt[20];
        sprintf(fmt, "%%%d.%df", width, prec);
        sprintf(sout, fmt, val);
        return sout;
#pragma GCC diagnostic pop
    }

#ifdef __cplusplus
}
#endif

#endif
#endif // DTOSTRF_H_
