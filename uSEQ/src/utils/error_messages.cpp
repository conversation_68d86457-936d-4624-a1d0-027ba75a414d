#include "error_messages.h"

String TOO_FEW_ARGS       = "too few arguments to function";
String TOO_MANY_ARGS      = "too many arguments to function";
String INVALID_ARGUMENT   = "invalid argument";
String MISMATCHED_TYPES   = "mismatched types";
String CALL_NON_FUNCTION  = "called non-function ";
String UNKNOWN_ERROR      = "unknown exception";
String INVALID_LAMBDA     = "invalid lambda";
String INVALID_BIN_OP     = "invalid binary operation";
String INVALID_ORDER      = "cannot order expression";
String BAD_CAST           = "cannot cast";
String ATOM_NOT_DEFINED   = "atom not defined ";
String EVAL_EMPTY_LIST    = "evaluated empty list";
String INTERNAL_ERROR     = "internal virtual machine error";
String INDEX_OUT_OF_RANGE = "index out of range";
String MALFORMED_PROGRAM  = "malformed program";
