#include <stddef.h>
#include <stdlib.h>
#include <stdbool.h>

void logic_gate_net(char const *inp, char *out) {
	const char v0 = (char) (inp[9] ^ inp[30]);
	const char v1 = (char) (~(inp[6] ^ inp[17]));
	const char v2 = (char) (~inp[24]);
	const char v3 = (char) (~inp[3]);
	const char v4 = (char) (inp[2] & ~inp[29]);
	const char v5 = (char) (inp[8]);
	const char v6 = (char) (~(inp[27] & inp[21]));
	const char v7 = (char) (~(inp[13] | inp[0]));
	const char v8 = (char) (inp[19]);
	const char v9 = (char) (~(inp[6] & inp[2]));
	const char v10 = (char) (inp[6] & inp[25]);
	const char v11 = (char) (inp[30] ^ inp[3]);
	const char v12 = (char) (~inp[1] | inp[12]);
	const char v13 = (char) (~(inp[29] | inp[24]));
	const char v14 = (char) (~(inp[12] | inp[24]));
	const char v15 = (char) (~(inp[17] | inp[18]));
	const char v16 = (char) (inp[6] | inp[0]);
	const char v17 = (char) (inp[28]);
	const char v18 = (char) (~inp[24]);
	const char v19 = (char) (~inp[23]);
	const char v20 = (char) (inp[5] & ~inp[24]);
	const char v21 = (char) (~inp[16]);
	const char v22 = (char) (~(inp[1] | inp[5]));
	const char v23 = (char) (~inp[19]);
	const char v24 = (char) (inp[22]);
	const char v25 = (char) (~inp[11]);
	const char v26 = (char) (~inp[9]);
	const char v27 = (char) (~inp[18] | inp[5]);
	const char v28 = (char) (~(inp[30] | inp[31]));
	const char v29 = (char) (~inp[29]);
	const char v30 = (char) (inp[29] ^ inp[4]);
	const char v31 = (char) (inp[27] | inp[15]);
	const char v32 = (char) (~inp[20]);
	const char v33 = (char) (inp[9] | inp[10]);
	const char v34 = (char) (~inp[26]);
	const char v35 = (char) (inp[1] & ~inp[7]);
	const char v36 = (char) (~inp[3]);
	const char v37 = (char) (inp[16] & ~inp[7]);
	const char v38 = (char) (~inp[6]);
	const char v39 = (char) (inp[15] ^ inp[31]);
	const char v40 = (char) (~(inp[11] | inp[7]));
	const char v41 = (char) (inp[0] & ~inp[3]);
	const char v42 = (char) (~inp[14]);
	const char v43 = (char) (inp[10] & ~inp[1]);
	const char v44 = (char) (inp[8] & ~inp[15]);
	const char v45 = (char) (~inp[18]);
	const char v46 = (char) (inp[7] & ~inp[23]);
	const char v47 = (char) (~inp[29]);
	const char v48 = (char) (~(inp[23] ^ inp[1]));
	const char v49 = (char) (inp[28]);
	const char v50 = (char) (~inp[24]);
	const char v51 = (char) (inp[28] | inp[27]);
	const char v52 = (char) (~(inp[8] ^ inp[12]));
	const char v53 = (char) (~inp[16]);
	const char v54 = (char) (inp[17] | inp[11]);
	const char v55 = (char) (~(inp[12] ^ inp[19]));
	const char v56 = (char) (inp[23] & ~inp[15]);
	const char v57 = (char) (~(inp[4] ^ inp[31]));
	const char v58 = (char) (inp[20] | inp[23]);
	const char v59 = (char) (inp[26]);
	const char v60 = (char) (inp[21]);
	const char v61 = (char) (inp[0] | inp[18]);
	const char v62 = (char) (inp[7] | inp[2]);
	const char v63 = (char) (~inp[20]);
	const char v64 = (char) (inp[2]);
	const char v65 = (char) (inp[27]);
	const char v66 = (char) (inp[28] | inp[19]);
	const char v67 = (char) (inp[20]);
	const char v68 = (char) (~(inp[27] | inp[23]));
	const char v69 = (char) (inp[8] ^ inp[14]);
	const char v70 = (char) (~inp[30]);
	const char v71 = (char) (inp[22] ^ inp[30]);
	const char v72 = (char) (inp[12] & inp[27]);
	const char v73 = (char) (~(inp[1] & inp[19]));
	const char v74 = (char) (inp[16] ^ inp[6]);
	const char v75 = (char) (~inp[31] | inp[6]);
	const char v76 = (char) (~(inp[11] ^ inp[7]));
	const char v77 = (char) (~inp[28] | inp[10]);
	const char v78 = (char) (inp[18] & inp[18]);
	const char v79 = (char) (~(inp[17] | inp[9]));
	const char v80 = (char) (inp[14]);
	const char v81 = (char) (~inp[11] | inp[19]);
	const char v82 = (char) (~(inp[29] ^ inp[10]));
	const char v83 = (char) (~inp[31]);
	const char v84 = (char) (~inp[14]);
	const char v85 = (char) (inp[15] & ~inp[7]);
	const char v86 = (char) (inp[10]);
	const char v87 = (char) (~(inp[26] ^ inp[31]));
	const char v88 = (char) (inp[9] ^ inp[29]);
	const char v89 = (char) (inp[12] & ~inp[11]);
	const char v90 = (char) (~inp[28]);
	const char v91 = (char) (inp[22] ^ inp[27]);
	const char v92 = (char) (~(inp[7] & inp[16]));
	const char v93 = (char) (~inp[22]);
	const char v94 = (char) (inp[23] & ~inp[4]);
	const char v95 = (char) (~(inp[31] ^ inp[5]));
	const char v96 = (char) (inp[21] | inp[16]);
	const char v97 = (char) (inp[4] & ~inp[25]);
	const char v98 = (char) (inp[18] & ~inp[21]);
	const char v99 = (char) (~(inp[15] | inp[20]));
	const char v100 = (char) (~inp[26]);
	const char v101 = (char) (~inp[4]);
	const char v102 = (char) (inp[28]);
	const char v103 = (char) (inp[4] & ~inp[24]);
	const char v104 = (char) (inp[0]);
	const char v105 = (char) (inp[20] | inp[26]);
	const char v106 = (char) (~(inp[19] ^ inp[10]));
	const char v107 = (char) (~(inp[11] ^ inp[11]));
	const char v108 = (char) (~inp[28] | inp[1]);
	const char v109 = (char) (~(inp[14] & inp[7]));
	const char v110 = (char) (~(inp[11] & inp[19]));
	const char v111 = (char) (inp[19] | inp[3]);
	const char v112 = (char) (~(inp[30] ^ inp[2]));
	const char v113 = (char) (~(inp[8] & inp[16]));
	const char v114 = (char) (inp[10] | inp[2]);
	const char v115 = (char) (inp[19] & inp[26]);
	const char v116 = (char) (~inp[29] | inp[16]);
	const char v117 = (char) (~inp[4]);
	const char v118 = (char) (~inp[11] | inp[13]);
	const char v119 = (char) (~(inp[23] | inp[4]));
	const char v120 = (char) (~inp[31]);
	const char v121 = (char) (~inp[15]);
	const char v122 = (char) (~inp[17] | inp[14]);
	const char v123 = (char) (~inp[29] | inp[22]);
	const char v124 = (char) (inp[8] & ~inp[1]);
	const char v125 = (char) (inp[23] & ~inp[10]);
	const char v126 = (char) (~(inp[25] & inp[11]));
	const char v127 = (char) (inp[10]);
	const char v128 = (char) (inp[16]);
	const char v129 = (char) (inp[9] ^ inp[7]);
	const char v130 = (char) (~inp[22]);
	const char v131 = (char) (inp[22] ^ inp[4]);
	const char v132 = (char) (inp[1]);
	const char v133 = (char) (inp[16]);
	const char v134 = (char) (~inp[26]);
	const char v135 = (char) (inp[25]);
	const char v136 = (char) (inp[2]);
	const char v137 = (char) (~(char) 0);
	const char v138 = (char) (~(inp[25] | inp[17]));
	const char v139 = (char) (inp[4]);
	const char v140 = (char) (~inp[13]);
	const char v141 = (char) (~inp[3] | inp[24]);
	const char v142 = (char) (~inp[5] | inp[22]);
	const char v143 = (char) (inp[17]);
	const char v144 = (char) (~(inp[15] ^ inp[26]));
	const char v145 = (char) (~(inp[30] ^ inp[25]));
	const char v146 = (char) (inp[16] | inp[15]);
	const char v147 = (char) (~(inp[7] ^ inp[0]));
	const char v148 = (char) (inp[19] | inp[24]);
	const char v149 = (char) (~inp[0] | inp[19]);
	const char v150 = (char) (inp[15] & inp[23]);
	const char v151 = (char) (inp[22] ^ inp[10]);
	const char v152 = (char) (inp[25] | inp[0]);
	const char v153 = (char) (inp[27]);
	const char v154 = (char) (~(inp[26] | inp[14]));
	const char v155 = (char) (inp[30] & ~inp[18]);
	const char v156 = (char) (~(inp[13] & inp[5]));
	const char v157 = (char) (~(inp[8] | inp[18]));
	const char v158 = (char) (~(inp[1] ^ inp[9]));
	const char v159 = (char) (~inp[7]);
	const char v160 = (char) (inp[12] | inp[5]);
	const char v161 = (char) (~(inp[5] | inp[9]));
	const char v162 = (char) (inp[30]);
	const char v163 = (char) (inp[30]);
	const char v164 = (char) (inp[8]);
	const char v165 = (char) (~inp[5]);
	const char v166 = (char) (inp[24]);
	const char v167 = (char) (inp[0]);
	const char v168 = (char) (inp[8] & inp[19]);
	const char v169 = (char) (inp[20]);
	const char v170 = (char) (inp[8] & ~inp[23]);
	const char v171 = (char) (inp[29]);
	const char v172 = (char) (~(inp[27] & inp[23]));
	const char v173 = (char) (inp[16]);
	const char v174 = (char) (inp[30] & inp[4]);
	const char v175 = (char) (inp[28] ^ inp[14]);
	const char v176 = (char) (inp[11] | inp[17]);
	const char v177 = (char) (inp[17]);
	const char v178 = (char) (~(inp[22] ^ inp[10]));
	const char v179 = (char) (~inp[3]);
	const char v180 = (char) (inp[20] & ~inp[5]);
	const char v181 = (char) (~(inp[26] & inp[13]));
	const char v182 = (char) (inp[22]);
	const char v183 = (char) (inp[2] | inp[6]);
	const char v184 = (char) (inp[16] | inp[2]);
	const char v185 = (char) (inp[31] | inp[29]);
	const char v186 = (char) (inp[28]);
	const char v187 = (char) (inp[19] & inp[28]);
	const char v188 = (char) (inp[23]);
	const char v189 = (char) (~inp[17] | inp[30]);
	const char v190 = (char) (inp[15] ^ inp[24]);
	const char v191 = (char) (inp[27] & ~inp[25]);
	const char v192 = (char) (inp[1] | inp[3]);
	const char v193 = (char) (~inp[1]);
	const char v194 = (char) (inp[8] | inp[8]);
	const char v195 = (char) (~(inp[14] & inp[30]));
	const char v196 = (char) (~inp[20]);
	const char v197 = (char) (~inp[5] | inp[27]);
	const char v198 = (char) (~inp[22]);
	const char v199 = (char) (~(inp[12] | inp[1]));
	const char v200 = (char) (inp[2] & inp[14]);
	const char v201 = (char) (inp[15] & ~inp[18]);
	const char v202 = (char) (inp[18] ^ inp[13]);
	const char v203 = (char) (inp[7] ^ inp[14]);
	const char v204 = (char) (~(inp[23] | inp[2]));
	const char v205 = (char) (inp[22]);
	const char v206 = (char) (inp[0] ^ inp[17]);
	const char v207 = (char) (inp[11]);
	const char v208 = (char) (inp[18] & inp[20]);
	const char v209 = (char) (~inp[24] | inp[1]);
	const char v210 = (char) (inp[6] ^ inp[27]);
	const char v211 = (char) (~inp[30] | inp[19]);
	const char v212 = (char) (~(inp[27] | inp[10]));
	const char v213 = (char) (inp[23] ^ inp[1]);
	const char v214 = (char) (inp[20] | inp[10]);
	const char v215 = (char) (~inp[19] | inp[12]);
	const char v216 = (char) (inp[21]);
	const char v217 = (char) (~(inp[29] | inp[27]));
	const char v218 = (char) (inp[27]);
	const char v219 = (char) (inp[31]);
	const char v220 = (char) (inp[14] ^ inp[2]);
	const char v221 = (char) (inp[25]);
	const char v222 = (char) (~(inp[0] | inp[5]));
	const char v223 = (char) (~(inp[26] ^ inp[6]));
	const char v224 = (char) (~(inp[19] & inp[0]));
	const char v225 = (char) (inp[14] & ~inp[6]);
	const char v226 = (char) (inp[31] & ~inp[22]);
	const char v227 = (char) (inp[3]);
	const char v228 = (char) (inp[28] ^ inp[25]);
	const char v229 = (char) (~inp[27]);
	const char v230 = (char) (~(inp[12] | inp[12]));
	const char v231 = (char) (~(char) 0);
	const char v232 = (char) (~(inp[25] ^ inp[9]));
	const char v233 = (char) (inp[15] | inp[3]);
	const char v234 = (char) (~inp[25] | inp[1]);
	const char v235 = (char) (inp[8] ^ inp[22]);
	const char v236 = (char) (inp[25] & inp[26]);
	const char v237 = (char) (inp[31] ^ inp[2]);
	const char v238 = (char) (~(inp[12] ^ inp[15]));
	const char v239 = (char) (~(inp[17] & inp[11]));
	const char v240 = (char) (~(inp[18] | inp[13]));
	const char v241 = (char) (inp[4]);
	const char v242 = (char) (inp[5] & inp[21]);
	const char v243 = (char) (~(inp[28] ^ inp[16]));
	const char v244 = (char) (~(inp[28] ^ inp[7]));
	const char v245 = (char) (inp[31] & inp[26]);
	const char v246 = (char) (~(inp[3] | inp[28]));
	const char v247 = (char) (~(inp[26] ^ inp[23]));
	const char v248 = (char) (inp[24]);
	const char v249 = (char) (~inp[29]);
	const char v250 = (char) (inp[14]);
	const char v251 = (char) (~inp[0] | inp[10]);
	const char v252 = (char) (~inp[5]);
	const char v253 = (char) (inp[3]);
	const char v254 = (char) (~inp[21] | inp[9]);
	const char v255 = (char) (inp[27] ^ inp[16]);
	const char v256 = (char) (~(inp[6] ^ inp[29]));
	const char v257 = (char) (~inp[24]);
	const char v258 = (char) (~inp[25] | inp[30]);
	const char v259 = (char) (inp[0] ^ inp[17]);
	const char v260 = (char) (inp[8] & ~inp[27]);
	const char v261 = (char) (~(inp[24] | inp[7]));
	const char v262 = (char) (~(inp[21] ^ inp[0]));
	const char v263 = (char) (~(inp[31] | inp[0]));
	const char v264 = (char) (~(inp[17] ^ inp[10]));
	const char v265 = (char) (~(inp[29] | inp[24]));
	const char v266 = (char) (inp[26] | inp[2]);
	const char v267 = (char) (~(inp[31] ^ inp[15]));
	const char v268 = (char) (~(inp[28] | inp[15]));
	const char v269 = (char) (inp[6] & ~inp[20]);
	const char v270 = (char) (~inp[16]);
	const char v271 = (char) (~inp[4]);
	const char v272 = (char) (inp[18]);
	const char v273 = (char) (inp[5] | inp[15]);
	const char v274 = (char) (~(inp[13] | inp[12]));
	const char v275 = (char) (inp[0] & ~inp[7]);
	const char v276 = (char) (inp[12] ^ inp[14]);
	const char v277 = (char) (~(inp[30] ^ inp[4]));
	const char v278 = (char) (~inp[20] | inp[17]);
	const char v279 = (char) (inp[4] & ~inp[30]);
	const char v280 = (char) (~(v51 ^ v141));
	const char v281 = (char) (~v105 | v5);
	const char v282 = (char) (v29 & ~v78);
	const char v283 = (char) (~v241);
	const char v284 = (char) (~(v44 ^ v254));
	const char v285 = (char) (~(v69 & v118));
	const char v286 = (char) (v48 & v278);
	const char v287 = (char) (v225 ^ v108);
	const char v288 = (char) (~v155);
	const char v289 = (char) (v67 ^ v129);
	const char v290 = (char) (~v183 | v65);
	const char v291 = (char) (~v193 | v238);
	const char v292 = (char) (~v277);
	const char v293 = (char) (~v247 | v96);
	const char v294 = (char) (v18 | v52);
	const char v295 = (char) (~(v207 ^ v82));
	const char v296 = (char) (~v231 | v91);
	const char v297 = (char) (v274 & ~v69);
	const char v298 = (char) (~(v176 | v204));
	const char v299 = (char) (~v132 | v191);
	const char v300 = (char) (v260);
	const char v301 = (char) (v152 | v143);
	const char v302 = (char) (v200 ^ v24);
	const char v303 = (char) (~(v229 | v222));
	const char v304 = (char) (v194);
	const char v305 = (char) (v210);
	const char v306 = (char) (~(v261 | v20));
	const char v307 = (char) (v83);
	const char v308 = (char) (~v39);
	const char v309 = (char) (v103);
	const char v310 = (char) (v257 & v13);
	const char v311 = (char) (~(v12 & v145));
	const char v312 = (char) (~v148);
	const char v313 = (char) (~v31 | v253);
	const char v314 = (char) (~(v75 | v208));
	const char v315 = (char) (v120);
	const char v316 = (char) (v248 ^ v33);
	const char v317 = (char) (v159 & v186);
	const char v318 = (char) (v128 & ~v187);
	const char v319 = (char) (v164 | v124);
	const char v320 = (char) (v246 | v199);
	const char v321 = (char) (~v83 | v235);
	const char v322 = (char) (v209 & v49);
	const char v323 = (char) (~v174 | v184);
	const char v324 = (char) (~(v150 | v136));
	const char v325 = (char) (v163);
	const char v326 = (char) (~v81 | v100);
	const char v327 = (char) (v162 & ~v46);
	const char v328 = (char) (~v175 | v157);
	const char v329 = (char) (v57);
	const char v330 = (char) (~(v86 ^ v88));
	const char v331 = (char) (v192 ^ v84);
	const char v332 = (char) (v196 | v157);
	const char v333 = (char) (~v239);
	const char v334 = (char) (~v149 | v115);
	const char v335 = (char) (v214 ^ v15);
	const char v336 = (char) (v151);
	const char v337 = (char) (v3 ^ v66);
	const char v338 = (char) (v43);
	const char v339 = (char) (v106);
	const char v340 = (char) (v24 ^ v237);
	const char v341 = (char) (~v224 | v138);
	const char v342 = (char) (v169 ^ v265);
	const char v343 = (char) (~(v14 ^ v17));
	const char v344 = (char) (v178 ^ v109);
	const char v345 = (char) (v44 | v213);
	const char v346 = (char) ((char) 0);
	const char v347 = (char) (~v128);
	const char v348 = (char) (~v277);
	const char v349 = (char) (v186);
	const char v350 = (char) (~(v8 ^ v120));
	const char v351 = (char) (~(v19 ^ v59));
	const char v352 = (char) (v247 & ~v43);
	const char v353 = (char) (~(v183 | v79));
	const char v354 = (char) (v197 & ~v219);
	const char v355 = (char) (v25 & ~v38);
	const char v356 = (char) (~v114 | v60);
	const char v357 = (char) (v94 & ~v72);
	const char v358 = (char) (v111 & ~v275);
	const char v359 = (char) (~(v125 ^ v162));
	const char v360 = (char) (~v70 | v135);
	const char v361 = (char) (~(v114 ^ v134));
	const char v362 = (char) (v231 ^ v61);
	const char v363 = (char) (~(v147 | v199));
	const char v364 = (char) (v248);
	const char v365 = (char) (v87 & ~v4);
	const char v366 = (char) (v268 | v264);
	const char v367 = (char) (v37 & ~v211);
	const char v368 = (char) (v243);
	const char v369 = (char) (~(v204 ^ v127));
	const char v370 = (char) (~v79);
	const char v371 = (char) (~v259 | v182);
	const char v372 = (char) (~(v55 & v258));
	const char v373 = (char) (~v62);
	const char v374 = (char) (v15 & ~v51);
	const char v375 = (char) (v130 ^ v215);
	const char v376 = (char) (v214);
	const char v377 = (char) (~v97);
	const char v378 = (char) (~v50);
	const char v379 = (char) (v268 ^ v265);
	const char v380 = (char) (~v27);
	const char v381 = (char) (~(v102 | v153));
	const char v382 = (char) (~v139);
	const char v383 = (char) (v21 | v28);
	const char v384 = (char) (v70 & ~v99);
	const char v385 = (char) (~v68);
	const char v386 = (char) (v88);
	const char v387 = (char) (v117 & v239);
	const char v388 = (char) (~(v230 & v279));
	const char v389 = (char) (~(v101 & v156));
	const char v390 = (char) (~v149 | v7);
	const char v391 = (char) (v173 & ~v273);
	const char v392 = (char) (v50);
	const char v393 = (char) (v143 ^ v135);
	const char v394 = (char) (~(v176 ^ v54));
	const char v395 = (char) (~(v12 ^ v242));
	const char v396 = (char) (v36);
	const char v397 = (char) (~v189 | v64);
	const char v398 = (char) (~v161 | v211);
	const char v399 = (char) (~v160);
	const char v400 = (char) (v131 | v98);
	const char v401 = (char) (v54 & ~v103);
	const char v402 = (char) (v165 | v226);
	const char v403 = (char) (v42);
	const char v404 = (char) (~(v87 | v110));
	const char v405 = (char) (v250);
	const char v406 = (char) (~(v29 ^ v26));
	const char v407 = (char) (~v47);
	const char v408 = (char) (~v90 | v76);
	const char v409 = (char) (v234 | v2);
	const char v410 = (char) (v35 & ~v263);
	const char v411 = (char) (v249);
	const char v412 = (char) (~v200);
	const char v413 = (char) (v153 | v276);
	const char v414 = (char) (~v245);
	const char v415 = (char) (~(v163 | v196));
	const char v416 = (char) (~v185);
	const char v417 = (char) (v112);
	const char v418 = (char) (v256 ^ v218);
	const char v419 = (char) (~v226);
	const char v420 = (char) (v92 | v74);
	const char v421 = (char) (~v236);
	const char v422 = (char) (v202);
	const char v423 = (char) (v189);
	const char v424 = (char) (~v84);
	const char v425 = (char) (v261);
	const char v426 = (char) (v34 & ~v271);
	const char v427 = (char) (v18 & ~v45);
	const char v428 = (char) (v8 | v221);
	const char v429 = (char) (~(v220 ^ v16));
	const char v430 = (char) (v206 | v91);
	const char v431 = (char) (~(v92 ^ v89));
	const char v432 = (char) (v4);
	const char v433 = (char) (~v56);
	const char v434 = (char) (v279 ^ v185);
	const char v435 = (char) (~v111 | v147);
	const char v436 = (char) (~(v58 | v259));
	const char v437 = (char) (~v194 | v255);
	const char v438 = (char) (v210);
	const char v439 = (char) (~(v53 & v107));
	const char v440 = (char) (~(v104 | v192));
	const char v441 = (char) (~(v251 | v271));
	const char v442 = (char) (v260 ^ v205);
	const char v443 = (char) (~v198 | v208);
	const char v444 = (char) (v165 ^ v0);
	const char v445 = (char) (~v229 | v272);
	const char v446 = (char) (~(v232 ^ v264));
	const char v447 = (char) (~v65 | v68);
	const char v448 = (char) (v52);
	const char v449 = (char) (~v217);
	const char v450 = (char) (v41 ^ v71);
	const char v451 = (char) (~(v97 ^ v106));
	const char v452 = (char) (~(v216 ^ v188));
	const char v453 = (char) (~(v206 ^ v179));
	const char v454 = (char) (v233);
	const char v455 = (char) (v80);
	const char v456 = (char) (v121);
	const char v457 = (char) (v123);
	const char v458 = (char) (~(v146 ^ v254));
	const char v459 = (char) (v166);
	const char v460 = (char) (v136);
	const char v461 = (char) (~(v267 ^ v266));
	const char v462 = (char) (v262 ^ v35);
	const char v463 = (char) (~(v257 & v63));
	const char v464 = (char) (~v278 | v30);
	const char v465 = (char) (v6 & ~v115);
	const char v466 = (char) (~(v107 ^ v146));
	const char v467 = (char) (v154);
	const char v468 = (char) (v10 ^ v223);
	const char v469 = (char) (~(v203 & v195));
	const char v470 = (char) (~v156 | v63);
	const char v471 = (char) (v145 & ~v213);
	const char v472 = (char) (~v76 | v267);
	const char v473 = (char) (~v116 | v142);
	const char v474 = (char) (~(v178 | v22));
	const char v475 = (char) (~v31 | v253);
	const char v476 = (char) (~(v244 | v27));
	const char v477 = (char) (~v95);
	const char v478 = (char) (~v144);
	const char v479 = (char) (v159 | v177);
	const char v480 = (char) (v23 & ~v93);
	const char v481 = (char) (~v273);
	const char v482 = (char) (v269 | v191);
	const char v483 = (char) (v252 & ~v113);
	const char v484 = (char) (v222 & v164);
	const char v485 = (char) (~(v139 & v73));
	const char v486 = (char) (v161 & v121);
	const char v487 = (char) (v225 ^ v212);
	const char v488 = (char) (~v96);
	const char v489 = (char) (~v138 | v170);
	const char v490 = (char) (~v228);
	const char v491 = (char) (v122 ^ v71);
	const char v492 = (char) (~(v274 | v230));
	const char v493 = (char) (v49);
	const char v494 = (char) (v233 | v142);
	const char v495 = (char) (~v197 | v175);
	const char v496 = (char) (~v41 | v246);
	const char v497 = (char) (~(v5 ^ v125));
	const char v498 = (char) (~v195);
	const char v499 = (char) (~(v85 | v2));
	const char v500 = (char) (v158 & ~v266);
	const char v501 = (char) (~v201);
	const char v502 = (char) (v94 ^ v10);
	const char v503 = (char) (v117);
	const char v504 = (char) (v209);
	const char v505 = (char) (v118 & v19);
	const char v506 = (char) (~v236 | v60);
	const char v507 = (char) (v82 | v37);
	const char v508 = (char) (~v219);
	const char v509 = (char) (v240 & v67);
	const char v510 = (char) (~(v123 | v182));
	const char v511 = (char) (~(v13 | v93));
	const char v512 = (char) (v75 | v1);
	const char v513 = (char) (v207 & ~v144);
	const char v514 = (char) (v124 & ~v216);
	const char v515 = (char) (~(v39 ^ v126));
	const char v516 = (char) (~(v99 | v227));
	const char v517 = (char) (v263);
	const char v518 = (char) (v234 & ~v38);
	const char v519 = (char) (~v77);
	const char v520 = (char) (v11);
	const char v521 = (char) (~(v270 ^ v132));
	const char v522 = (char) (~(v78 ^ v140));
	const char v523 = (char) (~v155 | v151);
	const char v524 = (char) (~(v276 ^ v23));
	const char v525 = (char) (~v20 | v34);
	const char v526 = (char) (v61 ^ v40);
	const char v527 = (char) (~(v45 | v9));
	const char v528 = (char) (~(v190 | v73));
	const char v529 = (char) (~v158 | v190);
	const char v530 = (char) (v133);
	const char v531 = (char) (~v152);
	const char v532 = (char) (v170);
	const char v533 = (char) (~v181 | v89);
	const char v534 = (char) (~v243);
	const char v535 = (char) (~v167);
	const char v536 = (char) (~(v218 & v109));
	const char v537 = (char) (~v40);
	const char v538 = (char) (~(v28 | v228));
	const char v539 = (char) (v112 | v90);
	const char v540 = (char) (v244 & ~v129);
	const char v541 = (char) (~v270);
	const char v542 = (char) (v133 | v42);
	const char v543 = (char) (~(v168 & v272));
	const char v544 = (char) (v180);
	const char v545 = (char) (~(v198 & v179));
	const char v546 = (char) (~v212);
	const char v547 = (char) (v137 & ~v173);
	const char v548 = (char) (v77 ^ v235);
	const char v549 = (char) (v6);
	const char v550 = (char) (v258 & v250);
	const char v551 = (char) (~(v174 ^ v275));
	const char v552 = (char) (~v171);
	const char v553 = (char) (~(v105 | v202));
	const char v554 = (char) (~v203 | v32);
	const char v555 = (char) (~(v201 | v100));
	const char v556 = (char) (v74 ^ v14);
	const char v557 = (char) (~v169);
	const char v558 = (char) (v172 | v16);
	const char v559 = (char) (~(v95 & v184));
	const char v560 = (char) (v519 | v404);
	const char v561 = (char) (~v365 | v358);
	const char v562 = (char) (~(v558 & v336));
	const char v563 = (char) (~(v417 ^ v349));
	const char v564 = (char) (~v347);
	const char v565 = (char) (v470 ^ v446);
	const char v566 = (char) (~v520 | v331);
	const char v567 = (char) (~(v546 | v551));
	const char v568 = (char) (v321);
	const char v569 = (char) (~v293 | v515);
	const char v570 = (char) (~v511);
	const char v571 = (char) (~v428);
	const char v572 = (char) (v387 & ~v311);
	const char v573 = (char) (~v481 | v339);
	const char v574 = (char) (v300 ^ v443);
	const char v575 = (char) (~v415 | v374);
	const char v576 = (char) (~v384);
	const char v577 = (char) (v513 ^ v457);
	const char v578 = (char) (~(v456 ^ v412));
	const char v579 = (char) (~(v309 ^ v442));
	const char v580 = (char) (v545 ^ v542);
	const char v581 = (char) (v508 | v436);
	const char v582 = (char) (v406);
	const char v583 = (char) (v400 & ~v359);
	const char v584 = (char) (v508);
	const char v585 = (char) (~v320 | v546);
	const char v586 = (char) (v494 ^ v540);
	const char v587 = (char) (v368);
	const char v588 = (char) (v518 & ~v471);
	const char v589 = (char) (~v298 | v488);
	const char v590 = (char) (~(v281 ^ v325));
	const char v591 = (char) (~(v352 ^ v473));
	const char v592 = (char) (v434);
	const char v593 = (char) (v533 & ~v535);
	const char v594 = (char) (~(v487 & v494));
	const char v595 = (char) (~v504 | v340);
	const char v596 = (char) (v524 ^ v423);
	const char v597 = (char) (~(v452 ^ v428));
	const char v598 = (char) (~v385 | v408);
	const char v599 = (char) (v299 & v285);
	const char v600 = (char) (v317 & ~v527);
	const char v601 = (char) (~v552);
	const char v602 = (char) (~v317 | v324);
	const char v603 = (char) (v345 | v516);
	const char v604 = (char) (v360 & v305);
	const char v605 = (char) (~v349 | v293);
	const char v606 = (char) (v330 & ~v484);
	const char v607 = (char) (~(v432 | v429));
	const char v608 = (char) (~v310 | v516);
	const char v609 = (char) (~v523 | v528);
	const char v610 = (char) (v491 ^ v306);
	const char v611 = (char) (~(v500 ^ v455));
	const char v612 = (char) (v354 & v449);
	const char v613 = (char) (~v496);
	const char v614 = (char) (v533 | v464);
	const char v615 = (char) (v522 & ~v377);
	const char v616 = (char) (~v483);
	const char v617 = (char) (~v447);
	const char v618 = (char) (v472 & ~v294);
	const char v619 = (char) (v365 & ~v300);
	const char v620 = (char) (~v536 | v337);
	const char v621 = (char) (v496 ^ v492);
	const char v622 = (char) (~v364 | v326);
	const char v623 = (char) (~v525 | v340);
	const char v624 = (char) (v463 & ~v498);
	const char v625 = (char) (v545 & ~v295);
	const char v626 = (char) (v523);
	const char v627 = (char) (v448 ^ v393);
	const char v628 = (char) (v478 & ~v351);
	const char v629 = (char) (~(v501 ^ v482));
	const char v630 = (char) (v544);
	const char v631 = (char) (v530 | v514);
	const char v632 = (char) (v471);
	const char v633 = (char) (~(v312 ^ v430));
	const char v634 = (char) (v283);
	const char v635 = (char) (~v484);
	const char v636 = (char) (v544);
	const char v637 = (char) (v451 ^ v450);
	const char v638 = (char) (v449);
	const char v639 = (char) (~v303);
	const char v640 = (char) (v313);
	const char v641 = (char) (~v372);
	const char v642 = (char) (~v503);
	const char v643 = (char) (~(v297 ^ v423));
	const char v644 = (char) (~(v424 ^ v499));
	const char v645 = (char) (~v302);
	const char v646 = (char) (~v335);
	const char v647 = (char) (v315 & ~v314);
	const char v648 = (char) (~(v361 & v532));
	const char v649 = (char) (~v433);
	const char v650 = (char) (v517 ^ v485);
	const char v651 = (char) (v553);
	const char v652 = (char) (v398 & ~v375);
	const char v653 = (char) (v517);
	const char v654 = (char) (v394 & v453);
	const char v655 = (char) (v495 | v451);
	const char v656 = (char) (~(v438 | v369));
	const char v657 = (char) (~v410);
	const char v658 = (char) (v284);
	const char v659 = (char) (v452 & v465);
	const char v660 = (char) (~v421 | v510);
	const char v661 = (char) (~v294 | v345);
	const char v662 = (char) (v339);
	const char v663 = (char) (~(v369 & v403));
	const char v664 = (char) (v548);
	const char v665 = (char) (~(v336 ^ v326));
	const char v666 = (char) (v395);
	const char v667 = (char) (v283 ^ v290);
	const char v668 = (char) (~v491);
	const char v669 = (char) (v285 & v512);
	const char v670 = (char) (~(v381 ^ v342));
	const char v671 = (char) (v287);
	const char v672 = (char) (v354);
	const char v673 = (char) (v460);
	const char v674 = (char) (v395 & ~v392);
	const char v675 = (char) (v436);
	const char v676 = (char) (v465 ^ v296);
	const char v677 = (char) (v308 ^ v433);
	const char v678 = (char) (~v327);
	const char v679 = (char) (v486 & ~v385);
	const char v680 = (char) (~v554);
	const char v681 = (char) (~(v476 & v390));
	const char v682 = (char) (~(v405 | v548));
	const char v683 = (char) (v407);
	const char v684 = (char) (~(v531 & v333));
	const char v685 = (char) (~v386 | v475);
	const char v686 = (char) (v318 ^ v440);
	const char v687 = (char) (~v358);
	const char v688 = (char) (v464 ^ v556);
	const char v689 = (char) (v338 & v291);
	const char v690 = (char) (v330 & ~v399);
	const char v691 = (char) (~(v355 ^ v289));
	const char v692 = (char) (~v398);
	const char v693 = (char) (~(v469 | v462));
	const char v694 = (char) (~v505);
	const char v695 = (char) (v526 & ~v286);
	const char v696 = (char) (v280 & ~v296);
	const char v697 = (char) (~v379);
	const char v698 = (char) (~(v289 | v335));
	const char v699 = (char) (v302 & ~v518);
	const char v700 = (char) (~(v372 & v492));
	const char v701 = (char) (v557 & ~v455);
	const char v702 = (char) (~(v348 | v337));
	const char v703 = (char) (~(v510 | v364));
	const char v704 = (char) (v502);
	const char v705 = (char) (~(v353 & v537));
	const char v706 = (char) (~v555 | v350);
	const char v707 = (char) (v325 ^ v380);
	const char v708 = (char) (v557 & ~v490);
	const char v709 = (char) (v391 & ~v356);
	const char v710 = (char) (~v282);
	const char v711 = (char) (~(v467 | v305));
	const char v712 = (char) (v530 | v341);
	const char v713 = (char) (~v352);
	const char v714 = (char) (~v479);
	const char v715 = (char) (v437 & ~v343);
	const char v716 = (char) (v413);
	const char v717 = (char) (v322);
	const char v718 = (char) (v416 ^ v447);
	const char v719 = (char) (v539);
	const char v720 = (char) (~v408);
	const char v721 = (char) (~(v505 ^ v506));
	const char v722 = (char) (v478 | v333);
	const char v723 = (char) (~(v384 ^ v535));
	const char v724 = (char) (v488);
	const char v725 = (char) (~v511 | v528);
	const char v726 = (char) (~(v439 | v314));
	const char v727 = (char) (v507);
	const char v728 = (char) (v468);
	const char v729 = (char) (v301 | v322);
	const char v730 = (char) (v419);
	const char v731 = (char) (~v435 | v474);
	const char v732 = (char) (v542);
	const char v733 = (char) (~v420 | v378);
	const char v734 = (char) (v460 | v467);
	const char v735 = (char) (~(v425 | v559));
	const char v736 = (char) (v549);
	const char v737 = (char) (v389 & ~v370);
	const char v738 = (char) (~v412 | v502);
	const char v739 = (char) (~v444);
	const char v740 = (char) (v351 | v541);
	const char v741 = (char) (v396);
	const char v742 = (char) (~v399 | v477);
	const char v743 = (char) (v402);
	const char v744 = (char) (~v371);
	const char v745 = (char) (~(v439 & v420));
	const char v746 = (char) (v537 | v480);
	const char v747 = (char) (~v342);
	const char v748 = (char) (~(v462 ^ v551));
	const char v749 = (char) (v415);
	const char v750 = (char) (~v446 | v426);
	const char v751 = (char) (~(v343 ^ v417));
	const char v752 = (char) (~v430);
	const char v753 = (char) (v291);
	const char v754 = (char) (v389 & v363);
	const char v755 = (char) (v429 | v497);
	const char v756 = (char) (~v427 | v373);
	const char v757 = (char) (~v416 | v401);
	const char v758 = (char) (~v320);
	const char v759 = (char) (~v529);
	const char v760 = (char) (v461 & ~v431);
	const char v761 = (char) (~v500 | v501);
	const char v762 = (char) (v304 & ~v520);
	const char v763 = (char) (~v485 | v350);
	const char v764 = (char) (v344 ^ v282);
	const char v765 = (char) (v459 | v363);
	const char v766 = (char) (~(v341 | v396));
	const char v767 = (char) (~v432 | v280);
	const char v768 = (char) (~v329);
	const char v769 = (char) (~(v362 ^ v495));
	const char v770 = (char) (~(v418 ^ v543));
	const char v771 = (char) (~v519);
	const char v772 = (char) (v435 ^ v387);
	const char v773 = (char) (v414);
	const char v774 = (char) (~v407);
	const char v775 = (char) (~v348 | v453);
	const char v776 = (char) (v480 ^ v281);
	const char v777 = (char) (~v301);
	const char v778 = (char) (~v504);
	const char v779 = (char) (~(v444 & v525));
	const char v780 = (char) (~v321 | v334);
	const char v781 = (char) (~(v448 ^ v383));
	const char v782 = (char) (v316);
	const char v783 = (char) (~(v386 ^ v547));
	const char v784 = (char) (v556 & ~v355);
	const char v785 = (char) (~v315);
	const char v786 = (char) (v357);
	const char v787 = (char) (~(v338 & v418));
	const char v788 = (char) (v376 | v393);
	const char v789 = (char) (v323 & v382);
	const char v790 = (char) (~v367);
	const char v791 = (char) (v409 ^ v539);
	const char v792 = (char) (v490 ^ v319);
	const char v793 = (char) (v524 & ~v287);
	const char v794 = (char) (~v419 | v534);
	const char v795 = (char) (v541 ^ v377);
	const char v796 = (char) (~v509 | v476);
	const char v797 = (char) (v284 & v445);
	const char v798 = (char) (~(v353 | v475));
	const char v799 = (char) (v329 & v332);
	const char v800 = (char) (v313 & v458);
	const char v801 = (char) (v558 & ~v331);
	const char v802 = (char) (v454);
	const char v803 = (char) (~(v489 ^ v552));
	const char v804 = (char) (v356 | v378);
	const char v805 = (char) (v463);
	const char v806 = (char) (~(v409 ^ v427));
	const char v807 = (char) (v390 ^ v466);
	const char v808 = (char) (v319);
	const char v809 = (char) (v497 ^ v493);
	const char v810 = (char) (~(v438 ^ v292));
	const char v811 = (char) (v328);
	const char v812 = (char) (v292);
	const char v813 = (char) (v405 & v473);
	const char v814 = (char) (v392);
	const char v815 = (char) (~v521);
	const char v816 = (char) (v288 ^ v316);
	const char v817 = (char) (~v323 | v538);
	const char v818 = (char) (~v380);
	const char v819 = (char) (~(v527 | v376));
	const char v820 = (char) (~(v434 ^ v311));
	const char v821 = (char) (~v411 | v550);
	const char v822 = (char) (~v538);
	const char v823 = (char) (v332 & v411);
	const char v824 = (char) (~v359 | v477);
	const char v825 = (char) (v531 & v360);
	const char v826 = (char) (~(v347 ^ v441));
	const char v827 = (char) (~v489);
	const char v828 = (char) (~v324);
	const char v829 = (char) (v391 ^ v286);
	const char v830 = (char) (~v397);
	const char v831 = (char) (v503 ^ v379);
	const char v832 = (char) (v374 & ~v401);
	const char v833 = (char) (v366 ^ v307);
	const char v834 = (char) (~v507);
	const char v835 = (char) (~v383);
	const char v836 = (char) (~(v327 ^ v454));
	const char v837 = (char) (~v414 | v426);
	const char v838 = (char) (v381 ^ v554);
	const char v839 = (char) (v459 & v413);
	out[0] = (char) (~(v592 & v745));
	out[1] = (char) (v608 & v609);
	out[2] = (char) (~v681 | v820);
	out[3] = (char) (v796);
	out[4] = (char) (v814 & v631);
	out[5] = (char) (~(v609 ^ v826));
	out[6] = (char) (~(v682 & v752));
	out[7] = (char) (~(v624 ^ v679));
	out[8] = (char) (~v628);
	out[9] = (char) (~(v693 ^ v703));
	out[10] = (char) (~(v615 ^ v596));
	out[11] = (char) (v589 & ~v699);
	out[12] = (char) (~(v763 ^ v613));
	out[13] = (char) (v654 | v804);
	out[14] = (char) (~v764 | v792);
	out[15] = (char) (~v619);
	out[16] = (char) (v810 ^ v734);
	out[17] = (char) (v610);
	out[18] = (char) (~(char) 0);
	out[19] = (char) (~(char) 0);
	out[20] = (char) (v730 & ~v620);
	out[21] = (char) (v744 ^ v739);
	out[22] = (char) (v807);
	out[23] = (char) (v598 & ~v723);
	out[24] = (char) (v765 & ~v827);
	out[25] = (char) (v606);
	out[26] = (char) (v648);
	out[27] = (char) (v774 & ~v664);
	out[28] = (char) (~(v627 ^ v680));
	out[29] = (char) (~v586);
	out[30] = (char) (v624 & v574);
	out[31] = (char) (v646 | v791);
	out[32] = (char) (v635);
	out[33] = (char) (v837 & v585);
	out[34] = (char) (v812 & ~v760);
	out[35] = (char) (~(v706 | v583));
	out[36] = (char) (~(v604 | v687));
	out[37] = (char) (v625 ^ v762);
	out[38] = (char) (~(v815 ^ v742));
	out[39] = (char) (v837);
	out[40] = (char) (v800 ^ v671);
	out[41] = (char) (~(v697 ^ v748));
	out[42] = (char) (~(v600 ^ v711));
	out[43] = (char) (v672 & ~v784);
	out[44] = (char) (v650);
	out[45] = (char) (v722 | v644);
	out[46] = (char) (~v778 | v830);
	out[47] = (char) (~(v772 & v839));
	out[48] = (char) (v786);
	out[49] = (char) (~v637);
	out[50] = (char) (~v798);
	out[51] = (char) (v738 ^ v626);
	out[52] = (char) (v600 ^ v803);
	out[53] = (char) (~v655);
	out[54] = (char) (~v583);
	out[55] = (char) (~v723 | v740);
	out[56] = (char) (~v593);
	out[57] = (char) (~v806 | v749);
	out[58] = (char) (~v571 | v580);
	out[59] = (char) (v819 | v628);
	out[60] = (char) (v760 | v661);
	out[61] = (char) (~v802);
	out[62] = (char) (~(v800 ^ v607));
	out[63] = (char) (~(v572 ^ v688));
	out[64] = (char) (v654);
	out[65] = (char) (~v724);
	out[66] = (char) (v673 ^ v737);
	out[67] = (char) (~v672);
	out[68] = (char) (~v755);
	out[69] = (char) (~v678);
	out[70] = (char) (v705 & ~v706);
	out[71] = (char) (~v818);
	out[72] = (char) (v796 ^ v804);
	out[73] = (char) (v659 & ~v580);
	out[74] = (char) (v656 ^ v675);
	out[75] = (char) (v689 & ~v829);
	out[76] = (char) (v780 & v821);
	out[77] = (char) (v781 & ~v805);
	out[78] = (char) (v667);
	out[79] = (char) (~(v695 ^ v753));
	out[80] = (char) (~(v750 & v590));
	out[81] = (char) (v808 & v576);
	out[82] = (char) (~(v649 ^ v632));
	out[83] = (char) (~(v784 ^ v735));
	out[84] = (char) (v698 & v829);
	out[85] = (char) (~(v697 ^ v687));
	out[86] = (char) (v775 | v832);
	out[87] = (char) (v669 & ~v733);
	out[88] = (char) (v578);
	out[89] = (char) (v694 ^ v817);
	out[90] = (char) (v663 & ~v660);
	out[91] = (char) (~(v618 ^ v682));
	out[92] = (char) (~v618);
	out[93] = (char) (~v785 | v801);
	out[94] = (char) (~(v789 & v617));
	out[95] = (char) (v776 ^ v828);
	out[96] = (char) (v813 ^ v794);
	out[97] = (char) (v686 & v725);
	out[98] = (char) (v587 | v781);
	out[99] = (char) (~(v599 | v639));
	out[100] = (char) (~(v665 ^ v803));
	out[101] = (char) (~v798);
	out[102] = (char) (v717 & ~v835);
	out[103] = (char) (v834 & v605);
	out[104] = (char) (~(v700 & v799));
	out[105] = (char) (v758 ^ v595);
	out[106] = (char) (~v633);
	out[107] = (char) (v755 ^ v634);
	out[108] = (char) (~v763);
	out[109] = (char) (v811 & ~v691);
	out[110] = (char) (v685 & v833);
	out[111] = (char) (~(v575 | v691));
	out[112] = (char) (v750 ^ v577);
	out[113] = (char) (v794 | v696);
	out[114] = (char) (~v677);
	out[115] = (char) (v614 & v594);
	out[116] = (char) (~v702);
	out[117] = (char) (~(v630 | v813));
	out[118] = (char) (v751 & ~v692);
	out[119] = (char) (~(v714 ^ v575));
	out[120] = (char) (v612 ^ v709);
	out[121] = (char) (v757);
	out[122] = (char) (~(v808 ^ v573));
	out[123] = (char) (v716 & v761);
	out[124] = (char) (~v649 | v602);
	out[125] = (char) (~(v746 & v783));
	out[126] = (char) (~v692);
	out[127] = (char) (~v835 | v690);
	out[128] = (char) (v787 & ~v653);
	out[129] = (char) (~(v658 ^ v592));
	out[130] = (char) (v766 | v822);
	out[131] = (char) (v660 ^ v588);
	out[132] = (char) (~v674 | v611);
	out[133] = (char) (v566 | v815);
	out[134] = (char) (~(v699 | v601));
	out[135] = (char) (v565 ^ v838);
	out[136] = (char) (~(v720 & v704));
	out[137] = (char) (v819);
	out[138] = (char) (v785 & ~v778);
	out[139] = (char) (~v831);
	out[140] = (char) (~(v756 ^ v779));
	out[141] = (char) (v774);
	out[142] = (char) (~v729 | v703);
	out[143] = (char) (~(v629 ^ v735));
	out[144] = (char) (~v601);
	out[145] = (char) (v736 & v615);
	out[146] = (char) (v824 & v647);
	out[147] = (char) (v596 & ~v648);
	out[148] = (char) (~(v686 ^ v621));
	out[149] = (char) (v719 ^ v603);
	out[150] = (char) (~v790);
	out[151] = (char) (~v620 | v810);
	out[152] = (char) (v621 | v816);
	out[153] = (char) (v569 ^ v644);
	out[154] = (char) (v670);
	out[155] = (char) (v830 & ~v821);
	out[156] = (char) (v560);
	out[157] = (char) (~v566 | v611);
	out[158] = (char) (v709);
	out[159] = (char) (v584);
	out[160] = (char) (~(v591 ^ v612));
	out[161] = (char) (~(v836 ^ v562));
	out[162] = (char) (~(char) 0);
	out[163] = (char) (~v738);
	out[164] = (char) (v605);
	out[165] = (char) (~(v688 | v733));
	out[166] = (char) (v818);
	out[167] = (char) (~v563 | v639);
	out[168] = (char) (~v722);
	out[169] = (char) ((char) 0);
	out[170] = (char) (~v727 | v647);
	out[171] = (char) (v773);
	out[172] = (char) (v725 ^ v584);
	out[173] = (char) (v659 | v678);
	out[174] = (char) (~v635 | v771);
	out[175] = (char) (v715 & ~v636);
	out[176] = (char) (~(char) 0);
	out[177] = (char) (~v789);
	out[178] = (char) (~(v567 | v693));
	out[179] = (char) (~(v663 & v619));
	out[180] = (char) (v806 & ~v776);
	out[181] = (char) (~(v587 ^ v754));
	out[182] = (char) (~(v668 & v771));
	out[183] = (char) (v576);
	out[184] = (char) (~v616);
	out[185] = (char) (v770 & v607);
	out[186] = (char) (~(v714 | v581));
	out[187] = (char) (v604);
	out[188] = (char) (v701 & ~v623);
	out[189] = (char) (~v631 | v638);
	out[190] = (char) (v569 & ~v681);
	out[191] = (char) (v568);
	out[192] = (char) (v762 & v679);
	out[193] = (char) (~(v711 ^ v700));
	out[194] = (char) (v729);
	out[195] = (char) (v824 ^ v657);
	out[196] = (char) (~(v673 | v827));
	out[197] = (char) (~v581);
	out[198] = (char) (v822 & ~v777);
	out[199] = (char) (~(v811 ^ v825));
	out[200] = (char) (~v721);
	out[201] = (char) (v567 | v773);
	out[202] = (char) (v561 & ~v674);
	out[203] = (char) (~(v668 ^ v636));
	out[204] = (char) (v767);
	out[205] = (char) (~(v641 & v752));
	out[206] = (char) (v809);
	out[207] = (char) (v560);
	out[208] = (char) (~(v745 & v613));
	out[209] = (char) (~(v783 & v653));
	out[210] = (char) (~v746 | v831);
	out[211] = (char) (~(v633 ^ v740));
	out[212] = (char) (~(v743 ^ v579));
	out[213] = (char) (~(v764 ^ v715));
	out[214] = (char) (v826 ^ v753);
	out[215] = (char) (v642 ^ v677);
	out[216] = (char) (~v797 | v662);
	out[217] = (char) (~v817 | v666);
	out[218] = (char) (~(v634 ^ v724));
	out[219] = (char) (~v642 | v793);
	out[220] = (char) (~(v622 ^ v652));
	out[221] = (char) (v590);
	out[222] = (char) (~(v816 ^ v690));
	out[223] = (char) (~(v696 | v701));
	out[224] = (char) (v683 ^ v666);
	out[225] = (char) (v741 ^ v623);
	out[226] = (char) (~(v680 | v698));
	out[227] = (char) (v759 ^ v791);
	out[228] = (char) (v726 | v726);
	out[229] = (char) (v739 & ~v685);
	out[230] = (char) (v717 ^ v780);
	out[231] = (char) (v574 ^ v749);
	out[232] = (char) (v712 & ~v768);
	out[233] = (char) (v792 ^ v564);
	out[234] = (char) (v770);
	out[235] = (char) (v713 ^ v570);
	out[236] = (char) (v805 & ~v838);
	out[237] = (char) (~v708);
	out[238] = (char) (~v718);
	out[239] = (char) (v720);
	out[240] = (char) (v684 & v747);
	out[241] = (char) (~(v728 ^ v602));
	out[242] = (char) (~v676);
	out[243] = (char) (~v665 | v787);
	out[244] = (char) (~(v641 & v593));
	out[245] = (char) (v809);
	out[246] = (char) (~v669 | v610);
	out[247] = (char) (v561);
	out[248] = (char) (~(v594 ^ v769));
	out[249] = (char) (~(v707 ^ v834));
	out[250] = (char) (~v598 | v645);
	out[251] = (char) (v563 ^ v823);
	out[252] = (char) (v704);
	out[253] = (char) (~v632 | v656);
	out[254] = (char) (v606 | v622);
	out[255] = (char) (v769);
	out[256] = (char) (~(v710 ^ v597));
	out[257] = (char) (~(v572 & v779));
	out[258] = (char) (~v608);
	out[259] = (char) (v823 ^ v671);
	out[260] = (char) (~v747);
	out[261] = (char) (v797);
	out[262] = (char) (~(v640 | v737));
	out[263] = (char) (v732 ^ v731);
	out[264] = (char) (~v708);
	out[265] = (char) (~v637);
	out[266] = (char) (v571);
	out[267] = (char) (v795 ^ v734);
	out[268] = (char) (v643);
	out[269] = (char) (~(v675 ^ v582));
	out[270] = (char) (v650 ^ v638);
	out[271] = (char) (~(v651 | v786));
	out[272] = (char) (~(v836 ^ v828));
	out[273] = (char) (~(v814 | v625));
	out[274] = (char) (v713);
	out[275] = (char) (v664 ^ v731);
	out[276] = (char) (~(v833 ^ v728));
	out[277] = (char) (~(v801 ^ v768));
	out[278] = (char) (v782 ^ v790);
	out[279] = (char) (v579 & ~v795);
}

// void apply_logic_gate_net (bool const *inp, int *out, size_t len) {
//     char *inp_temp = malloc(32*sizeof(char));
//     char *out_temp = malloc(280*sizeof(char));
//     char *out_temp_o = malloc(5*sizeof(char));
    
//     for(size_t i = 0; i < len; ++i) {
    
//         // Converting the bool array into a bitpacked array
//         for(size_t d = 0; d < 32; ++d) {
//             char res = (char) 0;
//             for(size_t b = 0; b < 8; ++b) {
//                 res <<= 1;
//                 res += !!(inp[i * 32 * 8 + (8 - b - 1) * 32 + d]);
//             }
//             inp_temp[d] = res;
//         }
    
//         // Applying the logic gate net
//         logic_gate_net(inp_temp, out_temp);
        
//         // GroupSum of the results via logic gate networks
//         for(size_t c = 0; c < 14; ++c) {  // for each class
//             // Initialize the output bits
//             for(size_t d = 0; d < 5; ++d) {
//                 out_temp_o[d] = (char) 0;
//             }
            
//             // Apply the adder logic gate network
//             for(size_t a = 0; a < 20; ++a) {
//                 char carry = out_temp[c * 20 + a];
//                 char out_temp_o_d;
//                 for(int d = 5 - 1; d >= 0; --d) {
//                     out_temp_o_d  = out_temp_o[d];
//                     out_temp_o[d] = carry ^ out_temp_o_d;
//                     carry         = carry & out_temp_o_d;
//                 }
//             }
            
//             // Unpack the result bits
//             for(size_t b = 0; b < 8; ++b) {
//                 const char bit_mask = (char) 1 << b;
//                 int res = 0;
//                 for(size_t d = 0; d < 5; ++d) {
//                     res <<= 1;
//                     res += !!(out_temp_o[d] & bit_mask);
//                 }
//                 out[(i * 8 + b) * 14 + c] = res;
//             }
//         }
//     }
//     free(inp_temp);
//     free(out_temp);
//     free(out_temp_o);
// }

void apply_logic_gate_net_singleval (char const *inp, int *out) {
    char *out_temp = (char*)malloc(280*sizeof(char));
    
    // for(size_t i = 0; i < len; ++i) {
    
        // Applying the logic gate net
        logic_gate_net(inp, out_temp);
				const int classSize = 280/20;

        for(size_t c = 0; c < 14; ++c) {  // for each class
					int classSum = 0;
					for(size_t node=c*classSize; node < (c*classSize) + classSize; node++) {
						classSum += out_temp[node] & 1; //take the lowest bit, ignore the rest	
					}
					out[c] = classSum;
				}
        
    // }
    free(out_temp);
}

