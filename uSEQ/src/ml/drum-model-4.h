#include <stddef.h>
#include <stdlib.h>
#include <stdbool.h>

void logic_gate_net(char const *inp, char *out) {
	const char v0 = (char) (inp[20]);
	const char v1 = (char) (inp[17] ^ inp[6]);
	const char v2 = (char) (inp[15]);
	const char v3 = (char) (inp[17]);
	const char v4 = (char) (inp[21]);
	const char v5 = (char) (inp[2] & ~inp[2]);
	const char v6 = (char) (~inp[3]);
	const char v7 = (char) (inp[9]);
	const char v8 = (char) (inp[31]);
	const char v9 = (char) (inp[22] & ~inp[19]);
	const char v10 = (char) (inp[31]);
	const char v11 = (char) (~(char) 0);
	const char v12 = (char) (inp[31] & inp[31]);
	const char v13 = (char) (~(inp[5] ^ inp[26]));
	const char v14 = (char) (inp[15]);
	const char v15 = (char) (inp[13]);
	const char v16 = (char) (inp[6] ^ inp[30]);
	const char v17 = (char) (inp[24] ^ inp[14]);
	const char v18 = (char) (~inp[31]);
	const char v19 = (char) (~inp[16] | inp[29]);
	const char v20 = (char) (inp[7] & inp[16]);
	const char v21 = (char) (~inp[7]);
	const char v22 = (char) (inp[8]);
	const char v23 = (char) (inp[20] & ~inp[13]);
	const char v24 = (char) (inp[8] & ~inp[4]);
	const char v25 = (char) (inp[31] ^ inp[24]);
	const char v26 = (char) (~(inp[5] ^ inp[28]));
	const char v27 = (char) (~inp[23] | inp[15]);
	const char v28 = (char) (inp[30]);
	const char v29 = (char) (~inp[4]);
	const char v30 = (char) (inp[10] & ~inp[19]);
	const char v31 = (char) (inp[2] | inp[8]);
	const char v32 = (char) (inp[15]);
	const char v33 = (char) (~inp[29] | inp[11]);
	const char v34 = (char) (inp[8]);
	const char v35 = (char) (~(inp[17] | inp[9]));
	const char v36 = (char) (inp[23]);
	const char v37 = (char) (~inp[10] | inp[21]);
	const char v38 = (char) (inp[4]);
	const char v39 = (char) (~(inp[1] | inp[15]));
	const char v40 = (char) (~inp[15]);
	const char v41 = (char) ((char) 0);
	const char v42 = (char) (~inp[28]);
	const char v43 = (char) (~inp[26] | inp[1]);
	const char v44 = (char) (~inp[12] | inp[1]);
	const char v45 = (char) (inp[15] & ~inp[12]);
	const char v46 = (char) (~(inp[9] & inp[30]));
	const char v47 = (char) (~inp[14] | inp[26]);
	const char v48 = (char) (inp[20] | inp[5]);
	const char v49 = (char) (inp[12] ^ inp[9]);
	const char v50 = (char) (~inp[15] | inp[11]);
	const char v51 = (char) (inp[23] ^ inp[29]);
	const char v52 = (char) (~(inp[20] | inp[0]));
	const char v53 = (char) (inp[14] & ~inp[0]);
	const char v54 = (char) (inp[22] ^ inp[6]);
	const char v55 = (char) (inp[0] & ~inp[19]);
	const char v56 = (char) (inp[5] ^ inp[10]);
	const char v57 = (char) (~(inp[9] ^ inp[30]));
	const char v58 = (char) (inp[30] | inp[22]);
	const char v59 = (char) (inp[4]);
	const char v60 = (char) (~inp[28] | inp[21]);
	const char v61 = (char) (inp[7] ^ inp[15]);
	const char v62 = (char) (~(inp[12] ^ inp[0]));
	const char v63 = (char) (inp[30]);
	const char v64 = (char) (inp[16] | inp[18]);
	const char v65 = (char) (inp[20] & ~inp[18]);
	const char v66 = (char) (~(inp[7] & inp[28]));
	const char v67 = (char) (~inp[24]);
	const char v68 = (char) (~(inp[27] ^ inp[29]));
	const char v69 = (char) (~(inp[21] | inp[6]));
	const char v70 = (char) (inp[21] ^ inp[18]);
	const char v71 = (char) (~inp[4] | inp[13]);
	const char v72 = (char) (inp[22]);
	const char v73 = (char) (~inp[28]);
	const char v74 = (char) (inp[3] | inp[19]);
	const char v75 = (char) (~(inp[2] & inp[20]));
	const char v76 = (char) (~inp[13] | inp[26]);
	const char v77 = (char) (inp[28] & inp[10]);
	const char v78 = (char) (inp[8]);
	const char v79 = (char) (~inp[4]);
	const char v80 = (char) ((char) 0);
	const char v81 = (char) (inp[1] & inp[11]);
	const char v82 = (char) (inp[29] | inp[13]);
	const char v83 = (char) (~(inp[20] & inp[26]));
	const char v84 = (char) (inp[23] & ~inp[9]);
	const char v85 = (char) (~inp[27]);
	const char v86 = (char) (~(inp[25] ^ inp[9]));
	const char v87 = (char) (~(inp[24] & inp[2]));
	const char v88 = (char) (~(inp[23] ^ inp[26]));
	const char v89 = (char) (~(char) 0);
	const char v90 = (char) (~inp[25]);
	const char v91 = (char) (~inp[11]);
	const char v92 = (char) (inp[27] & ~inp[17]);
	const char v93 = (char) (~inp[26]);
	const char v94 = (char) (inp[16] ^ inp[27]);
	const char v95 = (char) (inp[12] ^ inp[12]);
	const char v96 = (char) (inp[3] & inp[16]);
	const char v97 = (char) (~inp[31]);
	const char v98 = (char) (inp[28] ^ inp[20]);
	const char v99 = (char) (~inp[21] | inp[5]);
	const char v100 = (char) (~inp[20]);
	const char v101 = (char) (inp[10] | inp[22]);
	const char v102 = (char) (~inp[26]);
	const char v103 = (char) (~(inp[2] & inp[9]));
	const char v104 = (char) (~inp[4]);
	const char v105 = (char) (~inp[31]);
	const char v106 = (char) (~inp[25]);
	const char v107 = (char) (~inp[0]);
	const char v108 = (char) (~(inp[11] | inp[0]));
	const char v109 = (char) (~inp[17]);
	const char v110 = (char) (~(inp[13] & inp[13]));
	const char v111 = (char) (~(char) 0);
	const char v112 = (char) (~inp[28] | inp[1]);
	const char v113 = (char) (inp[4] & ~inp[3]);
	const char v114 = (char) (~(inp[18] | inp[10]));
	const char v115 = (char) (inp[12]);
	const char v116 = (char) (inp[14] ^ inp[29]);
	const char v117 = (char) (inp[11]);
	const char v118 = (char) (inp[11] & ~inp[0]);
	const char v119 = (char) (inp[8] & ~inp[13]);
	const char v120 = (char) (~(inp[3] ^ inp[24]));
	const char v121 = (char) (~(inp[25] & inp[25]));
	const char v122 = (char) (~inp[16] | inp[7]);
	const char v123 = (char) (~inp[14] | inp[3]);
	const char v124 = (char) (~(inp[25] ^ inp[5]));
	const char v125 = (char) (inp[22] | inp[27]);
	const char v126 = (char) (~(inp[1] & inp[17]));
	const char v127 = (char) (~(inp[25] ^ inp[14]));
	const char v128 = (char) (~(inp[22] ^ inp[27]));
	const char v129 = (char) (inp[6]);
	const char v130 = (char) (~(inp[1] & inp[10]));
	const char v131 = (char) (~inp[17] | inp[22]);
	const char v132 = (char) (~inp[25] | inp[18]);
	const char v133 = (char) (~inp[18]);
	const char v134 = (char) (inp[22]);
	const char v135 = (char) (~(inp[29] | inp[0]));
	const char v136 = (char) (~inp[3]);
	const char v137 = (char) (inp[25] ^ inp[5]);
	const char v138 = (char) (inp[16]);
	const char v139 = (char) (inp[8]);
	const char v140 = (char) (v7);
	const char v141 = (char) (~v104);
	const char v142 = (char) (~v55);
	const char v143 = (char) (v25 ^ v31);
	const char v144 = (char) (v85 | v78);
	const char v145 = (char) (~v127);
	const char v146 = (char) (~v71 | v94);
	const char v147 = (char) (v88 & v16);
	const char v148 = (char) (~v38);
	const char v149 = (char) (~(v117 ^ v49));
	const char v150 = (char) (~(v134 & v97));
	const char v151 = (char) (v75 & ~v126);
	const char v152 = (char) (v110 & ~v14);
	const char v153 = (char) (~(v42 ^ v46));
	const char v154 = (char) (~v130 | v68);
	const char v155 = (char) (~v98);
	const char v156 = (char) (~v97 | v106);
	const char v157 = (char) (~v58);
	const char v158 = (char) (v88);
	const char v159 = (char) (~v5 | v89);
	const char v160 = (char) (~v77);
	const char v161 = (char) (v91 & ~v28);
	const char v162 = (char) (~(v125 | v113));
	const char v163 = (char) (v107 & ~v59);
	const char v164 = (char) (v64 & ~v60);
	const char v165 = (char) (v10 & v84);
	const char v166 = (char) (v93);
	const char v167 = (char) (v122 & ~v32);
	const char v168 = (char) (v136 & v37);
	const char v169 = (char) (v113 ^ v31);
	const char v170 = (char) (~v90);
	const char v171 = (char) (~v42);
	const char v172 = (char) (~(v52 | v123));
	const char v173 = (char) (v8 & ~v95);
	const char v174 = (char) (~(char) 0);
	const char v175 = (char) (v68);
	const char v176 = (char) (~(v109 ^ v30));
	const char v177 = (char) (v47 | v57);
	const char v178 = (char) (~(v116 ^ v128));
	const char v179 = (char) (v96 & ~v135);
	const char v180 = (char) (~(v83 ^ v24));
	const char v181 = (char) (v18 & ~v131);
	const char v182 = (char) (v61);
	const char v183 = (char) (~v63);
	const char v184 = (char) (v19 & v27);
	const char v185 = (char) (v54);
	const char v186 = (char) (~v15);
	const char v187 = (char) (v74);
	const char v188 = (char) (~v76 | v43);
	const char v189 = (char) (v6 | v95);
	const char v190 = (char) (v17);
	const char v191 = (char) (v38 & ~v80);
	const char v192 = (char) (~(v92 | v131));
	const char v193 = (char) (~(v5 & v47));
	const char v194 = (char) (~v18);
	const char v195 = (char) (~(v92 ^ v98));
	const char v196 = (char) (v128);
	const char v197 = (char) (v118 & ~v74);
	const char v198 = (char) (v104);
	const char v199 = (char) (~(v116 | v22));
	const char v200 = (char) (v67 & v114);
	const char v201 = (char) (v111 & ~v33);
	const char v202 = (char) (~(char) 0);
	const char v203 = (char) (~v34 | v56);
	const char v204 = (char) (~v9 | v133);
	const char v205 = (char) (v86 | v134);
	const char v206 = (char) (~v56);
	const char v207 = (char) (~v61);
	const char v208 = (char) (~v44);
	const char v209 = (char) (v112 & v81);
	const char v210 = (char) (v73 & ~v23);
	const char v211 = (char) (v91 ^ v9);
	const char v212 = (char) (v101);
	const char v213 = (char) (~v112);
	const char v214 = (char) (~v46 | v137);
	const char v215 = (char) (~v50);
	const char v216 = (char) (v2 & ~v36);
	const char v217 = (char) (v27 | v109);
	const char v218 = (char) (v120 & ~v12);
	const char v219 = (char) (v20 & v132);
	const char v220 = (char) (v103 & ~v93);
	const char v221 = (char) (v99 & ~v137);
	const char v222 = (char) (v100 & ~v26);
	const char v223 = (char) (v79);
	const char v224 = (char) (~(v78 | v126));
	const char v225 = (char) (~(v115 & v103));
	const char v226 = (char) (v135 & v39);
	const char v227 = (char) (v136 & ~v107);
	const char v228 = (char) (v102);
	const char v229 = (char) (v3 & v69);
	const char v230 = (char) (v54);
	const char v231 = (char) (~v69);
	const char v232 = (char) (v94 & v132);
	const char v233 = (char) (~v35);
	const char v234 = (char) (~v115 | v19);
	const char v235 = (char) (v48 & v106);
	const char v236 = (char) (~v99 | v84);
	const char v237 = (char) (~v4);
	const char v238 = (char) (v8 & v82);
	const char v239 = (char) (~(char) 0);
	const char v240 = (char) (v0 | v90);
	const char v241 = (char) (~v81 | v16);
	const char v242 = (char) (v26 & v63);
	const char v243 = (char) (~v20 | v41);
	const char v244 = (char) (~v117);
	const char v245 = (char) (v43 | v129);
	const char v246 = (char) (~(v66 & v124));
	const char v247 = (char) (v123);
	const char v248 = (char) (~v11 | v73);
	const char v249 = (char) (~v10);
	const char v250 = (char) (v79);
	const char v251 = (char) (~(v40 ^ v67));
	const char v252 = (char) (v70 & ~v33);
	const char v253 = (char) ((char) 0);
	const char v254 = (char) (v29 & v102);
	const char v255 = (char) (v108 & v86);
	const char v256 = (char) (~v138 | v139);
	const char v257 = (char) (v13 & v51);
	const char v258 = (char) (~(v87 & v100));
	const char v259 = (char) (~(v72 | v120));
	const char v260 = (char) (~(v66 & v44));
	const char v261 = (char) (~(v139 | v53));
	const char v262 = (char) (v75 & v34);
	const char v263 = (char) (v108 | v51);
	const char v264 = (char) (~v64 | v82);
	const char v265 = (char) (~v28);
	const char v266 = (char) (~v133);
	const char v267 = (char) (~v13 | v12);
	const char v268 = (char) (~(v24 ^ v41));
	const char v269 = (char) (v105);
	const char v270 = (char) ((char) 0);
	const char v271 = (char) (v125 & v118);
	const char v272 = (char) (v25 ^ v1);
	const char v273 = (char) (~v29 | v65);
	const char v274 = (char) (v17 | v23);
	const char v275 = (char) ((char) 0);
	const char v276 = (char) (~v21);
	const char v277 = (char) (~(v119 ^ v7));
	const char v278 = (char) (v77 & v45);
	const char v279 = (char) (~v50);
	const char v280 = (char) (~(v211 ^ v164));
	const char v281 = (char) (v159);
	const char v282 = (char) (~v254);
	const char v283 = (char) (~(v200 & v152));
	const char v284 = (char) (v146);
	const char v285 = (char) (v242 & v236);
	const char v286 = (char) (~(v202 | v174));
	const char v287 = (char) (~(v242 & v147));
	const char v288 = (char) (v204 & v160);
	const char v289 = (char) (~v175 | v229);
	const char v290 = (char) (v196 & ~v279);
	const char v291 = (char) (v268 | v279);
	const char v292 = (char) (~(char) 0);
	const char v293 = (char) (~v238 | v253);
	const char v294 = (char) (~v181 | v208);
	const char v295 = (char) (~v271);
	const char v296 = (char) (~(v226 & v182));
	const char v297 = (char) (~v215);
	const char v298 = (char) (~v195);
	const char v299 = (char) (~v157);
	const char v300 = (char) (~(v164 & v222));
	const char v301 = (char) (~v253);
	const char v302 = (char) (v213 ^ v177);
	const char v303 = (char) (~(v207 ^ v148));
	const char v304 = (char) (~(v268 & v270));
	const char v305 = (char) (~v259 | v210);
	const char v306 = (char) (~v154 | v194);
	const char v307 = (char) (~v246 | v227);
	const char v308 = (char) (~v225);
	const char v309 = (char) (~v273);
	const char v310 = (char) ((char) 0);
	const char v311 = (char) (~(v199 | v205));
	const char v312 = (char) (~v269 | v240);
	const char v313 = (char) (~(v189 ^ v244));
	const char v314 = (char) (~v260);
	const char v315 = (char) (~(v207 & v265));
	const char v316 = (char) (v197 | v220);
	const char v317 = (char) (~(v233 & v158));
	const char v318 = (char) (v206 & ~v221);
	const char v319 = (char) (~(v255 | v264));
	const char v320 = (char) ((char) 0);
	const char v321 = (char) (~v145);
	const char v322 = (char) (~(v170 | v272));
	const char v323 = (char) (~(v275 | v199));
	const char v324 = (char) (~(v191 & v179));
	const char v325 = (char) (v203 & ~v143);
	const char v326 = (char) (~(v224 ^ v272));
	const char v327 = (char) (v258 | v190);
	const char v328 = (char) (~(char) 0);
	const char v329 = (char) (~v195 | v275);
	const char v330 = (char) (~(v260 | v278));
	const char v331 = (char) (~(v266 | v167));
	const char v332 = (char) (v245 & ~v235);
	const char v333 = (char) (v227 | v175);
	const char v334 = (char) (v246);
	const char v335 = (char) (~(char) 0);
	const char v336 = (char) (v176 & v252);
	const char v337 = (char) (~(v263 & v169));
	const char v338 = (char) (~v183);
	const char v339 = (char) ((char) 0);
	const char v340 = (char) (~v165 | v245);
	const char v341 = (char) (~(v247 & v168));
	const char v342 = (char) (~(v186 & v264));
	const char v343 = (char) (v226 | v172);
	const char v344 = (char) (v243);
	const char v345 = (char) (v149 & ~v185);
	const char v346 = (char) (v239 & v232);
	const char v347 = (char) (~(char) 0);
	const char v348 = (char) (v218 & v162);
	const char v349 = (char) (~v230 | v234);
	const char v350 = (char) (~v206 | v176);
	const char v351 = (char) ((char) 0);
	const char v352 = (char) ((char) 0);
	const char v353 = (char) (~(v187 & v158));
	const char v354 = (char) (~v271 | v144);
	const char v355 = (char) (v159);
	const char v356 = (char) (v161);
	const char v357 = (char) (v163);
	const char v358 = (char) (v276 ^ v269);
	const char v359 = (char) (v273);
	const char v360 = (char) (~v162 | v274);
	const char v361 = (char) (~(v234 ^ v200));
	const char v362 = (char) (~v265 | v178);
	const char v363 = (char) (~(v172 & v211));
	const char v364 = (char) ((char) 0);
	const char v365 = (char) (~v171);
	const char v366 = (char) (~(v156 & v140));
	const char v367 = (char) (v188 & ~v255);
	const char v368 = (char) (v196 & ~v178);
	const char v369 = (char) (v185 & v238);
	const char v370 = (char) (v180);
	const char v371 = (char) (v221 & ~v141);
	const char v372 = (char) (~v154);
	const char v373 = (char) (v201 & ~v174);
	const char v374 = (char) (~v251 | v224);
	const char v375 = (char) (v230 & ~v201);
	const char v376 = (char) (~(v277 | v181));
	const char v377 = (char) (~v231 | v153);
	const char v378 = (char) (~(v208 | v212));
	const char v379 = (char) (v170);
	const char v380 = (char) (~v191 | v244);
	const char v381 = (char) (~v235 | v257);
	const char v382 = (char) (~(v215 | v192));
	const char v383 = (char) (v240 | v193);
	const char v384 = (char) (~v192 | v184);
	const char v385 = (char) (v252 | v214);
	const char v386 = (char) (~(v262 ^ v197));
	const char v387 = (char) (~(v171 & v209));
	const char v388 = (char) (~v220);
	const char v389 = (char) (v274 | v173);
	const char v390 = (char) (~(char) 0);
	const char v391 = (char) (~(v155 | v216));
	const char v392 = (char) ((char) 0);
	const char v393 = (char) (~(v250 & v243));
	const char v394 = (char) (v219 | v267);
	const char v395 = (char) (v183 | v249);
	const char v396 = (char) (v152 & ~v251);
	const char v397 = (char) (~v223);
	const char v398 = (char) (~v161);
	const char v399 = (char) (v210);
	const char v400 = (char) (v180 & ~v141);
	const char v401 = (char) (~(char) 0);
	const char v402 = (char) (~v276 | v263);
	const char v403 = (char) (~(char) 0);
	const char v404 = (char) (~(char) 0);
	const char v405 = (char) (~(v147 | v157));
	const char v406 = (char) (~(v169 ^ v155));
	const char v407 = (char) (~v248 | v177);
	const char v408 = (char) (v156 & ~v239);
	const char v409 = (char) (v151 | v261);
	const char v410 = (char) (~(char) 0);
	const char v411 = (char) (~v277 | v247);
	const char v412 = (char) (v212 & v150);
	const char v413 = (char) (v236);
	const char v414 = (char) (v228 & v256);
	const char v415 = (char) (v257 & ~v143);
	const char v416 = (char) (v256);
	const char v417 = (char) (v258 | v266);
	const char v418 = (char) (~(v241 & v232));
	const char v419 = (char) (~v189 | v249);
	const char v420 = (char) (~v299);
	const char v421 = (char) (~v364 | v354);
	const char v422 = (char) (v322);
	const char v423 = (char) (v315 & v350);
	const char v424 = (char) (~v280);
	const char v425 = (char) (~(v333 & v303));
	const char v426 = (char) (v324 | v349);
	const char v427 = (char) (~v419 | v389);
	const char v428 = (char) (v321 & ~v403);
	const char v429 = (char) (v285);
	const char v430 = (char) (v301 | v404);
	const char v431 = (char) (v401 & ~v295);
	const char v432 = (char) (~(v336 & v401));
	const char v433 = (char) (v287);
	const char v434 = (char) (~v409);
	const char v435 = (char) (~v288);
	const char v436 = (char) (v384);
	const char v437 = (char) (~(v377 & v331));
	const char v438 = (char) (~v343);
	const char v439 = (char) (~v399 | v378);
	const char v440 = (char) (v317 & v359);
	const char v441 = (char) ((char) 0);
	const char v442 = (char) (~v344);
	const char v443 = (char) (v396 & ~v285);
	const char v444 = (char) (v331 & ~v327);
	const char v445 = (char) (v380 | v402);
	const char v446 = (char) (v406 & ~v390);
	const char v447 = (char) (v318 | v363);
	const char v448 = (char) (v357);
	const char v449 = (char) (~(char) 0);
	const char v450 = (char) (~v344 | v412);
	const char v451 = (char) (v365 & ~v336);
	const char v452 = (char) (~v322 | v317);
	const char v453 = (char) ((char) 0);
	const char v454 = (char) (v303 & v392);
	const char v455 = (char) (~v309 | v300);
	const char v456 = (char) (v414 & ~v316);
	const char v457 = (char) ((char) 0);
	const char v458 = (char) (~(v412 ^ v381));
	const char v459 = (char) (v382 & ~v326);
	const char v460 = (char) (~v306 | v377);
	const char v461 = (char) (v353 ^ v391);
	const char v462 = (char) (v379 & v313);
	const char v463 = (char) (v399 & ~v381);
	const char v464 = (char) (v393 & v330);
	const char v465 = (char) (~(v375 | v294));
	const char v466 = (char) (v362 & v282);
	const char v467 = (char) (~v415);
	const char v468 = (char) (v319);
	const char v469 = (char) (~(v289 & v288));
	const char v470 = (char) (~v312 | v338);
	const char v471 = (char) (v286 & ~v347);
	const char v472 = (char) (v327 & ~v375);
	const char v473 = (char) (v374 | v302);
	const char v474 = (char) (~v320 | v389);
	const char v475 = (char) (~v296 | v341);
	const char v476 = (char) (~v417);
	const char v477 = (char) ((char) 0);
	const char v478 = (char) (~v310 | v361);
	const char v479 = (char) (~v373 | v403);
	const char v480 = (char) (v320 & ~v414);
	const char v481 = (char) ((char) 0);
	const char v482 = (char) (v369 | v382);
	const char v483 = (char) (~(char) 0);
	const char v484 = (char) (~(v309 ^ v356));
	const char v485 = (char) (v384 | v335);
	const char v486 = (char) (v324 & ~v390);
	const char v487 = (char) (v307);
	const char v488 = (char) (v291 | v347);
	const char v489 = (char) (v298 & ~v393);
	const char v490 = (char) (v351 & v335);
	const char v491 = (char) ((char) 0);
	const char v492 = (char) (v415 ^ v379);
	const char v493 = (char) (v402);
	const char v494 = (char) (~v304 | v419);
	const char v495 = (char) (v374 & ~v290);
	const char v496 = (char) (v398 & ~v354);
	const char v497 = (char) (v417 & ~v395);
	const char v498 = (char) (v299);
	const char v499 = (char) (~(v308 & v367));
	const char v500 = (char) (~v345 | v337);
	const char v501 = (char) (~(char) 0);
	const char v502 = (char) (~(v370 ^ v394));
	const char v503 = (char) (v359 ^ v332);
	const char v504 = (char) (v367);
	const char v505 = (char) (v287 | v305);
	const char v506 = (char) (~(char) 0);
	const char v507 = (char) (~v311);
	const char v508 = (char) (v366 ^ v368);
	const char v509 = (char) (~(v411 ^ v283));
	const char v510 = (char) (~v330);
	const char v511 = (char) (v387);
	const char v512 = (char) (~v297);
	const char v513 = (char) (v342 | v371);
	const char v514 = (char) ((char) 0);
	const char v515 = (char) (v314);
	const char v516 = (char) (v400 | v406);
	const char v517 = (char) ((char) 0);
	const char v518 = (char) (~(v329 & v292));
	const char v519 = (char) (v371);
	const char v520 = (char) (~(char) 0);
	const char v521 = (char) (~v385);
	const char v522 = (char) (v307 | v418);
	const char v523 = (char) (~(v323 | v363));
	const char v524 = (char) (v358 | v348);
	const char v525 = (char) (v365 & v416);
	const char v526 = (char) (v388 & v395);
	const char v527 = (char) (~(char) 0);
	const char v528 = (char) (v342);
	const char v529 = (char) (~v338 | v376);
	const char v530 = (char) (~(char) 0);
	const char v531 = (char) (~(char) 0);
	const char v532 = (char) (~v298 | v350);
	const char v533 = (char) (v385 & ~v339);
	const char v534 = (char) ((char) 0);
	const char v535 = (char) (~v295);
	const char v536 = (char) (v286 | v411);
	const char v537 = (char) (~(v316 & v397));
	const char v538 = (char) (v292);
	const char v539 = (char) (~v329 | v291);
	const char v540 = (char) (v293);
	const char v541 = (char) (~v386);
	const char v542 = (char) (v391 | v340);
	const char v543 = (char) (v372);
	const char v544 = (char) (~v373);
	const char v545 = (char) (v416 & v296);
	const char v546 = (char) (v349 & ~v321);
	const char v547 = (char) (~v310 | v343);
	const char v548 = (char) (~(v280 | v306));
	const char v549 = (char) ((char) 0);
	const char v550 = (char) (v356 & ~v409);
	const char v551 = (char) (v284 & ~v326);
	const char v552 = (char) (v325 & ~v413);
	const char v553 = (char) (v355 & ~v360);
	const char v554 = (char) (v315 & ~v302);
	const char v555 = (char) (~(v418 ^ v339));
	const char v556 = (char) (v334 | v346);
	const char v557 = (char) (~v372);
	const char v558 = (char) (v361 & ~v314);
	const char v559 = (char) (~v281 | v282);
	const char v560 = (char) (~v527);
	const char v561 = (char) (~v455);
	const char v562 = (char) (~(v424 & v533));
	const char v563 = (char) (v432);
	const char v564 = (char) (v508 | v465);
	const char v565 = (char) (v507 | v463);
	const char v566 = (char) (~(v439 ^ v462));
	const char v567 = (char) (v546 ^ v467);
	const char v568 = (char) (v483 ^ v504);
	const char v569 = (char) (v511 | v555);
	const char v570 = (char) (~(v441 & v442));
	const char v571 = (char) ((char) 0);
	const char v572 = (char) (~v454 | v506);
	const char v573 = (char) (v470 & ~v510);
	const char v574 = (char) (v516 & ~v483);
	const char v575 = (char) (~v461);
	const char v576 = (char) (v451 & ~v551);
	const char v577 = (char) (v555);
	const char v578 = (char) (v427);
	const char v579 = (char) (~(v514 & v525));
	const char v580 = (char) (~(v463 | v502));
	const char v581 = (char) (v439 & ~v450);
	const char v582 = (char) (v460 & ~v442);
	const char v583 = (char) (v488);
	const char v584 = (char) (v450);
	const char v585 = (char) (~(v536 & v530));
	const char v586 = (char) (~v541 | v423);
	const char v587 = (char) (~(v420 ^ v550));
	const char v588 = (char) (~v428 | v524);
	const char v589 = (char) (v495 & v482);
	const char v590 = (char) (~v550);
	const char v591 = (char) (~(v513 | v547));
	const char v592 = (char) (v536 | v468);
	const char v593 = (char) (v553 | v433);
	const char v594 = (char) (v522 ^ v452);
	const char v595 = (char) (v485 & ~v511);
	const char v596 = (char) (~(v431 & v431));
	const char v597 = (char) (v435 & ~v558);
	const char v598 = (char) (v500 & v545);
	const char v599 = (char) (v559 & v552);
	const char v600 = (char) (~(v517 & v481));
	const char v601 = (char) (~v469);
	const char v602 = (char) (~(char) 0);
	const char v603 = (char) (v426 | v537);
	const char v604 = (char) (~(char) 0);
	const char v605 = (char) (~v490 | v531);
	const char v606 = (char) (v421);
	const char v607 = (char) (v477 & ~v449);
	const char v608 = (char) ((char) 0);
	const char v609 = (char) ((char) 0);
	const char v610 = (char) (v491);
	const char v611 = (char) (~v516 | v499);
	const char v612 = (char) (v444 | v492);
	const char v613 = (char) (~(v477 | v498));
	const char v614 = (char) (~(v496 ^ v519));
	const char v615 = (char) (v440);
	const char v616 = (char) ((char) 0);
	const char v617 = (char) (v549 & ~v465);
	const char v618 = (char) (v535 | v438);
	const char v619 = (char) ((char) 0);
	const char v620 = (char) (~v521 | v554);
	const char v621 = (char) (v424 & ~v532);
	const char v622 = (char) (~v506 | v522);
	const char v623 = (char) (~v468 | v520);
	const char v624 = (char) (~v492);
	const char v625 = (char) (v542 | v528);
	const char v626 = (char) (~(char) 0);
	const char v627 = (char) (~(v435 | v482));
	const char v628 = (char) (v507);
	const char v629 = (char) (~v427 | v556);
	const char v630 = (char) (~v437 | v519);
	const char v631 = (char) (v440 & v524);
	const char v632 = (char) (~(v464 | v459));
	const char v633 = (char) (v429 | v521);
	const char v634 = (char) ((char) 0);
	const char v635 = (char) (v500 & ~v512);
	const char v636 = (char) (v458 & ~v552);
	const char v637 = (char) (~(v434 | v502));
	const char v638 = (char) (v531 | v501);
	const char v639 = (char) (~v485);
	const char v640 = (char) ((char) 0);
	const char v641 = (char) (~v488 | v481);
	const char v642 = (char) (~(v472 & v528));
	const char v643 = (char) (v430 & v529);
	const char v644 = (char) (v460);
	const char v645 = (char) (~v501 | v543);
	const char v646 = (char) (~(v447 | v525));
	const char v647 = (char) (~v457 | v527);
	const char v648 = (char) (~(v422 & v484));
	const char v649 = (char) (v436 & ~v557);
	const char v650 = (char) (~(v534 | v458));
	const char v651 = (char) (~(v491 & v517));
	const char v652 = (char) (v487);
	const char v653 = (char) (~v526);
	const char v654 = (char) (v498 | v456);
	const char v655 = (char) (v509);
	const char v656 = (char) (v487);
	const char v657 = (char) (~(char) 0);
	const char v658 = (char) (v548);
	const char v659 = (char) (~v504 | v546);
	const char v660 = (char) (v473 & v453);
	const char v661 = (char) (~v441 | v532);
	const char v662 = (char) (~v539);
	const char v663 = (char) (~v476 | v425);
	const char v664 = (char) (v448 & ~v436);
	const char v665 = (char) ((char) 0);
	const char v666 = (char) (~(v543 | v515));
	const char v667 = (char) (~v526);
	const char v668 = (char) (~v518 | v494);
	const char v669 = (char) (v426);
	const char v670 = (char) (v535 & v479);
	const char v671 = (char) (v420 & ~v434);
	const char v672 = (char) (v462 & v469);
	const char v673 = (char) (~v456);
	const char v674 = (char) (v443);
	const char v675 = (char) (~v433);
	const char v676 = (char) (~v445);
	const char v677 = (char) (v505 & ~v499);
	const char v678 = (char) (v478);
	const char v679 = (char) (v553 & ~v518);
	const char v680 = (char) ((char) 0);
	const char v681 = (char) (v537);
	const char v682 = (char) (v428 & v454);
	const char v683 = (char) ((char) 0);
	const char v684 = (char) (~v551 | v448);
	const char v685 = (char) (v489 & ~v559);
	const char v686 = (char) (v557 & ~v495);
	const char v687 = (char) (~(v540 & v466));
	const char v688 = (char) (v474 | v467);
	const char v689 = (char) (v461 | v533);
	const char v690 = (char) (v547 | v432);
	const char v691 = (char) (~(v493 & v538));
	const char v692 = (char) (v513);
	const char v693 = (char) (v447 & v429);
	const char v694 = (char) (v455 & v489);
	const char v695 = (char) (~v480 | v478);
	const char v696 = (char) (v503 & ~v475);
	const char v697 = (char) (v493 & ~v548);
	const char v698 = (char) (~(v479 | v476));
	const char v699 = (char) (v542 & ~v509);
	const char v700 = (char) (v571);
	const char v701 = (char) (~(v578 | v608));
	const char v702 = (char) (v587 & v586);
	const char v703 = (char) (~(v694 | v659));
	const char v704 = (char) (~v689);
	const char v705 = (char) (v646 & ~v661);
	const char v706 = (char) (v642 & ~v599);
	const char v707 = (char) (v585 & ~v649);
	const char v708 = (char) (~(v604 | v588));
	const char v709 = (char) (~(v612 & v667));
	const char v710 = (char) (~(v649 | v691));
	const char v711 = (char) (~(v593 | v611));
	const char v712 = (char) (v631 & ~v645);
	const char v713 = (char) (~v659 | v654);
	const char v714 = (char) (v684 | v671);
	const char v715 = (char) (v682 & v619);
	const char v716 = (char) (~(v563 ^ v582));
	const char v717 = (char) (v628 & v671);
	const char v718 = (char) (~v685 | v590);
	const char v719 = (char) (~(char) 0);
	const char v720 = (char) (v685 & v609);
	const char v721 = (char) (v677 & ~v596);
	const char v722 = (char) (~v600 | v676);
	const char v723 = (char) (v573 & v663);
	const char v724 = (char) (~v626 | v670);
	const char v725 = (char) (v652 & ~v675);
	const char v726 = (char) (~v615 | v587);
	const char v727 = (char) (~v636);
	const char v728 = (char) (~v597);
	const char v729 = (char) (~v676 | v692);
	const char v730 = (char) (v618 & v601);
	const char v731 = (char) (~(char) 0);
	const char v732 = (char) ((char) 0);
	const char v733 = (char) ((char) 0);
	const char v734 = (char) (v675 ^ v681);
	const char v735 = (char) (~v586);
	const char v736 = (char) ((char) 0);
	const char v737 = (char) (~v658 | v697);
	const char v738 = (char) (~(v641 | v606));
	const char v739 = (char) (v591 & ~v628);
	const char v740 = (char) ((char) 0);
	const char v741 = (char) (~(char) 0);
	const char v742 = (char) (v577);
	const char v743 = (char) (~(v564 | v613));
	const char v744 = (char) (v696 & ~v664);
	const char v745 = (char) (v629 & v584);
	const char v746 = (char) (~(v679 | v639));
	const char v747 = (char) (~v621);
	const char v748 = (char) (v650 & ~v581);
	const char v749 = (char) (~(v605 | v572));
	const char v750 = (char) (v656 & ~v633);
	const char v751 = (char) (~v595 | v657);
	const char v752 = (char) (~(v613 | v562));
	const char v753 = (char) (v614);
	const char v754 = (char) (v683 & ~v604);
	const char v755 = (char) (~(v694 & v622));
	const char v756 = (char) (v635 & ~v687);
	const char v757 = (char) (v698 | v690);
	const char v758 = (char) (~v579 | v689);
	const char v759 = (char) (~(char) 0);
	const char v760 = (char) (v666 & ~v565);
	const char v761 = (char) (v580);
	const char v762 = (char) (~(v673 & v624));
	const char v763 = (char) (~v654);
	const char v764 = (char) (~(char) 0);
	const char v765 = (char) (~(v568 | v625));
	const char v766 = (char) (~v666);
	const char v767 = (char) (~(v620 | v614));
	const char v768 = (char) (~(v600 | v647));
	const char v769 = (char) (~v680 | v570);
	const char v770 = (char) (v696 | v647);
	const char v771 = (char) (v690 & ~v653);
	const char v772 = (char) (v627 & v669);
	const char v773 = (char) (v584 & ~v630);
	const char v774 = (char) (~v589);
	const char v775 = (char) (~(v581 | v622));
	const char v776 = (char) (~v679 | v629);
	const char v777 = (char) (v593 & v643);
	const char v778 = (char) (~v567 | v651);
	const char v779 = (char) (v638);
	const char v780 = (char) (~v656 | v634);
	const char v781 = (char) (v661 | v572);
	const char v782 = (char) (v601);
	const char v783 = (char) (v674 | v578);
	const char v784 = (char) (~(char) 0);
	const char v785 = (char) (v618);
	const char v786 = (char) (~v655 | v566);
	const char v787 = (char) (v606 & ~v562);
	const char v788 = (char) (v633 ^ v602);
	const char v789 = (char) (~v611);
	const char v790 = (char) (~(char) 0);
	const char v791 = (char) (~v619 | v638);
	const char v792 = (char) (v693);
	const char v793 = (char) (v591 & ~v695);
	const char v794 = (char) (v699 & ~v594);
	const char v795 = (char) (~v582);
	const char v796 = (char) (~v645 | v678);
	const char v797 = (char) (~(v585 ^ v563));
	const char v798 = (char) (~v697);
	const char v799 = (char) (~(char) 0);
	const char v800 = (char) (v574 & v637);
	const char v801 = (char) (~(v575 & v569));
	const char v802 = (char) (~v652 | v682);
	const char v803 = (char) (~(v648 & v575));
	const char v804 = (char) (v583 & v616);
	const char v805 = (char) (~(char) 0);
	const char v806 = (char) (~v576);
	const char v807 = (char) (~v607 | v658);
	const char v808 = (char) (~(v669 & v640));
	const char v809 = (char) (~v686);
	const char v810 = (char) (v662 & ~v639);
	const char v811 = (char) (~(char) 0);
	const char v812 = (char) (v637 | v590);
	const char v813 = (char) (~(char) 0);
	const char v814 = (char) (v653 & ~v664);
	const char v815 = (char) (v644 & ~v636);
	const char v816 = (char) (~v612);
	const char v817 = (char) (~v641);
	const char v818 = (char) (~(char) 0);
	const char v819 = (char) (v634);
	const char v820 = (char) (~(v692 | v580));
	const char v821 = (char) (~(v677 ^ v561));
	const char v822 = (char) ((char) 0);
	const char v823 = (char) (v605 & v646);
	const char v824 = (char) (v644);
	const char v825 = (char) (~(v621 & v567));
	const char v826 = (char) (v561 & ~v686);
	const char v827 = (char) (~v594);
	const char v828 = (char) (v665 & ~v592);
	const char v829 = (char) ((char) 0);
	const char v830 = (char) (v589 & ~v573);
	const char v831 = (char) (~v698 | v673);
	const char v832 = (char) (~v592);
	const char v833 = (char) (~v610 | v598);
	const char v834 = (char) (v695 & v576);
	const char v835 = (char) (v579 | v583);
	const char v836 = (char) (v616 & ~v684);
	const char v837 = (char) (v672 | v607);
	const char v838 = (char) (v691 & v560);
	const char v839 = (char) (~v623 | v620);
	const char v840 = (char) (v728 & v702);
	const char v841 = (char) (v807 & ~v751);
	const char v842 = (char) (v753 | v832);
	const char v843 = (char) (~v785);
	const char v844 = (char) (v763 & ~v764);
	const char v845 = (char) (v804);
	const char v846 = (char) (~(v743 | v726));
	const char v847 = (char) (~v779 | v734);
	const char v848 = (char) (v776 & ~v812);
	const char v849 = (char) (~v713 | v730);
	const char v850 = (char) (~(v837 | v818));
	const char v851 = (char) (v828 & v732);
	const char v852 = (char) (v761 & v724);
	const char v853 = (char) (v809);
	const char v854 = (char) (~v751);
	const char v855 = (char) (~v772 | v738);
	const char v856 = (char) (~v803);
	const char v857 = (char) (~v741);
	const char v858 = (char) (v740 & ~v715);
	const char v859 = (char) (~(v734 & v727));
	const char v860 = (char) ((char) 0);
	const char v861 = (char) (v706 | v792);
	const char v862 = (char) (v747 & ~v727);
	const char v863 = (char) (v790 & v834);
	const char v864 = (char) (v772);
	const char v865 = (char) (~(char) 0);
	const char v866 = (char) (~v773 | v729);
	const char v867 = (char) (~(v726 | v701));
	const char v868 = (char) (~v767 | v825);
	const char v869 = (char) (v710);
	const char v870 = (char) (v768);
	const char v871 = (char) (~v815);
	const char v872 = (char) (~v737);
	const char v873 = (char) (~v736 | v769);
	const char v874 = (char) (~(v738 & v822));
	const char v875 = (char) (~(v708 & v723));
	const char v876 = (char) (v798 & v763);
	const char v877 = (char) (~(v774 | v745));
	const char v878 = (char) (v789 & v746);
	const char v879 = (char) (~v819);
	const char v880 = (char) (~v823);
	const char v881 = (char) (v703);
	const char v882 = (char) (v735 & ~v810);
	const char v883 = (char) (~v810 | v802);
	const char v884 = (char) (~(char) 0);
	const char v885 = (char) (v762 & v791);
	const char v886 = (char) (~v720 | v764);
	const char v887 = (char) (~(v799 ^ v770));
	const char v888 = (char) (~v803);
	const char v889 = (char) (v797 & ~v747);
	const char v890 = (char) (v824);
	const char v891 = (char) (v797 & v820);
	const char v892 = (char) ((char) 0);
	const char v893 = (char) (v766 & v839);
	const char v894 = (char) (v832 & ~v722);
	const char v895 = (char) (~v821 | v786);
	const char v896 = (char) (~(v742 & v838));
	const char v897 = (char) (~v756);
	const char v898 = (char) (~(char) 0);
	const char v899 = (char) (~v721 | v766);
	const char v900 = (char) (~v817 | v755);
	const char v901 = (char) (v795 ^ v781);
	const char v902 = (char) (~v814);
	const char v903 = (char) (~v709 | v768);
	const char v904 = (char) ((char) 0);
	const char v905 = (char) (~v704 | v719);
	const char v906 = (char) (~v828);
	const char v907 = (char) (v783);
	const char v908 = (char) (~v704 | v711);
	const char v909 = (char) (~(v836 & v733));
	const char v910 = (char) (~(v708 & v804));
	const char v911 = (char) (v798);
	const char v912 = (char) (~v808 | v745);
	const char v913 = (char) (v835 & ~v743);
	const char v914 = (char) (v827 | v806);
	const char v915 = (char) (v746 & ~v737);
	const char v916 = (char) (~v793);
	const char v917 = (char) (v786 | v787);
	const char v918 = (char) (v834);
	const char v919 = (char) (~v791);
	const char v920 = (char) (~(v756 & v774));
	const char v921 = (char) (v703 & ~v742);
	const char v922 = (char) (v710 & v750);
	const char v923 = (char) (~(v815 & v731));
	const char v924 = (char) (~(char) 0);
	const char v925 = (char) (~v830);
	const char v926 = (char) (~v816 | v730);
	const char v927 = (char) (~v775);
	const char v928 = (char) (~(char) 0);
	const char v929 = (char) (~v744 | v837);
	const char v930 = (char) (v755 & ~v712);
	const char v931 = (char) (v824 | v801);
	const char v932 = (char) (~v767);
	const char v933 = (char) (v801);
	const char v934 = (char) (~v762);
	const char v935 = (char) ((char) 0);
	const char v936 = (char) (~v709);
	const char v937 = (char) (v818 & ~v784);
	const char v938 = (char) (v785 & ~v826);
	const char v939 = (char) (v833 & ~v716);
	const char v940 = (char) (~(char) 0);
	const char v941 = (char) ((char) 0);
	const char v942 = (char) (v796);
	const char v943 = (char) (~(char) 0);
	const char v944 = (char) (v714 & ~v750);
	const char v945 = (char) (~v773);
	const char v946 = (char) (v748 & ~v724);
	const char v947 = (char) (v777 & v723);
	const char v948 = (char) (~v817 | v813);
	const char v949 = (char) (~(v732 | v826));
	const char v950 = (char) (~v813 | v812);
	const char v951 = (char) ((char) 0);
	const char v952 = (char) (~v800 | v808);
	const char v953 = (char) (v806 & ~v702);
	const char v954 = (char) (~(v758 | v777));
	const char v955 = (char) (~v794);
	const char v956 = (char) (v811 | v728);
	const char v957 = (char) (~v733);
	const char v958 = (char) (~(v782 & v835));
	const char v959 = (char) (~(char) 0);
	const char v960 = (char) (~v707 | v749);
	const char v961 = (char) (~(v757 | v788));
	const char v962 = (char) (~(v741 & v816));
	const char v963 = (char) (v789);
	const char v964 = (char) (v731);
	const char v965 = (char) (v749 & v809);
	const char v966 = (char) (v783);
	const char v967 = (char) (v765 & ~v757);
	const char v968 = (char) (~(v805 & v761));
	const char v969 = (char) (~(v718 | v780));
	const char v970 = (char) (~v718);
	const char v971 = (char) (v776 & ~v717);
	const char v972 = (char) (v792);
	const char v973 = (char) (~v782);
	const char v974 = (char) (~v765 | v825);
	const char v975 = (char) (v771 & ~v715);
	const char v976 = (char) (v754 & v799);
	const char v977 = (char) ((char) 0);
	const char v978 = (char) (~v793 | v740);
	const char v979 = (char) (~v781);
	const char v980 = (char) (~v869 | v840);
	const char v981 = (char) (v863);
	const char v982 = (char) (~(v933 | v914));
	const char v983 = (char) ((char) 0);
	const char v984 = (char) (~(v876 ^ v968));
	const char v985 = (char) (~v859);
	const char v986 = (char) (v953 | v952);
	const char v987 = (char) (v901 | v862);
	const char v988 = (char) (v884 | v972);
	const char v989 = (char) (v913);
	const char v990 = (char) (v973 & ~v875);
	const char v991 = (char) (v894);
	const char v992 = (char) (~v887 | v885);
	const char v993 = (char) (~v861 | v872);
	const char v994 = (char) (~v958);
	const char v995 = (char) (~v929 | v912);
	const char v996 = (char) (v956);
	const char v997 = (char) (~v878);
	const char v998 = (char) (~v876);
	const char v999 = (char) (~v942);
	const char v1000 = (char) (v852 | v849);
	const char v1001 = (char) (v947 & v935);
	const char v1002 = (char) (v853);
	const char v1003 = (char) (~v891 | v845);
	const char v1004 = (char) (~(v932 & v917));
	const char v1005 = (char) (~v979 | v899);
	const char v1006 = (char) (v914);
	const char v1007 = (char) (v977 & ~v957);
	const char v1008 = (char) (v941 & v854);
	const char v1009 = (char) (v955 & ~v961);
	const char v1010 = (char) (~v971);
	const char v1011 = (char) (v848 & v963);
	const char v1012 = (char) (v864 & v974);
	const char v1013 = (char) (v926 ^ v860);
	const char v1014 = (char) (v877 & v890);
	const char v1015 = (char) (~v880 | v859);
	const char v1016 = (char) (~v889);
	const char v1017 = (char) (~(v866 & v946));
	const char v1018 = (char) (v896 & ~v868);
	const char v1019 = (char) (~(char) 0);
	const char v1020 = (char) (~v863 | v947);
	const char v1021 = (char) (~(v905 & v918));
	const char v1022 = (char) (~v951);
	const char v1023 = (char) (~v927 | v936);
	const char v1024 = (char) (v973);
	const char v1025 = (char) (~(v868 | v917));
	const char v1026 = (char) (v944);
	const char v1027 = (char) (~v908);
	const char v1028 = (char) (v969 & ~v865);
	const char v1029 = (char) (v851 | v962);
	const char v1030 = (char) (v842);
	const char v1031 = (char) (v939 & v893);
	const char v1032 = (char) (~(v919 ^ v962));
	const char v1033 = (char) (v897);
	const char v1034 = (char) (v931);
	const char v1035 = (char) (~v897 | v975);
	const char v1036 = (char) (v843);
	const char v1037 = (char) (~(char) 0);
	const char v1038 = (char) (v844 ^ v895);
	const char v1039 = (char) (v879);
	const char v1040 = (char) (~v950);
	const char v1041 = (char) (v915 & ~v879);
	const char v1042 = (char) (v966);
	const char v1043 = (char) (~v965 | v922);
	const char v1044 = (char) (~(v960 | v886));
	const char v1045 = (char) (~(char) 0);
	const char v1046 = (char) (~v923);
	const char v1047 = (char) (v840 ^ v976);
	const char v1048 = (char) (v936);
	const char v1049 = (char) (v911 | v927);
	const char v1050 = (char) (v948);
	const char v1051 = (char) (~(v906 | v928));
	const char v1052 = (char) (~(v895 | v930));
	const char v1053 = (char) (~v940);
	const char v1054 = (char) (v851 & v857);
	const char v1055 = (char) (v864);
	const char v1056 = (char) (~(char) 0);
	const char v1057 = (char) (v958 & ~v945);
	const char v1058 = (char) (~v934 | v921);
	const char v1059 = (char) (~v870);
	const char v1060 = (char) (v933 ^ v924);
	const char v1061 = (char) (~(v853 | v849));
	const char v1062 = (char) (v867);
	const char v1063 = (char) (~v922);
	const char v1064 = (char) (~v904 | v890);
	const char v1065 = (char) (~(v970 | v891));
	const char v1066 = (char) (v910 & v915);
	const char v1067 = (char) (~(v883 | v951));
	const char v1068 = (char) (~(v940 | v964));
	const char v1069 = (char) (~v979);
	const char v1070 = (char) (v929 ^ v871);
	const char v1071 = (char) (v938 & ~v872);
	const char v1072 = (char) (~v841);
	const char v1073 = (char) (~v888);
	const char v1074 = (char) (v871 | v846);
	const char v1075 = (char) (v855 & v889);
	const char v1076 = (char) (~(v916 | v907));
	const char v1077 = (char) (v925);
	const char v1078 = (char) (v925 ^ v948);
	const char v1079 = (char) (v896 & ~v920);
	const char v1080 = (char) (~(v950 | v865));
	const char v1081 = (char) (v842 & ~v883);
	const char v1082 = (char) (v884);
	const char v1083 = (char) (~v968 | v966);
	const char v1084 = (char) (~v913 | v955);
	const char v1085 = (char) (~(v886 | v898));
	const char v1086 = (char) (~v960);
	const char v1087 = (char) (~(v932 & v903));
	const char v1088 = (char) (v941 | v903);
	const char v1089 = (char) (~v878);
	const char v1090 = (char) ((char) 0);
	const char v1091 = (char) (~(v875 & v969));
	const char v1092 = (char) (~(char) 0);
	const char v1093 = (char) ((char) 0);
	const char v1094 = (char) (~v881 | v963);
	const char v1095 = (char) (~v847 | v854);
	const char v1096 = (char) (~(v945 ^ v919));
	const char v1097 = (char) (v916 & ~v902);
	const char v1098 = (char) (v874 | v954);
	const char v1099 = (char) ((char) 0);
	const char v1100 = (char) (v928 & v882);
	const char v1101 = (char) (v930 & ~v956);
	const char v1102 = (char) (~(v850 | v866));
	const char v1103 = (char) (~(v907 ^ v978));
	const char v1104 = (char) (~(v900 | v943));
	const char v1105 = (char) (v856);
	const char v1106 = (char) (~v975);
	const char v1107 = (char) (v873 & ~v855);
	const char v1108 = (char) (~(v926 | v970));
	const char v1109 = (char) (~(v877 & v949));
	const char v1110 = (char) (v852 | v972);
	const char v1111 = (char) (~v909);
	const char v1112 = (char) (~v965);
	const char v1113 = (char) (v924);
	const char v1114 = (char) (v885);
	const char v1115 = (char) (v946 & v847);
	const char v1116 = (char) (v959 ^ v954);
	const char v1117 = (char) (v882);
	const char v1118 = (char) (~v921);
	const char v1119 = (char) (~(v893 | v881));
	const char v1120 = (char) (v1059 & v997);
	const char v1121 = (char) (~v981);
	const char v1122 = (char) (v1071);
	const char v1123 = (char) (v1044);
	const char v1124 = (char) (v1062);
	const char v1125 = (char) (v1048 & ~v1067);
	const char v1126 = (char) (~v1033);
	const char v1127 = (char) (~v996 | v1083);
	const char v1128 = (char) (v1083);
	const char v1129 = (char) (~v1046 | v1047);
	const char v1130 = (char) (v1079);
	const char v1131 = (char) (~(v1010 & v1089));
	const char v1132 = (char) (v1095 & ~v986);
	const char v1133 = (char) (v1049);
	const char v1134 = (char) (v1078);
	const char v1135 = (char) (v1034);
	const char v1136 = (char) (v1075 & v1094);
	const char v1137 = (char) (~v1064 | v1119);
	const char v1138 = (char) (~v1009);
	const char v1139 = (char) (v1013);
	const char v1140 = (char) (~(v1003 ^ v1012));
	const char v1141 = (char) (v1118 & ~v993);
	const char v1142 = (char) (v1024);
	const char v1143 = (char) (~v1026);
	const char v1144 = (char) (~v1032 | v1111);
	const char v1145 = (char) (v1017 & ~v1116);
	const char v1146 = (char) (v1002 ^ v1076);
	const char v1147 = (char) (v1070 ^ v1112);
	const char v1148 = (char) (v1061);
	const char v1149 = (char) (v1006);
	const char v1150 = (char) (~v982 | v1080);
	const char v1151 = (char) (v1043);
	const char v1152 = (char) (~v1104);
	const char v1153 = (char) (v1108);
	const char v1154 = (char) (~v1057 | v1063);
	const char v1155 = (char) (~(v1032 & v1058));
	const char v1156 = (char) (v1102);
	const char v1157 = (char) (~v997);
	const char v1158 = (char) (~v1104);
	const char v1159 = (char) (~(v987 & v1052));
	const char v1160 = (char) (v1090 & ~v1095);
	const char v1161 = (char) (~v1069);
	const char v1162 = (char) (~v1023);
	const char v1163 = (char) (~(char) 0);
	const char v1164 = (char) (v1014 & ~v990);
	const char v1165 = (char) (~v1007 | v1092);
	const char v1166 = (char) (v983);
	const char v1167 = (char) (~(v1060 & v980));
	const char v1168 = (char) (~(v1054 ^ v1088));
	const char v1169 = (char) (v1098 & ~v1034);
	const char v1170 = (char) (v1091 ^ v1010);
	const char v1171 = (char) (v1064 | v1016);
	const char v1172 = (char) (v1040 & v985);
	const char v1173 = (char) (v1086);
	const char v1174 = (char) (v1006 & ~v1013);
	const char v1175 = (char) (v985 ^ v1031);
	const char v1176 = (char) (v1029);
	const char v1177 = (char) (~v1015);
	const char v1178 = (char) (~v1035);
	const char v1179 = (char) (~v1005 | v998);
	const char v1180 = (char) (v1119 & v1025);
	const char v1181 = (char) (v1063 & ~v981);
	const char v1182 = (char) (~(v1002 & v989));
	const char v1183 = (char) (v1094 | v1075);
	const char v1184 = (char) (~v1084);
	const char v1185 = (char) (v1082 & ~v1016);
	const char v1186 = (char) (v1008 & ~v1037);
	const char v1187 = (char) (v1040);
	const char v1188 = (char) (~v1003 | v1107);
	const char v1189 = (char) (~v1057);
	const char v1190 = (char) (v1077);
	const char v1191 = (char) (~(v1116 | v1093));
	const char v1192 = (char) (~(v996 | v999));
	const char v1193 = (char) (~v1015 | v1027);
	const char v1194 = (char) (~v998);
	const char v1195 = (char) (v1081 ^ v1106);
	const char v1196 = (char) (~(char) 0);
	const char v1197 = (char) (~(v1101 & v1099));
	const char v1198 = (char) (v1055);
	const char v1199 = (char) (~(v1056 & v1052));
	const char v1200 = (char) (v999 & ~v1037);
	const char v1201 = (char) (v995 ^ v984);
	const char v1202 = (char) (~v994);
	const char v1203 = (char) ((char) 0);
	const char v1204 = (char) (~v984);
	const char v1205 = (char) (v1105);
	const char v1206 = (char) (v1017 | v1041);
	const char v1207 = (char) (v994);
	const char v1208 = (char) (v1022 & ~v1027);
	const char v1209 = (char) (~v1062);
	const char v1210 = (char) (v1059 | v993);
	const char v1211 = (char) (v1020);
	const char v1212 = (char) (~v1114);
	const char v1213 = (char) (v1050 & ~v1004);
	const char v1214 = (char) (v1046 & v1086);
	const char v1215 = (char) (~(v1068 | v1030));
	const char v1216 = (char) (~v1077);
	const char v1217 = (char) (v1085 ^ v1097);
	const char v1218 = (char) (v1018);
	const char v1219 = (char) (v1058 | v1028);
	const char v1220 = (char) (~(v1038 ^ v1054));
	const char v1221 = (char) (v1039 & ~v1035);
	const char v1222 = (char) (~(v1071 | v1110));
	const char v1223 = (char) (~(v1036 ^ v1097));
	const char v1224 = (char) ((char) 0);
	const char v1225 = (char) (~v1000);
	const char v1226 = (char) (~v1091);
	const char v1227 = (char) (~v1087);
	const char v1228 = (char) ((char) 0);
	const char v1229 = (char) (~v1011);
	const char v1230 = (char) (~v1009 | v1036);
	const char v1231 = (char) (v1060 & v1042);
	const char v1232 = (char) (v1051 | v1021);
	const char v1233 = (char) (v1117);
	const char v1234 = (char) (v1106);
	const char v1235 = (char) (v988 | v988);
	const char v1236 = (char) (v1100 & ~v1029);
	const char v1237 = (char) (v1117);
	const char v1238 = (char) (~(v1096 ^ v1115));
	const char v1239 = (char) (~v1078 | v1093);
	const char v1240 = (char) (v1103 & ~v1068);
	const char v1241 = (char) (v1090 & ~v1050);
	const char v1242 = (char) (v1024 & ~v1019);
	const char v1243 = (char) (v1079 | v1114);
	const char v1244 = (char) (v1065);
	const char v1245 = (char) (v1047 & v1100);
	const char v1246 = (char) (~(v980 | v1038));
	const char v1247 = (char) (v1031 & v1008);
	const char v1248 = (char) (~v1087 | v1096);
	const char v1249 = (char) (v1021);
	const char v1250 = (char) (~(v995 | v1088));
	const char v1251 = (char) (~(v1074 & v1098));
	const char v1252 = (char) (v1073);
	const char v1253 = (char) (~v1103);
	const char v1254 = (char) (v1007 & ~v1019);
	const char v1255 = (char) (v1108 & v1065);
	const char v1256 = (char) (~(v1102 | v1118));
	const char v1257 = (char) (~(v991 & v1109));
	const char v1258 = (char) (v1023);
	const char v1259 = (char) (v1026 ^ v1014);
	const char v1260 = (char) (v1171 & ~v1238);
	const char v1261 = (char) (~v1198);
	const char v1262 = (char) (v1164);
	const char v1263 = (char) (~(v1205 ^ v1168));
	const char v1264 = (char) (v1227 | v1136);
	const char v1265 = (char) (~(v1206 & v1250));
	const char v1266 = (char) (v1144 | v1253);
	const char v1267 = (char) (v1229 & v1233);
	const char v1268 = (char) (v1134 & v1168);
	const char v1269 = (char) (v1153 & v1244);
	const char v1270 = (char) (v1181);
	const char v1271 = (char) (v1221);
	const char v1272 = (char) (v1243);
	const char v1273 = (char) (v1221 & ~v1127);
	const char v1274 = (char) (v1222 ^ v1230);
	const char v1275 = (char) (v1229 & ~v1252);
	const char v1276 = (char) (v1166);
	const char v1277 = (char) (~(v1255 | v1185));
	const char v1278 = (char) (v1198 ^ v1165);
	const char v1279 = (char) ((char) 0);
	const char v1280 = (char) (v1155 & ~v1210);
	const char v1281 = (char) (~v1159);
	const char v1282 = (char) (v1259 & ~v1211);
	const char v1283 = (char) (v1183 & ~v1190);
	const char v1284 = (char) (~(v1235 ^ v1192));
	const char v1285 = (char) (~v1177 | v1194);
	const char v1286 = (char) (v1176);
	const char v1287 = (char) (~v1210 | v1143);
	const char v1288 = (char) (v1232);
	const char v1289 = (char) (~v1124);
	const char v1290 = (char) (v1125 & v1238);
	const char v1291 = (char) (~v1174);
	const char v1292 = (char) (~v1199);
	const char v1293 = (char) (~v1219);
	const char v1294 = (char) (~(char) 0);
	const char v1295 = (char) (v1152 & ~v1182);
	const char v1296 = (char) (v1143 & v1129);
	const char v1297 = (char) (~v1203);
	const char v1298 = (char) (~v1182 | v1232);
	const char v1299 = (char) (v1151 & v1227);
	const char v1300 = (char) (v1128);
	const char v1301 = (char) (~v1208);
	const char v1302 = (char) (v1184 ^ v1259);
	const char v1303 = (char) (v1258 | v1124);
	const char v1304 = (char) (v1121);
	const char v1305 = (char) (~v1205);
	const char v1306 = (char) (~v1141);
	const char v1307 = (char) (~(v1154 ^ v1137));
	const char v1308 = (char) (v1122 & ~v1169);
	const char v1309 = (char) (~v1225);
	const char v1310 = (char) (~(v1175 | v1249));
	const char v1311 = (char) (v1147 ^ v1201);
	const char v1312 = (char) (~(v1236 | v1252));
	const char v1313 = (char) (v1158 ^ v1249);
	const char v1314 = (char) (v1144);
	const char v1315 = (char) (~v1212 | v1138);
	const char v1316 = (char) (v1187 & ~v1216);
	const char v1317 = (char) (~v1130 | v1206);
	const char v1318 = (char) (v1177 | v1220);
	const char v1319 = (char) (~(v1163 ^ v1217));
	const char v1320 = (char) (v1253 | v1256);
	const char v1321 = (char) (~v1237);
	const char v1322 = (char) (~v1240);
	const char v1323 = (char) (~(v1175 | v1241));
	const char v1324 = (char) (v1234 | v1127);
	const char v1325 = (char) (v1128 & ~v1136);
	const char v1326 = (char) (v1193);
	const char v1327 = (char) (~v1187 | v1166);
	const char v1328 = (char) (v1180 & ~v1231);
	const char v1329 = (char) (v1150);
	const char v1330 = (char) (~v1195 | v1169);
	const char v1331 = (char) (~v1145 | v1151);
	const char v1332 = (char) (~v1148);
	const char v1333 = (char) (v1241);
	const char v1334 = (char) (~(v1170 & v1120));
	const char v1335 = (char) (v1254 | v1178);
	const char v1336 = (char) (v1139 | v1176);
	const char v1337 = (char) (~v1204 | v1207);
	const char v1338 = (char) (v1150 ^ v1140);
	const char v1339 = (char) (~v1239);
	const char v1340 = (char) (v1147 ^ v1237);
	const char v1341 = (char) (v1130);
	const char v1342 = (char) (~v1197);
	const char v1343 = (char) (~(v1138 & v1200));
	const char v1344 = (char) (~v1234 | v1185);
	const char v1345 = (char) (~v1257 | v1256);
	const char v1346 = (char) (~(v1140 ^ v1129));
	const char v1347 = (char) (v1189 & ~v1220);
	const char v1348 = (char) (~v1188);
	const char v1349 = (char) (~v1135 | v1248);
	const char v1350 = (char) (~v1149);
	const char v1351 = (char) (v1139 | v1218);
	const char v1352 = (char) (~(v1246 & v1135));
	const char v1353 = (char) (v1190 & ~v1247);
	const char v1354 = (char) (v1173);
	const char v1355 = (char) (v1209);
	const char v1356 = (char) (~(v1149 | v1132));
	const char v1357 = (char) (v1224);
	const char v1358 = (char) (~(v1226 ^ v1181));
	const char v1359 = (char) (v1213);
	const char v1360 = (char) (~(v1183 & v1121));
	const char v1361 = (char) (v1250);
	const char v1362 = (char) (v1202 & ~v1215);
	const char v1363 = (char) (~(v1212 | v1189));
	const char v1364 = (char) (v1180 & ~v1228);
	const char v1365 = (char) (~(v1137 ^ v1131));
	const char v1366 = (char) (~v1126);
	const char v1367 = (char) (~(v1133 & v1179));
	const char v1368 = (char) (v1152 | v1148);
	const char v1369 = (char) (v1188 & ~v1134);
	const char v1370 = (char) (~v1146);
	const char v1371 = (char) (~(v1125 | v1172));
	const char v1372 = (char) (v1243 | v1159);
	const char v1373 = (char) (v1156);
	const char v1374 = (char) (v1155 | v1200);
	const char v1375 = (char) (~v1258);
	const char v1376 = (char) (~v1248);
	const char v1377 = (char) (v1228 & v1217);
	const char v1378 = (char) (v1164 & ~v1251);
	const char v1379 = (char) (v1208);
	const char v1380 = (char) (v1219);
	const char v1381 = (char) (v1223);
	const char v1382 = (char) (v1157 | v1179);
	const char v1383 = (char) (v1225);
	const char v1384 = (char) (v1174 & ~v1214);
	const char v1385 = (char) (~v1191);
	const char v1386 = (char) (~v1216);
	const char v1387 = (char) (v1257 & v1255);
	const char v1388 = (char) (v1202 | v1157);
	const char v1389 = (char) (v1141 & ~v1203);
	const char v1390 = (char) (v1211);
	const char v1391 = (char) (~v1245);
	const char v1392 = (char) (~v1197 | v1178);
	const char v1393 = (char) (~(v1122 | v1236));
	const char v1394 = (char) (v1209 | v1192);
	const char v1395 = (char) (~(v1142 & v1170));
	const char v1396 = (char) (~v1218);
	const char v1397 = (char) (~v1222);
	const char v1398 = (char) (~v1167 | v1156);
	const char v1399 = (char) (~(v1244 ^ v1199));
	const char v1400 = (char) (v1353);
	const char v1401 = (char) (v1265);
	const char v1402 = (char) (~(v1260 | v1347));
	const char v1403 = (char) (~(v1304 & v1289));
	const char v1404 = (char) (~(v1274 | v1381));
	const char v1405 = (char) (v1314);
	const char v1406 = (char) (~(v1306 & v1341));
	const char v1407 = (char) (~(v1371 & v1388));
	const char v1408 = (char) (~v1316 | v1310);
	const char v1409 = (char) (~(v1356 | v1272));
	const char v1410 = (char) (~(v1313 & v1372));
	const char v1411 = (char) (~(v1315 | v1325));
	const char v1412 = (char) (~v1365);
	const char v1413 = (char) (~v1384);
	const char v1414 = (char) (v1378 & v1268);
	const char v1415 = (char) (~(v1325 ^ v1360));
	const char v1416 = (char) (v1348 & ~v1330);
	const char v1417 = (char) (v1395 & ~v1349);
	const char v1418 = (char) (~(v1391 ^ v1327));
	const char v1419 = (char) (~v1282 | v1369);
	const char v1420 = (char) (~v1355 | v1309);
	const char v1421 = (char) (~v1318);
	const char v1422 = (char) (~v1397);
	const char v1423 = (char) (v1379 | v1346);
	const char v1424 = (char) (~(v1392 | v1279));
	const char v1425 = (char) (~v1266);
	const char v1426 = (char) (v1389 ^ v1385);
	const char v1427 = (char) (~v1358);
	const char v1428 = (char) (v1288 | v1367);
	const char v1429 = (char) (v1335 & ~v1302);
	const char v1430 = (char) (v1332);
	const char v1431 = (char) (v1267 & ~v1366);
	const char v1432 = (char) (v1262);
	const char v1433 = (char) (v1338 & ~v1373);
	const char v1434 = (char) (v1334 | v1337);
	const char v1435 = (char) (~v1264);
	const char v1436 = (char) (v1315 | v1383);
	const char v1437 = (char) (~(v1273 ^ v1372));
	const char v1438 = (char) (v1304 | v1342);
	const char v1439 = (char) (~v1321);
	const char v1440 = (char) (v1293);
	const char v1441 = (char) (~(v1322 | v1301));
	const char v1442 = (char) (v1271);
	const char v1443 = (char) (~(v1386 & v1391));
	const char v1444 = (char) (~v1289 | v1310);
	const char v1445 = (char) (v1270 ^ v1357);
	const char v1446 = (char) (~(v1299 & v1340));
	const char v1447 = (char) (~v1326);
	const char v1448 = (char) (~v1345);
	const char v1449 = (char) (~v1379 | v1399);
	const char v1450 = (char) (~v1398);
	const char v1451 = (char) (~v1291 | v1273);
	const char v1452 = (char) (~v1291 | v1316);
	const char v1453 = (char) (~v1381 | v1301);
	const char v1454 = (char) (~v1299);
	const char v1455 = (char) (~v1387 | v1280);
	const char v1456 = (char) (v1387);
	const char v1457 = (char) (v1295 ^ v1361);
	const char v1458 = (char) (v1366 & v1262);
	const char v1459 = (char) (v1340 & v1319);
	const char v1460 = (char) (v1374 & ~v1348);
	const char v1461 = (char) (v1393);
	const char v1462 = (char) (v1261 ^ v1339);
	const char v1463 = (char) (~v1324 | v1375);
	const char v1464 = (char) (~v1321);
	const char v1465 = (char) (~(v1328 & v1386));
	const char v1466 = (char) (v1269 & ~v1314);
	const char v1467 = (char) (~(v1344 ^ v1305));
	const char v1468 = (char) (v1336 & ~v1287);
	const char v1469 = (char) (v1375 ^ v1329);
	const char v1470 = (char) (v1388 & v1303);
	const char v1471 = (char) (~v1377 | v1360);
	const char v1472 = (char) (v1370 & ~v1283);
	const char v1473 = (char) (v1293 & ~v1380);
	const char v1474 = (char) (v1363 ^ v1384);
	const char v1475 = (char) (v1268 | v1338);
	const char v1476 = (char) (~v1359 | v1270);
	const char v1477 = (char) (~v1282 | v1362);
	const char v1478 = (char) (v1298);
	const char v1479 = (char) (v1290 & ~v1294);
	const char v1480 = (char) (v1367 & v1336);
	const char v1481 = (char) (~v1394);
	const char v1482 = (char) (~(v1323 | v1382));
	const char v1483 = (char) (v1271);
	const char v1484 = (char) (v1281);
	const char v1485 = (char) (v1359 & ~v1309);
	const char v1486 = (char) (~(v1322 | v1352));
	const char v1487 = (char) (v1277);
	const char v1488 = (char) (v1357);
	const char v1489 = (char) (~v1374);
	const char v1490 = (char) (~v1329);
	const char v1491 = (char) (v1354 | v1353);
	const char v1492 = (char) (v1334 & v1279);
	const char v1493 = (char) (~v1399);
	const char v1494 = (char) (v1342);
	const char v1495 = (char) (v1363 | v1398);
	const char v1496 = (char) (v1311);
	const char v1497 = (char) (~v1393);
	const char v1498 = (char) (~v1286);
	const char v1499 = (char) (v1361 & ~v1275);
	const char v1500 = (char) (~v1376 | v1350);
	const char v1501 = (char) (~v1397);
	const char v1502 = (char) (~v1371);
	const char v1503 = (char) (v1284 ^ v1284);
	const char v1504 = (char) (v1300);
	const char v1505 = (char) (~v1290);
	const char v1506 = (char) (~v1285 | v1277);
	const char v1507 = (char) (~v1276);
	const char v1508 = (char) (~v1330 | v1339);
	const char v1509 = (char) (~(v1396 & v1344));
	const char v1510 = (char) (~v1305);
	const char v1511 = (char) (~v1285);
	const char v1512 = (char) (~v1396 | v1380);
	const char v1513 = (char) (v1283 & v1352);
	const char v1514 = (char) (v1341 | v1355);
	const char v1515 = (char) (v1326);
	const char v1516 = (char) (v1373);
	const char v1517 = (char) (v1332);
	const char v1518 = (char) (v1327 ^ v1308);
	const char v1519 = (char) (v1266);
	const char v1520 = (char) (~(v1272 ^ v1298));
	const char v1521 = (char) (v1378);
	const char v1522 = (char) (~v1320 | v1263);
	const char v1523 = (char) (v1324 & v1347);
	const char v1524 = (char) (~(v1394 & v1349));
	const char v1525 = (char) (~(v1265 | v1287));
	const char v1526 = (char) (~(v1351 & v1313));
	const char v1527 = (char) (~(v1269 ^ v1317));
	const char v1528 = (char) (v1365 | v1286);
	const char v1529 = (char) (v1278 & ~v1356);
	const char v1530 = (char) (v1354);
	const char v1531 = (char) (~(v1307 ^ v1297));
	const char v1532 = (char) (~v1288 | v1296);
	const char v1533 = (char) (~v1395);
	const char v1534 = (char) (v1302 & ~v1294);
	const char v1535 = (char) (v1382 & v1306);
	const char v1536 = (char) (~v1312 | v1364);
	const char v1537 = (char) (v1292);
	const char v1538 = (char) (v1385 ^ v1303);
	const char v1539 = (char) (~(v1320 & v1392));
	const char v1540 = (char) (~v1423);
	const char v1541 = (char) (v1408);
	const char v1542 = (char) (v1428 ^ v1532);
	const char v1543 = (char) (v1528);
	const char v1544 = (char) (v1442);
	const char v1545 = (char) (~v1502 | v1448);
	const char v1546 = (char) (~(v1433 ^ v1408));
	const char v1547 = (char) (~(v1403 & v1467));
	const char v1548 = (char) (v1521);
	const char v1549 = (char) (v1429 ^ v1452);
	const char v1550 = (char) (v1444);
	const char v1551 = (char) (~v1440);
	const char v1552 = (char) (~v1473);
	const char v1553 = (char) (~v1463);
	const char v1554 = (char) (~(v1538 | v1421));
	const char v1555 = (char) (v1443);
	const char v1556 = (char) (v1477);
	const char v1557 = (char) (~v1476 | v1487);
	const char v1558 = (char) (v1468 & ~v1527);
	const char v1559 = (char) (~(v1474 & v1459));
	const char v1560 = (char) (v1443);
	const char v1561 = (char) (~v1406);
	const char v1562 = (char) (v1423);
	const char v1563 = (char) (v1523 | v1413);
	const char v1564 = (char) (v1525);
	const char v1565 = (char) (v1431 | v1504);
	const char v1566 = (char) (v1508);
	const char v1567 = (char) (v1517);
	const char v1568 = (char) (v1452 & ~v1500);
	const char v1569 = (char) (~(v1521 & v1475));
	const char v1570 = (char) (~v1499);
	const char v1571 = (char) (~(v1497 | v1523));
	const char v1572 = (char) (v1538 | v1409);
	const char v1573 = (char) (~v1536);
	const char v1574 = (char) (~v1491);
	const char v1575 = (char) (~v1509);
	const char v1576 = (char) (v1484);
	const char v1577 = (char) (v1400);
	const char v1578 = (char) (~v1436);
	const char v1579 = (char) (~v1424 | v1439);
	const char v1580 = (char) (v1435 ^ v1501);
	const char v1581 = (char) (~v1448);
	const char v1582 = (char) (~(v1485 & v1488));
	const char v1583 = (char) (~(v1514 & v1428));
	const char v1584 = (char) (~v1501);
	const char v1585 = (char) (v1425 ^ v1430);
	const char v1586 = (char) (~(v1436 & v1442));
	const char v1587 = (char) (v1504 & v1473);
	const char v1588 = (char) (~v1470);
	const char v1589 = (char) (v1421);
	const char v1590 = (char) (v1454 & v1407);
	const char v1591 = (char) (v1451 ^ v1427);
	const char v1592 = (char) (~(v1539 ^ v1514));
	const char v1593 = (char) (~v1465);
	const char v1594 = (char) (~v1529 | v1486);
	const char v1595 = (char) (~v1522);
	const char v1596 = (char) (~(v1524 ^ v1490));
	const char v1597 = (char) (~v1479);
	const char v1598 = (char) (~(v1531 ^ v1516));
	const char v1599 = (char) (v1418 ^ v1509);
	const char v1600 = (char) (~v1495);
	const char v1601 = (char) (v1479 & ~v1520);
	const char v1602 = (char) (v1460 | v1497);
	const char v1603 = (char) (v1457 & ~v1480);
	const char v1604 = (char) (v1437 | v1484);
	const char v1605 = (char) (v1507 & ~v1446);
	const char v1606 = (char) (v1522 & ~v1477);
	const char v1607 = (char) (v1400 & ~v1487);
	const char v1608 = (char) (~(v1416 & v1480));
	const char v1609 = (char) (~(v1490 ^ v1526));
	const char v1610 = (char) (v1419 ^ v1469);
	const char v1611 = (char) (~(v1465 & v1420));
	const char v1612 = (char) (~v1527 | v1438);
	const char v1613 = (char) (v1518);
	const char v1614 = (char) (v1467 & v1526);
	const char v1615 = (char) (~(v1417 | v1489));
	const char v1616 = (char) (v1445 & v1432);
	const char v1617 = (char) (v1472);
	const char v1618 = (char) (~(v1519 ^ v1454));
	const char v1619 = (char) (~v1409);
	const char v1620 = (char) (v1517);
	const char v1621 = (char) (v1437);
	const char v1622 = (char) (v1528);
	const char v1623 = (char) (~(v1449 ^ v1512));
	const char v1624 = (char) (v1476);
	const char v1625 = (char) (v1427 & ~v1495);
	const char v1626 = (char) (~v1531 | v1434);
	const char v1627 = (char) (v1524);
	const char v1628 = (char) (~(v1444 ^ v1462));
	const char v1629 = (char) (~v1405);
	const char v1630 = (char) (~v1537 | v1455);
	const char v1631 = (char) (~v1449);
	const char v1632 = (char) (v1518 & ~v1432);
	const char v1633 = (char) (v1420);
	const char v1634 = (char) (v1498 ^ v1463);
	const char v1635 = (char) (~v1533);
	const char v1636 = (char) (v1441 & ~v1403);
	const char v1637 = (char) (~(v1404 ^ v1510));
	const char v1638 = (char) (~v1530 | v1503);
	const char v1639 = (char) (~v1515);
	const char v1640 = (char) (~v1471 | v1451);
	const char v1641 = (char) (v1402 & ~v1439);
	const char v1642 = (char) (~v1450);
	const char v1643 = (char) (v1431 & v1433);
	const char v1644 = (char) (~(v1419 | v1478));
	const char v1645 = (char) (v1470);
	const char v1646 = (char) (v1483 & v1410);
	const char v1647 = (char) (v1520 & ~v1458);
	const char v1648 = (char) (~v1474);
	const char v1649 = (char) (~v1485);
	const char v1650 = (char) (v1456 & ~v1493);
	const char v1651 = (char) (~(v1515 | v1404));
	const char v1652 = (char) (v1512 ^ v1434);
	const char v1653 = (char) (~(v1464 | v1532));
	const char v1654 = (char) (~(v1502 & v1459));
	const char v1655 = (char) (v1478 & ~v1440);
	const char v1656 = (char) (~(v1486 | v1430));
	const char v1657 = (char) (v1519 | v1530);
	const char v1658 = (char) (~v1505 | v1461);
	const char v1659 = (char) (v1468);
	const char v1660 = (char) (v1407);
	const char v1661 = (char) (~v1535 | v1455);
	const char v1662 = (char) (~(v1498 ^ v1424));
	const char v1663 = (char) (v1447);
	const char v1664 = (char) (~(v1422 | v1494));
	const char v1665 = (char) (v1466);
	const char v1666 = (char) (v1426 & ~v1534);
	const char v1667 = (char) (v1445 ^ v1416);
	const char v1668 = (char) (v1529 & ~v1412);
	const char v1669 = (char) (v1496 & ~v1493);
	const char v1670 = (char) (v1525 & v1441);
	const char v1671 = (char) (~v1415);
	const char v1672 = (char) (~v1401);
	const char v1673 = (char) (v1496 | v1481);
	const char v1674 = (char) (~(v1405 & v1401));
	const char v1675 = (char) (~v1402 | v1534);
	const char v1676 = (char) (v1499);
	const char v1677 = (char) (v1458 & ~v1411);
	const char v1678 = (char) (v1466);
	const char v1679 = (char) (v1511 & v1435);
	out[0] = (char) (~v1554 | v1567);
	out[1] = (char) (~v1577 | v1649);
	out[2] = (char) (~(v1624 & v1549));
	out[3] = (char) (~v1607 | v1639);
	out[4] = (char) (v1628 ^ v1671);
	out[5] = (char) (v1674 ^ v1548);
	out[6] = (char) (~(v1628 ^ v1590));
	out[7] = (char) (~v1657 | v1626);
	out[8] = (char) (~(v1593 | v1650));
	out[9] = (char) (~v1606);
	out[10] = (char) (v1662);
	out[11] = (char) (v1619 & ~v1598);
	out[12] = (char) (~v1600);
	out[13] = (char) (~v1634);
	out[14] = (char) (v1556 ^ v1572);
	out[15] = (char) (v1617 & ~v1595);
	out[16] = (char) (v1624);
	out[17] = (char) (v1656);
	out[18] = (char) (~v1541);
	out[19] = (char) (~(v1582 ^ v1607));
	out[20] = (char) (~(v1669 & v1545));
	out[21] = (char) (v1568);
	out[22] = (char) (~v1651);
	out[23] = (char) (v1659 | v1606);
	out[24] = (char) (~v1667);
	out[25] = (char) (~v1589 | v1564);
	out[26] = (char) (~(v1588 ^ v1586));
	out[27] = (char) (v1622 | v1567);
	out[28] = (char) (~(v1611 & v1663));
	out[29] = (char) (~(v1632 ^ v1540));
	out[30] = (char) (~(v1558 | v1576));
	out[31] = (char) (v1643 | v1605);
	out[32] = (char) (v1678 & v1552);
	out[33] = (char) (~v1558);
	out[34] = (char) (~v1627);
	out[35] = (char) (~v1641);
	out[36] = (char) (~(v1620 | v1609));
	out[37] = (char) (v1562 & ~v1543);
	out[38] = (char) (v1647);
	out[39] = (char) (~(v1566 ^ v1657));
	out[40] = (char) (~(v1555 & v1574));
	out[41] = (char) (v1586 ^ v1570);
	out[42] = (char) (v1592);
	out[43] = (char) (v1555 ^ v1551);
	out[44] = (char) (~(v1651 ^ v1623));
	out[45] = (char) (v1625 ^ v1635);
	out[46] = (char) (v1652 | v1679);
	out[47] = (char) (v1547 | v1652);
	out[48] = (char) (~(v1640 ^ v1646));
	out[49] = (char) (~(v1641 & v1658));
	out[50] = (char) (~v1676);
	out[51] = (char) (v1565 & ~v1613);
	out[52] = (char) (~(v1580 ^ v1625));
	out[53] = (char) (v1575 ^ v1557);
	out[54] = (char) (v1618 | v1608);
	out[55] = (char) (v1604 | v1621);
	out[56] = (char) (v1636 ^ v1640);
	out[57] = (char) (v1653);
	out[58] = (char) (~v1675);
	out[59] = (char) (~v1626 | v1655);
	out[60] = (char) (~(v1595 | v1659));
	out[61] = (char) (v1618 ^ v1544);
	out[62] = (char) (~v1650);
	out[63] = (char) (~v1561);
	out[64] = (char) (v1580 & v1613);
	out[65] = (char) (~v1614 | v1602);
	out[66] = (char) (~v1658 | v1579);
	out[67] = (char) (~v1603);
	out[68] = (char) (~v1632 | v1661);
	out[69] = (char) (~v1615);
	out[70] = (char) (~(v1660 ^ v1631));
	out[71] = (char) (v1575 ^ v1570);
	out[72] = (char) (~v1584);
	out[73] = (char) (~v1579 | v1644);
	out[74] = (char) (v1559 & ~v1602);
	out[75] = (char) (~(v1648 | v1605));
	out[76] = (char) (v1678 | v1591);
	out[77] = (char) (v1671);
	out[78] = (char) (v1560 & ~v1594);
	out[79] = (char) (~v1620);
	out[80] = (char) (v1559 | v1601);
	out[81] = (char) (~(v1674 ^ v1645));
	out[82] = (char) (~(v1571 & v1633));
	out[83] = (char) (~v1569);
	out[84] = (char) (~(v1585 & v1656));
	out[85] = (char) (v1643 | v1654);
	out[86] = (char) (~(v1573 ^ v1610));
	out[87] = (char) (v1670 ^ v1642);
	out[88] = (char) (~(char) 0);
	out[89] = (char) (~v1542 | v1560);
	out[90] = (char) (v1556 & ~v1634);
	out[91] = (char) (~(v1629 ^ v1610));
	out[92] = (char) (~v1541);
	out[93] = (char) (v1655);
	out[94] = (char) (v1593 ^ v1646);
	out[95] = (char) (v1636 & v1582);
	out[96] = (char) (v1666 | v1596);
	out[97] = (char) (v1623 & ~v1642);
	out[98] = (char) (v1544);
	out[99] = (char) (v1600 & v1668);
	out[100] = (char) (~v1639 | v1553);
	out[101] = (char) (v1573 ^ v1578);
	out[102] = (char) (~v1587 | v1612);
	out[103] = (char) (~(v1592 & v1665));
	out[104] = (char) (~v1540 | v1571);
	out[105] = (char) (~v1550 | v1631);
	out[106] = (char) (v1663);
	out[107] = (char) (~v1621);
	out[108] = (char) (~v1645 | v1603);
	out[109] = (char) (~v1599 | v1661);
	out[110] = (char) (v1597 & ~v1563);
	out[111] = (char) (v1568);
	out[112] = (char) (v1583);
	out[113] = (char) (~v1630 | v1583);
	out[114] = (char) (~(v1653 | v1675));
	out[115] = (char) (~(v1563 | v1677));
	out[116] = (char) (v1644 ^ v1616);
	out[117] = (char) (~v1547);
	out[118] = (char) (v1672 & ~v1633);
	out[119] = (char) (v1576);
	out[120] = (char) (~v1548 | v1577);
	out[121] = (char) (v1561 | v1630);
	out[122] = (char) (~v1584 | v1564);
	out[123] = (char) (~v1637 | v1664);
	out[124] = (char) (v1572 | v1668);
	out[125] = (char) (v1552);
	out[126] = (char) (~v1673);
	out[127] = (char) (~(v1587 & v1670));
	out[128] = (char) (~v1599);
	out[129] = (char) (~v1647 | v1664);
	out[130] = (char) (~(v1581 | v1596));
	out[131] = (char) (v1672);
	out[132] = (char) (~v1551);
	out[133] = (char) (v1638 & ~v1619);
	out[134] = (char) (~(v1589 | v1569));
	out[135] = (char) (~(v1565 ^ v1590));
	out[136] = (char) (v1574);
	out[137] = (char) (~v1585);
	out[138] = (char) (~(v1557 | v1612));
	out[139] = (char) (v1662 & ~v1608);
}

// void apply_logic_gate_net (bool const *inp, int *out, size_t len) {
//     char *inp_temp = malloc(32*sizeof(char));
//     char *out_temp = malloc(140*sizeof(char));
//     char *out_temp_o = malloc(4*sizeof(char));
    
//     for(size_t i = 0; i < len; ++i) {
    
//         // Converting the bool array into a bitpacked array
//         for(size_t d = 0; d < 32; ++d) {
//             char res = (char) 0;
//             for(size_t b = 0; b < 8; ++b) {
//                 res <<= 1;
//                 res += !!(inp[i * 32 * 8 + (8 - b - 1) * 32 + d]);
//             }
//             inp_temp[d] = res;
//         }
    
//         // Applying the logic gate net
//         logic_gate_net(inp_temp, out_temp);
        
//         // GroupSum of the results via logic gate networks
//         for(size_t c = 0; c < 14; ++c) {  // for each class
//             // Initialize the output bits
//             for(size_t d = 0; d < 4; ++d) {
//                 out_temp_o[d] = (char) 0;
//             }
            
//             // Apply the adder logic gate network
//             for(size_t a = 0; a < 10; ++a) {
//                 char carry = out_temp[c * 10 + a];
//                 char out_temp_o_d;
//                 for(int d = 4 - 1; d >= 0; --d) {
//                     out_temp_o_d  = out_temp_o[d];
//                     out_temp_o[d] = carry ^ out_temp_o_d;
//                     carry         = carry & out_temp_o_d;
//                 }
//             }
            
//             // Unpack the result bits
//             for(size_t b = 0; b < 8; ++b) {
//                 const char bit_mask = (char) 1 << b;
//                 int res = 0;
//                 for(size_t d = 0; d < 4; ++d) {
//                     res <<= 1;
//                     res += !!(out_temp_o[d] & bit_mask);
//                 }
//                 out[(i * 8 + b) * 14 + c] = res;
//             }
//         }
//     }
//     free(inp_temp);
//     free(out_temp);
//     free(out_temp_o);
// }
void apply_logic_gate_net_singleval (char const *inp, int *out) {
    char *out_temp = (char*)malloc(140*sizeof(char));
    
    // for(size_t i = 0; i < len; ++i) {
    
        // Applying the logic gate net
        logic_gate_net(inp, out_temp);
				const int classSize = 140/10;

        for(size_t c = 0; c < 14; ++c) {  // for each class
					int classSum = 0;
					for(size_t node=c*classSize; node < (c*classSize) + classSize; node++) {
						classSum += out_temp[node] & 1; //take the lowest bit, ignore the rest	
					}
					out[c] = classSum;
				}
        
    // }
    free(out_temp);
}
