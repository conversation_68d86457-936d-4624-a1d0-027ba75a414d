uSEQ/.theia/*

.clj-kondo/
scripts/.nrepl-port
.DS_Store
.idea
*.log
tmp/

.cache/
build/
out/
# Let's leave this checked in for now,
# helps users skip a step when building the firmware
# LispLibrary.h

.vscode


interfaces/useqedit/__pycache__/Buffer.cpython-310.pyc

interfaces/useqedit/__pycache__/Cursor.cpython-310.pyc

interfaces/useqedit/__pycache__/MessageLog.cpython-310.pyc

*.pyc

interfaces/useqedit/__pycache__/

*.csv#

hardware/version0.2/kicad/useq-backups/

interfaces/virtual/cmake-build-debug/

interfaces/useqedit/useqedit.json

hardware/version0.2/fr4panel/fr4panel/fr4panel-backups/

hardware/version1.0/fr4panel/fr4panel/fr4panel-backups/

hardware/version1.0/fr4panel/fr4panel/jlcpcb/gerber/

hardware/version1.0/fr4panel/fr4panel/jlcpcb/production_files/

hardware/version1.0/fr4panel/fr4panel/jlcpcb/

hardware/version1.0/useq1/jlcpcb/

hardware/version1.0/useq1/useq-backups/

hardware/version1.0/useq1/useq1-backups/
