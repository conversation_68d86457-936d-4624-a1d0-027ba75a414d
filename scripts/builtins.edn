{;; LISP builtins
 "eval" {:args {:num "== 1"}
         :eval-args? false
         :body ["result = args[0].eval(env);"]
         :docstring "Evaluate a value as code"}

 ;; TODO @correctness should this check number of args?
 "list" {:body ["result = Value(args);"]

         :docstring "Create a list of values"}

 "vec" {:body ["result = Value::vector(args);"]
        :docstring "Create a list of values"}

 "lambda" {:eval-args? false
           :args {:num "== 2"
                  :type {0 ".is_vector()"}}
           :body "result = Value(args[0].as_vector(), args[1], env);"
           :docstring "Create a lambda function (SPECIAL FORM)"}

 "if_then_else" {:eval-args? false
                 :args {:num "== 3"}
                 :body ["if (args[0].eval(env).as_bool())"
                        "        result = args[1].eval(env);"
                        "    else"
                        "        result = args[2].eval(env);"]}

 "get_expr" {:eval-args? false
             :args {:num "== 1"
                    :type {0 ".is_symbol()"}}
             :body ["String name  = args[0].display();"
                    "std::optional<Value> expr = env.get_expr(name);"
                    "if (expr)"
                    "{"
                    "    result = expr.value();"
                    "}"
                    "else"
                    "{"
                    "    report_user_warning(\"(**get-expr**) Expression for \" + name + \" wasn't found.\");"
                    "}"]

             :docstring "Define a variable with a value (SPECIAL FORM)"}

 ;; FIXME unnecessary duplication,
 ;; we need to find a better way to map multiple
 ;; idents to the same function while keeping error messages correct
 "define" {:eval-args? false
           :args {:num "== 2"
                  :type {0 ".is_symbol()"}}
           :body ["String name  = args[0].display();"
                  "// NOTE: body is unevalled still"
                  "Value body = args[1];"
                  "env.set_expr(name, body);"
                  "env.set(name, body.eval(env));"
                  "result = Value::atom(name);"]

           :docstring "Define a variable with a value (SPECIAL FORM)"}

 "def" {:eval-args? false
        :args {:num "== 2"
               :type {0 ".is_symbol()"}}
        :body ["String name  = args[0].display();"
               "// NOTE: body is unevalled still"
               "Value body = args[1];"
               "env.set_expr(name, body);"
               "env.set(name, body.eval(env));"
               "result = Value::atom(name);"]

        :docstring "Define a variable with a value (SPECIAL FORM)"}

 "defs" {:eval-args? false
         :args {:num ">= 2"}
         :body ["if (args.size() % 2 != 0)"
                "{"
                "    report_custom_function_error("
                "        user_facing_name,"
                "        \"Number of arguments should be even, forming pairs of <name> <value>.\");"
                "}"

                "// iterate by twos and make sure all even args are symbols"
                "for (size_t i = 0; i < args.size() - 1; i += 2)"
                "{"
                "    if (!(args[i].is_symbol()))"
                "    {"
                "        report_error_wrong_specific_pred(user_facing_name, i + 1, \"a symbol \","
                "                                  args[i].to_lisp_src());"
                "        return Value::error();"
                "    }"
                "}"

                "std::vector<Value> names;"

                "for (size_t i = 0; i < args.size() - 1; i += 2)"
                "{"
                "    String name = args[i].display();"
                "    // NOTE: body is unevalled still"
                "    Value body = args[i + 1];"
                "    env.set_expr(name, body);"
                "    env.set(name, body.eval(env));"

                "    names.push_back(Value::atom(name));"
                "}"

                "result = Value::vector(names);"]

         :docstring "Define a variable with a value (SPECIAL FORM)"}

 "defun" {:eval-args? false
          :args {:num "== 3"
                 :type {0 ".is_symbol()"
                        1 ".is_sequential()"}}
          :body ["String f_name             = args[0].display();"
                 "std::vector<Value> params = args[1].as_sequential();"
                 "Value body                = args[2];"
                 "result                    = Value(params, body, env);"
                 "env.set(f_name, result);"]
          :docstring "Define a function with parameters and a result expression (SPECIAL FORM)"}

 "defn" {:eval-args? false
         :args {:num "== 3"
                :type {0 ".is_symbol()"
                       1 ".is_sequential()"}}
         :body ["String f_name             = args[0].display();"
                "std::vector<Value> params = args[1].as_sequential();"
                "Value body                = args[2];"
                "result                    = Value(params, body, env);"
                "env.set(f_name, result);"]
         :docstring "Define a function with parameters and a result expression (SPECIAL FORM)"}

 "while_loop" {:eval-args? false
               :args {:num ">= 1"}
               :body ["Value acc;"
                      "while (args[0].eval(env).as_bool())"
                      "{"
                      "    for (size_t i = 1; i < args.size() - 1; i++)"
                      "        args[i].eval(env);"
                      "    acc = args[args.size() - 1].eval(env);"
                      "}"
                      "result = acc;"]
               :docstring "Loop over a list of expressions with a condition (SPECIAL FORM)"}

 "for_loop" {:eval-args? false
             :args {:num ">= 2"
                    :type {0 ".is_symbol()"
                           1 ".is_list()"}}
             :body ["Value acc;"
                    "std::vector<Value> list = args[1].eval(env).as_list();"

                    "for (size_t i = 0; i < list.size(); i++)"
                    "{"
                    "    env.set(args[0].as_atom(), list[i]);"

                    "    for (size_t j = 1; j < args.size() - 1; j++)"
                    "        args[j].eval(env);"
                    "    acc = args[args.size() - 1].eval(env);"
                    "}"
                    "result = acc;"]
             :docstring "Iterate through a list of values in a list (SPECIAL FORM)"}

 "do_block" {:eval-args? false
             :body ["Value acc;"
                    "for (size_t i = 0; i < args.size(); i++)"
                    "    acc = args[i].eval(env);"
                    "result = acc;"]
             :docstring "Evaluate a block of expressions in the current environment (SPECIAL FORM)"}

 "let_block" {:eval-args? false
              :args {:num ">= 2"}
              :docstring "Evaluate a block of expressions in the current environment (SPECIAL FORM)"
              :body ["Value bindings = args[0];"
                     "// Make sure bindings arg is a valid vector/list"
                     "if (!(bindings.is_sequential()))"
                     "{"
                     "    report_error_wrong_specific_pred(user_facing_name, 1, \"a list or a vector\","
                     "                              args[0].to_lisp_src());"
                     "    return Value::error();"
                     "}"
                     ""
                     "std::vector<Value> bindings_vec = bindings.as_vector();"
                     ""
                     "if (bindings_vec.size() % 2 != 0)"
                     "{"
                     "    report_custom_function_error(user_facing_name,"
                     "                          \"Number of elements in the bindings vector should be \""
                     "                          \"even, forming pairs of <name> <value>.\");"
                     "    return Value::error();"
                     "}"
                     ""
                     "// iterate bindings by twos and make sure all even elements are symbols"
                     "for (size_t i = 0; i < bindings_vec.size() - 1; i += 2)"
                     "{"
                     "    Value item = bindings_vec[i];"
                     "    if (!(item.is_symbol()))"
                     "    {"
                     "        report_custom_function_error(user_facing_name,"
                     "                              \"All even elements in the bindings vector are \""
                     "                              \"expected to be symbols, but element #\" +"
                     "                                  String((i - 1) / 2) +"
                     "                                  \" is not: \" + item.to_lisp_src());"
                     "        return Value::error();"
                     "    }"
                     "}"
                     ""
                     "// Make local env to store local bindings, deferring to the"
                     "// linked parent scope for everything else"
                     "Environment local_env;"
                     "local_env.set_parent_scope(&env);"
                     ""
                     "// iterate bindings by twos and insert defs in local env"
                     "for (size_t i = 0; i < bindings_vec.size() - 1; i += 2)"
                     "{"
                     "    String name       = bindings_vec[i].str;"
                     "    Value body        = bindings_vec[i + 1];"
                     "    Value evaled_body = body.eval(local_env);"
                     "    if (evaled_body.is_error())"
                     "    {"
                     "        report_custom_function_error(user_facing_name,"
                     "                              \"The definition for entry #\" +"
                     "                                  String((i - 1) / 2) +"
                     "                                  \" evaluates to an error: \" + body.to_lisp_src());"
                     "        // println(\"- \" + body.display());"
                     "    }"
                     "    local_env.set(name, evaled_body);"
                     "}"
                     ""
                     "// Eval the remaining body exprs in that env"
                     "// NOTE: it starts at 1"
                     "for (int i = 1; i < args.size(); i++)"
                     "{"
                     "    Value expr = args[i];"
                     "    result     = expr.eval(local_env);"
                     "}"]}

 "scope" {:eval-args? false
          :body ["Environment e = env;"
                 "Value acc;"
                 "for (size_t i = 0; i < args.size(); i++)"
                 "    acc = args[i].eval(e);"
                 "result = acc;"]
          :docstring "Evaluate a block of expressions in a new environment (SPECIAL FORM)"}

 "quote" {:eval-args? false
          :body ["result = Value(args);"]
          :docstring "Quote an expression (SPECIAL FORM)"}

 ;; NOTE: duplication with "define"
 ;; this used to use set_global, but I'm not sure
 ;; there's ever a use case for the user to do that
 ;; Instead, it's used to set just the (static) value, not an expr
 "set" {:eval-args? false
        :args {:num "== 2"
               :type {0 ".is_symbol()"}}
        :body ["String name  = args[0].display();"
               "// NOTE: body is unevalled still"
               "Value val = args[1].eval(env);"
               "env.set(name, val);"
               "env.unset_expr(name);"
               "result = val;"]}

 "sum" {:args {:num ">= 1"
               :type {:all ".is_number()"}}
        :body ["Value acc = args[0];"
               ;; TODO @performance this could be used to short-circuit :all evaluation
               "for (size_t i = 1; i < args.size(); i++)"
               "{"
               "acc = acc + args[i];"
               "}"
               "result = acc;"]
        :docstring "Sum multiple values"}

 "subtract" {:args {:num "== 2"
                    :type {:all  ".is_number()"}}
             :body ["result = args[0] - args[1];"]
             :docstring "Subtract two values"}

 "product" {:args {:num ">= 1"
                   :type
                   {:all  ".is_number()"}}
            :body ["Value acc = args[0];"
                   "for (size_t i = 1; i < args.size(); i++)"
                   "{"
                   "    Value num = args[i];"
                   "    if (num == Value(0))"
                   "    {"
                   "        return Value(0);"
                   "    }"
                   "    else"
                   "    {"
                   "        acc = acc * args[i];"
                   "    }"
                   "}"
                   "result = acc;"]
            :docstring "Multiply several values"}

 "divide" {:args {:num "== 2"
                  :type
                  {:all  ".is_number()"}}
           :body ["result = args[0] / args[1];"]
           :docstring "Divide two values"}

 "remainder" {:args {:num "== 2"
                     :type
                     {:all  ".is_number()"}}
              :body ["result = args[0] % args[1];"]
              :docstring "Get the remainder of values"}

 "eq" {:args {:num "== 2"}
       :body ["result = Value(int (args [0] == args [1]));"]
       :docstring "Are two values equal?"}

 "neq" {:args {:num "== 2"}
        :body ["result = Value(int(args[0] != args[1]));"]
        :docstring "Are two values not equal?"}

 "greater" {:args {:num "== 2"}
            :body ["result = Value(int(args[0] > args[1]));"]
            :docstring "Is one number greater than another?"}

 "less" {:args {:num "== 2"}
         :body ["result = Value(int(args[0] < args[1]));"]
         :docstring "Is one number less than another?"}

 "greater_eq" {:args {:num "== 2"}
               :eval-args? true
               :body ["result = Value(int(args[0] >= args[1]));"]
               :docstring "Is one number greater than or equal to another?"}

 "less_eq" {:args {:num "== 2"}
            :eval-args? true
            :body ["result = Value(int(args[0] <= args[1]));"]
            :docstring "Is one number less than or equal to another?"}

 "get_type_name" {:args {:num "== 1"}
                  :eval-args? true
                  :body ["result = Value::string(args[0].get_type_name());"]
                  :docstring "Get the type name of a value"}

 "cast_to_float" {:args {:num "== 1"
                         :type {0 ".is_number()"}}
                  :eval-args? false
                  :body ["result = Value(args[0].as_float());"]
                  :docstring "Cast an item to a float"}

 "cast_to_int" {:args {:num "== 1"
                       :type {0 ".is_number()"}}
                :eval-args? false
                :body ["result = args[0].cast_to_int();"]
                :docstring "Cast an item to an int"}

 ;; NOTE: this is mostly manual for now
 "index" {:args {:num "== 2"
                 ;; NOTE: need to generate custom messages
                 :type {0 [".is_sequential()"
                           "!.is_empty()"]
                        1 ".is_number()"}}
          :body ["std::vector<Value> list = args[0].as_vector();"
                 "int i                   = args[1].as_int();"
                 "if (i < list.size())"
                 "{"
                 "    result = list[i];"
                 "}"
                 "else"
                 "{"
                 "    report_custom_function_error(\"index\", \"Index should be smaller than the size of the list.\");"
                 "    return Value::error();"
                 "}"]
          :docstring "Index a list"}

 "insert" {:args {:num "== 3"
                  :type {0 ".is_list()"
                         1 ".is_number()"}}
           :eval-args? true
           :body ["std::vector<Value> list = args[0].as_list();"
                  "int i                   = args[1].as_int();"
                  "if (i < list.size())"
                  "    Serial.println(INDEX_OUT_OF_RANGE);"
                  "else"
                  "    list.insert(list.begin() + args[1].as_int(), args[2].as_int());"
                  "result = Value(list);"]
           :docstring "Insert a value into a list"}

 "remove" {:args {:num "== 2"
                  :type {0 ".is_list()"
                         1 ".is_number()"}}
           :eval-args? true
           :body ["std::vector<Value> list = args[0].as_list();"
                  "    int i                   = args[1].as_int();"
                  "    if (list.empty() || i >= (int)list.size())"
                  "        report_error(INDEX_OUT_OF_RANGE);"
                  "    else"
                  "        list.erase(list.begin() + i);"
                  "result = Value(list);"]
           :docstring "Remove a value at an index from a list"}

 "len" {:args {:num "== 1"
               :type {0 ".is_sequential()"}}
        :eval-args? true
        :body ["result = Value((int)args[0].as_sequential().size());"]
        :docstring "Get the length of a list"}

 "push" {:args {:num "== 2"}
         :eval-args? true
         :body ["for (size_t i = 1; i < args.size(); i++)"
                "    args[0].push(args[i]);"
                "result = args[0];"]
         :docstring "Add an item to the end of a list"}

 "pop" {:args {:num "== 1"}
        :eval-args? true
        :body ["result = args[0].pop();"]
        :docstring "Remove an item from the end of a list"}

 "head" {:args {:num "== 1"
                :type {0 ".is_sequential()"}}
         :eval-args? true
         :body ["std::vector<Value> list = args[0].as_sequential();"
                "if (list.empty())"
                "{"
                "    report_error(INDEX_OUT_OF_RANGE);"
                "}"
                "else"
                "{"
                "    result = list[0];"
                "}"]
         :docstring "Get the first element of list"}

 ;; TODO @performance might be better to use stl to copy vec subset?
 "tail" {:args {:num "== 1"
                :type {0 ".is_sequential()"}}
         :eval-args? true
         :body ["std::vector<Value> content_list = args[0].as_sequential();"
                "std::vector<Value> result_list(content_list.begin() + 1, content_list.end());"    // Make sure we return same type
                "result = args[0].is_vector() ? Value::vector(result_list) : Value(result_list);"]
         :docstring "Get all elements after the first"}

 "ard_delay"
 {:args {:num "== 1"
         :type {0 ".is_number()"}}
  :body  ["int delaytime = args[0].as_int();"
          "delay(delaytime);"
          "result = args[0];"]}

 "ard_delaymicros"
 {:args {:num "== 1"
         :type {0 ".is_number()"}}
  :body ["int  delaytime = args[0].as_int();"
         "delayMicroseconds(delaytime);"
         "result = args[0];"]}

 "ard_millis"
 {:args {:num "== 0"}
  :body ["int m = millis();"
         "result = Value(m);"]}

 "ard_micros"
 {:args {:num "== 0"}
  :body ["int m = micros();"
         "result = Value(m);"]}

 "useq_pulse" {:args {:num "== 2"
                      :type {0 ".is_number()"
                             1 ".is_number()"}}
               :body ["// args: pulse width, phasor"
                      "double pulseWidth = args[0].as_float();"
                      "double phasor = args[1].as_float();"
                      "result = Value(phasor < pulseWidth ? 1.0 : 0.0);"]}

 "useq_sqr" {:args {:num "== 1"
                    :type {0 ".is_number()"}}
             :body ["result = Value(fmod(args[0].as_float(), 1.0) < 0.5 ? 1.0 : 0.0);"]}

 "ard_sin" {:args {:num "== 1"
                   :type {0 ".is_number()"}}
            :body ["result = Value(sin(args[0].as_float()));"]}

 ;; NOTE normalised
 "ard_usin" {:args {:num "== 1"
                    :type {0 ".is_number()"}}
             :body ["result = Value(0.5 + 0.5 * sin(args[0].as_float()));"]}

 "ard_cos" {:args {:num "== 1"
                   :type {0 ".is_number()"}}
            :body ["result = Value(cos(args[0].as_float()));"]}

 ;; NOTE normalised
 "ard_ucos" {:args {:num "== 1"
                    :type {0 ".is_number()"}}
             :body ["result = Value(0.5 + 0.5 * cos(args[0].as_float()));"]}

 "ard_tan" {:args {:num "== 1"
                   :type {0 ".is_number()"}}
            :body ["result = Value(tan(args[0].as_float()));"]}

 "ard_abs" {:args {:num "== 1"
                   :type {0 ".is_number()"}}
            :body ["result = Value(abs(args[0].as_float()));"]}

 "ard_min" {:args {:num "== 2"
                   :type {:all ".is_number()"}}
            :body ["result = Value(min(args[0].as_float(), args[1].as_float()));"]}

 "ard_max" {:args {:num "== 2"
                   :type {:all ".is_number()"}}
            :body ["result = Value(max(args[0].as_float(), args[1].as_float()));"]}

 "ard_pow" {:args {:num "== 2"
                   :type {:all ".is_number()"}}
            :body ["float exponent = args[0].as_float();"
                   "float base     = args[1].as_float();"
                   "result         = Value(pow(base, exponent));"]}

 "ard_sqrt" {:args {:num "== 1"
                    :type {0 ".is_number()"}}
             :body ["result = Value(sqrt(args[0].as_float()));"]}

 "ard_floor" {:args {:num "== 1"
                     :type {0 ".is_number()"}}
              :body ["result = Value(floor(args[0].as_float()));"]}

 "ard_ceil" {:args {:num "== 1"
                    :type {0 ".is_number()"}}
             :body ["result = Value(ceil(args[0].as_float()));"]}

 "zeros" {:args {:num "== 1"
                 :type {0 ".is_number()"}}
          :body ["int length = args[0].as_int();"
                 "std::vector<Value> zeroList(length, Value(0));"
                 "result = Value(zeroList);"]}

 "timeit" {:args {:num "== 1"}
           :body ["unsigned long ts = micros();"
                  "args[0].eval(env);"
                  "ts = micros() - ts;"
                  "result = Value(static_cast<int>(ts));"]}

 "ard_map" {:args {:num "== 5"}
            :type {:all ".is_number()"}
            :body ["float phasor = args.back().as_float();"
                   "float in_min, in_max, out_min, out_max;"
                   "if (args.size() == 5)"
                   "{"
                   "    in_min = args[0].as_float();"
                   "    in_max = args[1].as_float();"
                   "    out_min = args[2].as_float();"
                   "    out_max = args[3].as_float();"
                   "}"
                   "else"
                   "{"
                   "    in_min = 0.0;"
                   "    in_max = 1.0;"
                   "    out_min = args[0].as_float();"
                   "    out_max = args[1].as_float();"
                   "}"
                   "result = Value(scale_value(phasor, in_min, in_max, out_min, out_max));"]}

 "ard_lerp" {:args {:num "== 5"}
             :type {:all ".is_number()"}
             :body ["result = Value(lerp(args[0].as_float(), args[1].as_float(),args[0].as_float()));"]}

 "replace" {:args {:num "== 3"
                   :type {:all ".is_string()"}}
            :body ["String src = args[0].as_string();"
                   "src.replace(args[1].as_string(), args[2].as_string());"
                   "return Value::string(src);"]}

 "display" {:args {:num "== 1"}
            :body ["result = Value::string(args[0].display());"]}

 ;; TODO: what is the intended use case for this?
 "debug" {:args {:num "== 1"}
          :body ["result = Value::string(args[0].to_lisp_src());"]}

 "println" {:args {:num "== 1"}
            :body ["String s;"
                   "if (args[0].is_string())"
                   "{"
                   "    s = args[0].as_string();"
                   "}"
                   "else"
                   "{"
                   "    s = args[0].display();"
                   "}"
                   "Serial.println(s);"]}

 "print" {:args {:num "== 1"}
          :body ["String s;"
                 "if (args[0].is_string())"
                 "{"
                 "    s = args[0].as_string();"
                 "}"
                 "else"
                 "{"
                 "    s = args[0].display();"
                 "}"
                 "Serial.print(s);"]}

 "b_to_u" {:args {:num "== 1"
                  :type {0 ".is_number()"}}
           :body ["result = args[0].as_float() * 0.5 + 0.5;"]}

 "u_to_b" {:args {:num "== 1"
                  :type {0 ".is_number()"}}
           :body ["result = args[0].as_float() * 2 - 1;"]}

 ;; "debug" {:args {:num "== 1"}
 ;;          :body ["result = Value::string(args[0].debug());"]}

 ;;
 ;; "useq_perf" {:body ["report += env.get(\"fps\").as_float(); report += \", qt: \";" ;
 ;;                     "report += env.get(\"qt\").as_float(); report += \", heap free: \";"
 ;;                     "report += rp2040.getFreeHeap() / 1024;"       "Serial.println(report);"
 ;;                     "result = Value();"]}
 ;;
 ;;
 }
