# Document Title

1.0 -> 1.0.1
bb280b0..cc04555

# Fixes
- Fixed `pulse`'s width parameter being inverted.
- Fixed `pow` so that base is on the right
- Fixed `scale` to work with floats and take either 3 or 5 args
- added `lerp`
- added `random`
- added vector call syntax


- added `toggle_select` (?)

- fix `dm` args error checking
- fix `gatesw`, phasor on the right
- fix (re-introduce) `trigs`

- change IO analogWriteFreq from 80000 to 100000

- change `euclid`, remove some args and phasor on the right
- add `eu` TODO
- remove `looph`

- `len` now works with sequentials (currently, that means List & Vector) and not just with Lists.

# Additions
# Removals
# Changes
- Changed println to accept any object, not just strings
